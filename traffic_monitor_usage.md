# Android应用流量监控工具使用说明

## 📱 工具简介

这是一个专业的Android应用流量抓取采集工具，能够实时监控Android设备上指定应用的网络流量消耗情况，并生成详细的统计报告。

## 🛠️ 环境准备

### 1. 安装依赖
```bash
pip install -r requirements_traffic_monitor.txt
```

### 2. 安装ADB工具
- **Windows**: 下载并安装 [Android SDK Platform Tools](https://developer.android.com/studio/releases/platform-tools)
- **macOS**: 使用 Homebrew `brew install android-platform-tools`
- **Linux**: `sudo apt install android-tools-adb` (Ubuntu/Debian)

### 3. Android设备设置
1. 开启开发者选项：设置 → 关于手机 → 连续点击版本号7次
2. 开启USB调试：设置 → 开发者选项 → USB调试
3. 通过USB连接设备到电脑
4. 授权ADB调试权限

## 🚀 使用方法

### 基本使用

**完整版 (需要安装matplotlib):**
```bash
# 安装依赖
pip install -r requirements_traffic_monitor.txt
# 运行工具
python android_traffic_monitor.py
```

**简化版 (无第三方依赖):**
```bash
# 直接运行，无需安装任何依赖
python android_traffic_monitor_simple.py
```

推荐使用简化版，功能完整且无依赖冲突。

### 功能特点

#### 1. 🔍 自动设备发现
- 自动检测连接的Android设备
- 支持多设备选择
- 实时显示设备连接状态

#### 2. 📱 应用选择
- 自动获取已安装的第三方应用列表
- 显示应用名称和包名
- 支持交互式选择

#### 3. ⏱️ 实时监控
- 可自定义监控间隔（默认5秒）
- 实时显示流量变化
- 分别统计上传和下载流量

#### 4. 📊 数据分析
- 自动计算流量增量
- 显示平均速率
- 生成详细统计摘要

#### 5. 📈 报告生成
- 导出CSV格式的详细数据
- 生成流量趋势图表
- 支持多种可视化展示

## 📋 输出文件说明

### 1. 流量数据CSV文件
包含以下字段：
- `timestamp`: 时间戳
- `package_name`: 应用包名
- `app_name`: 应用名称
- `uid`: 应用UID
- `rx_bytes`: 接收字节数
- `tx_bytes`: 发送字节数
- `total_bytes`: 总字节数
- `rx_packets`: 接收包数
- `tx_packets`: 发送包数
- `total_packets`: 总包数

### 2. 流量趋势图表
- 上半部分：流量使用趋势（按时间间隔）
- 下半部分：累计流量使用情况
- 分别显示上传和下载流量

### 3. 日志文件
- 自动生成 `traffic_monitor.log`
- 记录详细的监控过程和错误信息

## 📊 监控示例

```
📱 Android应用流量监控工具
==================================================
✅ ADB连接成功，设备ID: emulator-5554

🔍 正在获取已安装的应用列表...
📱 找到 25 个第三方应用

📋 找到 25 个可监控的应用:
 1. 微信 (com.tencent.mm)
 2. 支付宝 (com.eg.android.AlipayGphone)
 3. 抖音 (com.ss.android.ugc.aweme)
 ...

请选择要监控的应用 (1-25): 1
请输入监控间隔(秒，默认5): 

🎯 已选择监控: 微信
⏱️  监控间隔: 5 秒
💡 按 Ctrl+C 停止监控

🚀 开始监控应用: com.tencent.mm
📊 微信 - 下载: 0.15MB, 上传: 0.03MB, 总计: 0.18MB
📊 微信 - 下载: 0.22MB, 上传: 0.05MB, 总计: 0.27MB
...
```

## 🎯 应用场景

### 1. 应用性能优化
- 分析应用流量消耗模式
- 识别异常流量使用
- 优化网络请求策略

### 2. 用户体验测试
- 监控应用在不同操作下的流量消耗
- 评估应用对用户流量的影响
- 制定流量优化方案

### 3. 成本控制
- 统计应用实际流量成本
- 分析流量使用趋势
- 预测流量需求

### 4. 竞品分析
- 对比不同应用的流量使用效率
- 分析竞品网络策略
- 制定差异化策略

## ⚠️ 注意事项

1. **设备权限**: 确保设备已开启USB调试并授权ADB访问
2. **网络环境**: 建议在稳定的网络环境下进行监控
3. **应用状态**: 确保被监控的应用处于活跃状态
4. **监控时长**: 建议监控时长不少于5分钟以获得有效数据
5. **数据精度**: 流量统计基于系统API，可能存在轻微延迟

## 🔧 故障排除

### 1. ADB连接失败
```bash
# 检查ADB是否正确安装
adb version

# 重启ADB服务
adb kill-server
adb start-server

# 检查设备连接
adb devices
```

### 2. 应用列表为空
- 确保设备已解锁并授权USB调试
- 检查设备是否安装了第三方应用
- 尝试重新连接设备

### 3. 流量数据为0
- 确保被监控应用正在使用网络
- 检查应用是否有网络权限
- 尝试使用不同的应用进行测试

## 📞 技术支持

如有问题或建议，请联系开发团队或查看工具日志文件以获取详细错误信息。 