#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os
import sys
import subprocess
import time
import logging
import datetime
import platform
import psutil
import signal
import threading
import atexit
import argparse  # 添加导入argparse

# 解析命令行参数
def parse_arguments():
    parser = argparse.ArgumentParser(description='启动自动化脚本服务')
    parser.add_argument('--test-mode', action='store_true', 
                        help='以测试模式启动自动化脚本')
    parser.add_argument('--keep-running', action='store_true', 
                        help='保持主进程运行，不清理子进程')
    return parser.parse_args()

# 配置日志
def setup_logging():
    log_dir = os.path.join(os.getcwd(), 'logs')
    os.makedirs(log_dir, exist_ok=True)
    log_file = os.path.join(log_dir, f'start_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )
    return log_file

# 检测操作系统
IS_WINDOWS = platform.system() == "Windows"
PYTHON_CMD = "python" if IS_WINDOWS else "python3"

# 要启动的脚本列表
SCRIPTS = [
    {
        "name": "dingtalk_stream_server.py",
        "description": "钉钉Stream服务器",
        "priority": 1, # 优先启动的脚本
        "process": None,
        "required": False # 非必须，失败不影响其他脚本
    },
    {
        "name": "keepalive.py",
        "description": "保活脚本",
        "priority": 2,
        "process": None,
        "required": True
    },
    {
        "name": "tk_chat_automation.py",
        "description": "自动化脚本",
        "priority": 3,
        "process": None,
        "required": False # 由保活脚本负责维护，所以这里标记为非必须
    }
]

# 存储子进程的列表，用于退出时清理
processes = []

# 终止进程及其子进程
def terminate_process_tree(process):
    if IS_WINDOWS:
        subprocess.call(['taskkill', '/F', '/T', '/PID', str(process.pid)])
    else:
        try:
            parent = psutil.Process(process.pid)
            for child in parent.children(recursive=True):
                child.terminate()
            parent.terminate()
        except:
            process.terminate()

# 检查进程是否在运行
def is_process_running(script_name):
    """检查指定名称的进程是否在运行"""
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['cmdline']:
                cmd_line = ' '.join(proc.info['cmdline'])
                if 'python' in cmd_line and script_name in cmd_line:
                    return True
        except:
            pass
    return False

# 启动指定脚本
def start_script(script, test_mode=False):
    """启动单个脚本"""
    script_path = os.path.join(os.getcwd(), script["name"])
    logging.info(f"正在启动 {script['description']}...")
    
    # 检查脚本文件是否存在
    if not os.path.exists(script_path):
        logging.error(f"脚本文件不存在: {script_path}")
        return None
    
    try:
        # 构建启动命令
        cmd = [PYTHON_CMD, script["name"]]
        
        # 如果是自动化脚本且启用了测试模式，添加参数
        if script["name"] == "tk_chat_automation.py" and test_mode:
            cmd.append("--test-mode")
        
        # Windows系统下使用不同的方法启动
        if IS_WINDOWS:
            # 如果是钉钉Stream服务器，使用可见的控制台窗口
            if script["name"] == "dingtalk_stream_server.py":
                process = subprocess.Popen(
                    cmd,
                    cwd=os.getcwd(),
                    creationflags=subprocess.CREATE_NEW_CONSOLE
                )
            else:
                # 其他脚本使用pythonw启动，隐藏控制台窗口
                python_path = sys.executable
                pythonw_path = python_path.replace('python.exe', 'pythonw.exe')
                
                if os.path.exists(pythonw_path) and script["name"] != "dingtalk_stream_server.py":
                    cmd[0] = pythonw_path
                
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = 0  # 0表示隐藏窗口
                
                process = subprocess.Popen(
                    cmd,
                    cwd=os.getcwd(),
                    startupinfo=startupinfo,
                    creationflags=subprocess.CREATE_NO_WINDOW
                )
        else:
            # Unix系统使用nohup启动，允许在终端关闭后继续运行
            if script["name"] == "dingtalk_stream_server.py":
                # 钉钉Stream服务器使用nohup后台启动，确保在主进程退出后继续运行
                log_file = f"{script['name'].replace('.py', '')}.log"
                script_cmd = ' '.join(cmd)
                with open(log_file, "w") as f:
                    process = subprocess.Popen(
                        f"nohup {script_cmd} > {log_file} 2>&1 &",
                        cwd=os.getcwd(),
                        shell=True,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE
                    )
            else:
                # 其他脚本使用nohup后台运行
                # 为测试模式添加参数
                script_cmd = ' '.join(cmd)
                log_file = f"{script['name'].replace('.py', '')}.log"
                with open(log_file, "w") as f:
                    process = subprocess.Popen(
                        f"nohup {script_cmd} > {log_file} 2>&1 &",
                        cwd=os.getcwd(),
                        shell=True,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE
                    )
        
        logging.info(f"{script['description']}已启动，PID: {process.pid}")
        return process
    except Exception as e:
        logging.error(f"启动 {script['description']} 失败: {str(e)}")
        return None

# 主函数
def main():
    # 解析命令行参数
    args = parse_arguments()
    test_mode = args.test_mode
    keep_running = args.keep_running
    
    # 设置日志
    log_file = setup_logging()
    logging.info("启动脚本开始执行...")
    logging.info(f"操作系统: {platform.system()}, Python命令: {PYTHON_CMD}")
    
    if test_mode:
        logging.info("已启用测试模式，自动化脚本将以测试模式运行")
    
    # 注册退出清理函数，但仅在Windows系统或明确指定keep_running=False时使用
    if IS_WINDOWS and not keep_running:
        def cleanup():
            logging.info("正在清理所有子进程...")
            for script in SCRIPTS:
                if script["process"] and script["process"].poll() is None:  # 检查进程是否还在运行
                    try:
                        logging.info(f"正在终止 {script['description']}...")
                        terminate_process_tree(script["process"])
                    except Exception as e:
                        logging.error(f"终止 {script['description']} 时出错: {str(e)}")
            
            logging.info("所有子进程已清理")
        
        atexit.register(cleanup)
    else:
        logging.info("不注册清理函数，子进程将在后台继续运行")
    
    # 按优先级排序脚本
    sorted_scripts = sorted(SCRIPTS, key=lambda x: x["priority"])
    
    # 启动所有脚本
    for script in sorted_scripts:
        if is_process_running(script["name"]):
            logging.warning(f"{script['description']}已在运行中，跳过启动")
            continue
            
        script["process"] = start_script(script, test_mode)
        if script["process"] is None and script["required"]:
            logging.error(f"启动必需的{script['description']}失败，终止启动过程")
            # 清理已启动的进程，但只在Windows系统且不是keep_running模式时
            if IS_WINDOWS and not keep_running:
                for s in sorted_scripts:
                    if s["process"] and s["process"].poll() is None:
                        terminate_process_tree(s["process"])
            sys.exit(1)
        
        # 等待进程启动并稳定
        time.sleep(3)
    
    logging.info("所有脚本已启动")
    
    # 在Windows系统下，或者指定了keep_running参数时，保持主进程运行
    if IS_WINDOWS or keep_running:
        try:
            logging.info("主进程将保持运行，监控子进程状态...")
            while True:
                # 检查所有必需的子进程状态
                all_required_running = True
                for script in sorted_scripts:
                    if script["required"] and script["process"]:
                        if script["process"].poll() is not None:  # 进程已退出
                            all_required_running = False
                            logging.error(f"必需的{script['description']}已退出，退出码: {script['process'].returncode}")
                            
                # 如果任何必需的进程退出，则退出主进程
                if not all_required_running:
                    logging.error("由于必需的子进程已退出，正在终止所有进程...")
                    # 只在Windows系统且不是keep_running模式时清理子进程
                    if IS_WINDOWS and not keep_running:
                        for s in sorted_scripts:
                            if s["process"] and s["process"].poll() is None:
                                terminate_process_tree(s["process"])
                    sys.exit(1)
                
                # 定期检查
                time.sleep(30)
        except KeyboardInterrupt:
            logging.info("启动脚本被用户中断")
            # 只在Windows系统且不是keep_running模式时清理子进程
            if IS_WINDOWS and not keep_running:
                for s in sorted_scripts:
                    if s["process"] and s["process"].poll() is None:
                        terminate_process_tree(s["process"])
    else:
        # 在Unix系统下，我们打印一条消息并退出，子进程会在后台继续运行
        logging.info("启动完成，所有服务将在后台继续运行")
        
        # 创建一个提示文件，说明如何查看各个脚本的日志
        try:
            with open("服务运行说明.txt", "w", encoding="utf-8") as f:
                f.write("# 太古聊天机器人自动化服务已启动\n\n")
                f.write("以下服务正在运行：\n\n")
                
                for script in sorted_scripts:
                    if script["process"]:
                        f.write(f"- {script['description']} ({script['name']})\n")
                
                f.write("\n要查看各服务的日志，请在终端中运行以下命令：\n\n")
                f.write(f"- 钉钉Stream服务日志: tail -f logs/dingtalk_stream_*.log\n")
                f.write(f"- 保活脚本日志: tail -f logs/keepalive_*.log\n")
                f.write(f"- 自动化脚本日志: tail -f logs/chat_log_*.log\n\n")
                
                f.write("如需重启服务，请运行：\n\n")
                f.write("1. 停止所有服务: pkill -f 'python.*tk_chat_automation.py|python.*keepalive.py|python.*dingtalk_stream_server.py'\n")
                f.write("2. 重新启动: python3 start.py\n\n")
                
                f.write(f"最后更新时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            logging.info("已创建服务运行说明文件: 服务运行说明.txt")
        except Exception as e:
            logging.error(f"创建服务运行说明文件失败: {str(e)}")

if __name__ == "__main__":
    main() 