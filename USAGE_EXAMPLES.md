# Chrome批量启动器使用示例

## 快速开始

### 1. 基本使用（推荐）

```bash
# 使用移动设备模拟版本（推荐）
python3 batch_chrome_simple_mobile.py

# 使用小测试文件（推荐用于测试）
python3 batch_chrome_simple_mobile.py test_urls_small.txt

# 使用完整URL文件
python3 batch_chrome_simple_mobile.py test_urls_pre.txt
```

### 2. 指定移动设备类型

```bash
# 使用iPad Pro模拟
python3 batch_chrome_simple_mobile.py test_urls_small.txt "iPad Pro"

# 使用iPhone 12模拟
python3 batch_chrome_simple_mobile.py test_urls_small.txt "iPhone 12"

# 使用Samsung Galaxy S21模拟
python3 batch_chrome_simple_mobile.py test_urls_small.txt "Samsung Galaxy S21"
```

### 3. 使用Bash版本（传统方式）

```bash
# 启动Bash版本（不支持移动设备模拟）
./batch_chrome_launcher.sh
```

## 移动设备模拟效果

### iPhone 13 Pro（默认）
- **视口大小**: 390x844
- **设备缩放**: 3x
- **User-Agent**: iPhone iOS 15.0
- **触摸事件**: 已启用

### iPad Pro
- **视口大小**: 1024x1366  
- **设备缩放**: 2x
- **User-Agent**: iPad iOS 15.0
- **触摸事件**: 已启用

### Samsung Galaxy S21
- **视口大小**: 360x800
- **设备缩放**: 3x
- **User-Agent**: Android 11 Chrome
- **触摸事件**: 已启用

## 操作流程示例

1. **启动脚本**
   ```bash
   python3 batch_chrome_simple_mobile.py test_urls_small.txt "iPhone 13 Pro"
   ```

2. **观察输出**
   ```
   找到 3 个URL，开始启动Chrome浏览器...
   模拟设备: iPhone 13 Pro
   设备配置: 390x844 @ 3x
   启动浏览器 1/3: https://uat-tkmates.swireproperties.com.cn/...
   启动浏览器 2/3: https://uat-tkmates.swireproperties.com.cn/...
   启动浏览器 3/3: https://uat-tkmates.swireproperties.com.cn/...
   
   成功启动了 3 个Chrome浏览器进程
   ```

3. **在浏览器中操作**
   - 每个浏览器窗口都使用移动设备User-Agent
   - 窗口大小模拟移动设备屏幕尺寸
   - 窗口会自动排列，避免重叠
   - **重要**: 为获得完整的移动设备模拟效果，请在每个浏览器中：
     - 按 `F12` 打开开发者工具
     - 按 `Ctrl+Shift+M` (macOS: `Cmd+Shift+M`) 切换到设备模拟模式
     - 或点击开发者工具中的设备图标 📱

4. **查看状态**
   ```
   请输入命令 (输入 'quit' 关闭所有进程): status
   
   进程状态检查:
   ✓ 运行中 - PID 12345: URL 1: https://...
   ✓ 运行中 - PID 12346: URL 2: https://...
   ✓ 运行中 - PID 12347: URL 3: https://...
   总计: 3 个进程，3 个正在运行
   ```

5. **关闭所有浏览器**
   ```
   请输入命令 (输入 'quit' 关闭所有进程): quit
   
   开始关闭 3 个Chrome进程...
   关闭进程 PID 12345...
   关闭进程 PID 12346...
   关闭进程 PID 12347...
   成功关闭了 3 个进程
   程序结束
   ```

## 分批处理特性

### 自动关闭上一批

为了节省系统资源，脚本在启动下一批浏览器时会**自动关闭上一批**：

```bash
=== 启动第 2/5 批 ===
关闭上一批的 3 个浏览器...
✓ 已关闭上一批的 3 个浏览器
URL范围: 4-6
启动浏览器 4/15: https://example4.com
启动浏览器 5/15: https://example5.com
启动浏览器 6/15: https://example6.com

✓ 第 2/5 批启动完成
本批启动了 3 个浏览器
当前运行 3 个浏览器 (已自动关闭上一批)
```

### 批次管理优势

- **节省内存**: 同时只运行一批浏览器，避免系统卡顿
- **提高效率**: 专注处理当前批次，减少干扰
- **自动化**: 无需手动关闭，一键切换到下一批
- **灵活控制**: 可随时查看状态或退出程序

## 高级用法

### 自动启用移动设备模拟

如果你想自动为所有浏览器启用完整的移动设备模拟模式：

```bash
# 启动浏览器后，运行自动化脚本
python3 auto_enable_mobile_mode.py
```

这个脚本会自动：
- 打开开发者工具 (F12)
- 切换到设备模拟模式 (Ctrl+Shift+M)

**注意**: 目前支持macOS和Linux系统的自动化。

### 自定义URL文件格式

创建你自己的URL文件：

```bash
# my_urls.txt
# 这是注释行，会被忽略
https://example1.com
https://example2.com

# 空行也会被忽略
https://example3.com
```

然后使用：
```bash
python3 batch_chrome_simple_mobile.py my_urls.txt "iPad Pro"
```

### 窗口排列

脚本会自动排列浏览器窗口：
- 每行最多4个窗口
- 窗口间距400像素
- 行间距300像素
- 起始位置：(50, 50)

### 进程管理

- 每个URL使用独立的Chrome进程
- 独立的用户数据目录（避免会话冲突）
- 支持优雅关闭和强制关闭
- 自动清理临时文件

## 故障排除

### 常见问题

1. **Chrome未找到**
   ```bash
   # 检查Chrome安装
   ./install_dependencies.sh
   ```

2. **进程启动失败**
   - 检查URL格式是否正确
   - 确认网络连接正常
   - 检查系统资源是否充足

3. **窗口重叠**
   - 脚本会自动排列窗口
   - 如果屏幕太小，可能会有重叠
   - 可以手动调整窗口位置

### 性能建议

- **小批量测试**: 先用`test_urls_small.txt`测试
- **资源监控**: 每个Chrome进程约占用100-200MB内存
- **网络带宽**: 同时加载多个页面需要足够带宽
- **系统性能**: 建议不超过20个并发浏览器进程
