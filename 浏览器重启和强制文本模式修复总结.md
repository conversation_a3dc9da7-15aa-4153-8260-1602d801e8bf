# 浏览器重启和强制文本模式修复总结

## 📋 问题概述

本次修复解决了两个主要问题：
1. 浏览器crash后没有正确重启，导致脚本陷入无限重试循环
2. 强制文本模式切换逻辑在第一次对话后立即执行，但等待时间不足，导致切换失败

## 🛠️ 修复内容

### 1. 浏览器crash检测和重启修复

#### 主要改进：
- **直接检测浏览器crash异常**：在主异常处理块中直接检测`TargetClosedError`异常
- **立即重启浏览器**：检测到浏览器crash时立即调用`restart_browser_if_needed`
- **优雅关闭旧实例**：在强制清理前先尝试优雅关闭旧浏览器实例
- **避免重试计数**：浏览器重启成功后不计入重试次数

#### 关键代码改进：

```python
# 检查是否是浏览器crash异常
is_browser_crash = False

# 检查异常消息
if ("Target page, context or browser has been closed" in str(e) or 
    "TargetClosedError" in str(e)):
    is_browser_crash = True
else:
    # 尝试检测浏览器状态
    try:
        if not is_browser_alive(page, context, page):
            is_browser_crash = True
    except:
        # 如果连状态检测都失败了，很可能是浏览器crash
        is_browser_crash = True

if is_browser_crash:
    logging.warning("🚨 检测到浏览器crash异常，准备重启浏览器...")
    # 直接触发浏览器重启
    browser, context, page, playwright = restart_browser_if_needed(browser, context, page)
    # 重新导航并重启聊天会话
    # ...
```

### 2. 强制文本模式切换优化

#### 主要改进：
- **增加等待时间**：从2秒增加到5秒，确保页面完全加载
- **重试机制**：增加最多10次重试，每次间隔3秒
- **改进选择器**：扩展文本模式按钮的选择器列表
- **统一locator方法**：使用更可靠的Playwright locator API
- **详细调试信息**：找不到按钮时提供详细的页面元素信息
- **截图记录**：记录切换前后的页面状态

#### 关键代码改进：

```python
# 等待页面充分稳定，确保所有UI元素都已加载
logging.info("等待页面充分加载...")
time.sleep(5)

# 多次尝试等待文本模式按钮出现
max_wait_attempts = 10
text_mode_success = False

for attempt in range(max_wait_attempts):
    logging.info(f"第{attempt + 1}次尝试切换到文本模式...")
    
    # 执行文本模式切换
    text_mode_success = switch_to_text_mode(page, screenshots_dir)
    
    if text_mode_success:
        logging.info("✅ 强制文本模式切换成功")
        break
    else:
        if attempt < max_wait_attempts - 1:
            logging.info(f"文本模式按钮暂未出现，等待3秒后重试...")
            time.sleep(3)
        else:
            logging.warning("⚠️ 强制文本模式切换失败，继续使用当前模式")
```

#### 扩展的文本模式按钮选择器：

```python
text_mode_selectors = [
    'button:has(img[alt="文本"])',  # 优先使用复合选择器
    'button img[alt="文本"]',
    'img[alt="文本"]',
    'img[src*="text_model"]',
    'img[src*="text_model.66434ac9.png"]',
    'button[title*="文本"]',  # 新增title属性选择器
    'button[aria-label*="文本"]',  # 新增aria-label选择器
    '[data-testid*="text"]',  # 新增测试ID选择器
    'button:has-text("文本")',  # 新增文本内容选择器
]
```

### 3. 调试和监控改进

#### 详细调试信息：
- **页面元素检查**：找不到按钮时检查页面上所有按钮和图片
- **状态截图**：记录切换前后的页面状态
- **错误日志**：提供更详细的错误信息和堆栈跟踪

#### 截图记录：
- `before_text_mode_switch.png` - 切换前页面状态
- `after_text_mode_switch.png` - 切换后页面状态
- `text_mode_button_not_found.png` - 找不到按钮时的页面状态

## 🧪 测试建议

### 基础测试
```bash
# 测试强制文本模式功能
python3 tk_chat_automation.py --force-text-mode --test-mode

# 测试浏览器重启功能（需要手动关闭浏览器进程来模拟crash）
python3 tk_chat_automation.py --test-mode
```

### 压力测试
```bash
# 长时间运行测试浏览器稳定性
python3 tk_chat_automation.py --force-text-mode --questions-file questions.json
```

## 🔍 关键验证点

### 浏览器重启验证：
1. ✅ 检测到`TargetClosedError`异常时立即重启浏览器
2. ✅ 重启成功后正确更新browser/context/page对象
3. ✅ 重启失败次数达到限制时正确终止脚本
4. ✅ 不再出现无限重试循环

### 强制文本模式验证：
1. ✅ 第一次对话成功完成后触发切换
2. ✅ 增加充分的等待时间（5秒 + 重试机制）
3. ✅ 使用更全面的按钮选择器
4. ✅ 切换失败时提供详细调试信息
5. ✅ 切换失败不影响后续对话继续进行

## 📝 日志关键词

监控以下日志消息以验证修复效果：

### 浏览器重启相关：
- `🚨 检测到浏览器crash异常，准备重启浏览器...`
- `🔧 检测到浏览器crash，立即重启浏览器...`
- `✅ 浏览器crash后恢复成功，继续测试...`

### 强制文本模式相关：
- `🎯 第一次对话完成，开始执行强制文本模式切换...`
- `等待页面充分加载...`
- `第X次尝试切换到文本模式...`
- `✅ 强制文本模式切换成功`
- `页面上共找到 X 个按钮` （调试信息）

## 🎯 预期效果

1. **浏览器crash问题**：不再出现无限重试，脚本能正确检测并重启浏览器
2. **强制文本模式**：成功率显著提高，即使失败也能提供详细调试信息
3. **整体稳定性**：脚本在长时间运行中更加稳定，异常恢复能力增强

---

**修复时间**: 2025-07-23  
**版本**: v4.1 - 浏览器重启和强制文本模式优化  
**文件**: `tk_chat_automation.py` 