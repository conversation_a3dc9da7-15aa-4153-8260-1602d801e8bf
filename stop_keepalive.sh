#!/bin/bash

# 确保在脚本所在目录运行
cd "$(dirname "$0")"

# 检查PID文件是否存在
if [ -f keepalive.pid ]; then
    # 读取PID
    PID=$(cat keepalive.pid)
    echo "找到保活脚本进程ID: $PID"
    
    # 尝试终止进程
    if kill $PID 2>/dev/null; then
        echo "已发送终止信号，等待进程退出..."
        sleep 3
        
        # 检查进程是否仍在运行
        if ps -p $PID > /dev/null; then
            echo "进程仍在运行，尝试强制终止..."
            kill -9 $PID 2>/dev/null
            sleep 1
        fi
        
        # 再次检查进程状态
        if ! ps -p $PID > /dev/null; then
            echo "保活脚本已成功停止"
            rm keepalive.pid
        else
            echo "警告: 无法终止进程 $PID"
        fi
    else
        echo "找不到进程 $PID，可能已经停止"
        rm keepalive.pid
    fi
else
    echo "未找到保活脚本PID文件，尝试查找并终止所有相关进程..."
    
    # 查找并终止所有相关进程
    PIDS=$(pgrep -f "python.*keepalive.py")
    if [ -n "$PIDS" ]; then
        echo "找到以下保活脚本进程: $PIDS"
        for P in $PIDS; do
            echo "正在终止进程 $P..."
            kill $P 2>/dev/null
        done
        sleep 2
        
        # 检查是否有进程仍在运行
        REMAINING=$(pgrep -f "python.*keepalive.py")
        if [ -n "$REMAINING" ]; then
            echo "以下进程仍在运行，尝试强制终止: $REMAINING"
            for P in $REMAINING; do
                kill -9 $P 2>/dev/null
            done
        fi
        
        echo "保活脚本已停止"
    else
        echo "未找到运行中的保活脚本进程"
    fi
fi

# 检查是否有聊天自动化脚本在运行
CHAT_PIDS=$(pgrep -f "python.*tk_chat_automation.py")
if [ -n "$CHAT_PIDS" ]; then
    echo "找到以下聊天自动化脚本进程: $CHAT_PIDS"
    read -p "是否同时停止聊天自动化脚本? (y/n): " ANSWER
    if [ "$ANSWER" == "y" ] || [ "$ANSWER" == "Y" ]; then
        for P in $CHAT_PIDS; do
            echo "正在终止进程 $P..."
            kill $P 2>/dev/null
        done
        sleep 2
        
        # 检查是否有进程仍在运行
        REMAINING=$(pgrep -f "python.*tk_chat_automation.py")
        if [ -n "$REMAINING" ]; then
            echo "以下进程仍在运行，尝试强制终止: $REMAINING"
            for P in $REMAINING; do
                kill -9 $P 2>/dev/null
            done
        fi
        
        echo "聊天自动化脚本已停止"
    fi
fi

echo "操作完成" 