"""
统计管理模块
负责对话统计、性能统计、数据分析等功能
"""

import json
import logging
from datetime import datetime
from collections import defaultdict, Counter


class DialogueStats:
    """对话统计类"""
    
    def __init__(self):
        self.conversations = []
        self.total_questions = 0
        self.total_errors = 0
        self.response_times = []
        self.start_time = datetime.now()
        
        # 分类统计
        self.question_types = Counter()
        self.error_types = Counter()
        self.response_time_ranges = defaultdict(int)
        
        # 每轮对话统计
        self.conversation_stats = {}
        
        # 兼容性统计
        self.successful_questions = 0
        self.failed_questions = 0
    
    def add_question(self, success=True):
        """添加单个问题的统计记录（兼容性方法）"""
        try:
            self.total_questions += 1
            if success:
                self.successful_questions += 1
            else:
                self.failed_questions += 1
                self.total_errors += 1
            
            logging.debug(f"已添加问题统计: success={success}")
            
        except Exception as e:
            logging.error(f"添加问题统计失败: {str(e)}")
    
    def add_conversation(self, conversation_id, qas):
        """添加对话记录"""
        try:
            conversation_data = {
                'conversation_id': conversation_id,
                'qas': qas,
                'timestamp': datetime.now().isoformat(),
                'question_count': len(qas),
                'avg_response_time': 0
            }
            
            # 计算响应时间
            response_times = []
            for qa in qas:
                if 'response_time' in qa and qa['response_time']:
                    try:
                        time_value = float(qa['response_time'].replace('秒', '').replace('s', ''))
                        response_times.append(time_value)
                    except (ValueError, AttributeError):
                        pass
            
            if response_times:
                conversation_data['avg_response_time'] = sum(response_times) / len(response_times)
                self.response_times.extend(response_times)
            
            # 统计问题类型
            for qa in qas:
                question = qa.get('question', '')
                if '[图片]' in question:
                    self.question_types['图片问题'] += 1
                elif 'AI生成问题' in question:
                    self.question_types['AI生成问题'] += 1
                elif 'AI追问对话' in question:
                    self.question_types['AI追问问题'] += 1
                else:
                    self.question_types['文本问题'] += 1
            
            self.conversations.append(conversation_data)
            self.conversation_stats[conversation_id] = conversation_data
            
            logging.info(f"已添加对话统计: {conversation_id}, 包含 {len(qas)} 个问题")
            
        except Exception as e:
            logging.error(f"添加对话统计失败: {str(e)}")
    
    def add_error(self, error_type, error_message=None, context=None):
        """添加错误记录"""
        try:
            self.total_errors += 1
            self.error_types[error_type] += 1
            
            error_record = {
                'type': error_type,
                'message': error_message,
                'context': context,
                'timestamp': datetime.now().isoformat()
            }
            
            # 如果有对话上下文，更新对应的对话统计
            if context and 'conversation_id' in context:
                conv_id = context['conversation_id']
                if conv_id in self.conversation_stats:
                    if 'errors' not in self.conversation_stats[conv_id]:
                        self.conversation_stats[conv_id]['errors'] = []
                    self.conversation_stats[conv_id]['errors'].append(error_record)
            
            logging.debug(f"已添加错误统计: {error_type}")
            
        except Exception as e:
            logging.error(f"添加错误统计失败: {str(e)}")
    
    def get_stats(self):
        """获取统计摘要"""
        try:
            current_time = datetime.now()
            duration = current_time - self.start_time
            
            stats = {
                'summary': {
                    'total_conversations': len(self.conversations),
                    'total_questions': self.total_questions,
                    'total_errors': self.total_errors,
                    'duration_seconds': duration.total_seconds(),
                    'start_time': self.start_time.isoformat(),
                    'end_time': current_time.isoformat()
                },
                'performance': {
                    'avg_response_time': sum(self.response_times) / len(self.response_times) if self.response_times else 0,
                    'min_response_time': min(self.response_times) if self.response_times else 0,
                    'max_response_time': max(self.response_times) if self.response_times else 0,
                    'questions_per_minute': (self.total_questions / duration.total_seconds() * 60) if duration.total_seconds() > 0 else 0
                },
                'question_types': dict(self.question_types),
                'error_types': dict(self.error_types),
                'success_rate': ((self.total_questions - self.total_errors) / self.total_questions * 100) if self.total_questions > 0 else 0
            }
            
            # 添加响应时间分布
            if self.response_times:
                stats['response_time_distribution'] = self._get_response_time_distribution()
            
            return stats
            
        except Exception as e:
            logging.error(f"获取统计摘要失败: {str(e)}")
            return {'error': str(e)}
    
    def _get_response_time_distribution(self):
        """获取响应时间分布"""
        try:
            distribution = {
                'under_1s': 0,
                '1s_to_3s': 0,
                '3s_to_5s': 0,
                '5s_to_10s': 0,
                'over_10s': 0
            }
            
            for time_val in self.response_times:
                if time_val < 1:
                    distribution['under_1s'] += 1
                elif time_val < 3:
                    distribution['1s_to_3s'] += 1
                elif time_val < 5:
                    distribution['3s_to_5s'] += 1
                elif time_val < 10:
                    distribution['5s_to_10s'] += 1
                else:
                    distribution['over_10s'] += 1
            
            return distribution
            
        except Exception as e:
            logging.error(f"计算响应时间分布失败: {str(e)}")
            return {}
    
    def get_conversation_stats(self, conversation_id):
        """获取特定对话的统计"""
        return self.conversation_stats.get(conversation_id, None)
    
    def export_stats(self, output_file):
        """导出统计数据"""
        try:
            stats_data = {
                'stats': self.get_stats(),
                'conversations': self.conversations,
                'raw_response_times': self.response_times
            }
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(stats_data, f, ensure_ascii=False, indent=2)
            
            logging.info(f"统计数据已导出到: {output_file}")
            
        except Exception as e:
            logging.error(f"导出统计数据失败: {str(e)}")
    
    def reset(self):
        """重置统计数据"""
        self.__init__()
        logging.info("统计数据已重置")


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics = defaultdict(list)
        self.start_times = {}
        self.counters = defaultdict(int)
    
    def start_timer(self, metric_name):
        """开始计时"""
        self.start_times[metric_name] = datetime.now()
    
    def end_timer(self, metric_name):
        """结束计时并记录"""
        if metric_name in self.start_times:
            duration = (datetime.now() - self.start_times[metric_name]).total_seconds()
            self.metrics[metric_name].append(duration)
            del self.start_times[metric_name]
            return duration
        return None
    
    def record_metric(self, metric_name, value):
        """记录指标值"""
        self.metrics[metric_name].append(value)
    
    def increment_counter(self, counter_name, amount=1):
        """增加计数器"""
        self.counters[counter_name] += amount
    
    def get_metric_stats(self, metric_name):
        """获取指标统计"""
        values = self.metrics.get(metric_name, [])
        if not values:
            return None
        
        return {
            'count': len(values),
            'sum': sum(values),
            'avg': sum(values) / len(values),
            'min': min(values),
            'max': max(values),
            'latest': values[-1] if values else None
        }
    
    def get_all_stats(self):
        """获取所有统计"""
        stats = {
            'metrics': {},
            'counters': dict(self.counters),
            'timestamp': datetime.now().isoformat()
        }
        
        for metric_name in self.metrics:
            stats['metrics'][metric_name] = self.get_metric_stats(metric_name)
        
        return stats


class DataAnalyzer:
    """数据分析器"""
    
    @staticmethod
    def analyze_response_times(response_times):
        """分析响应时间"""
        if not response_times:
            return {}
        
        sorted_times = sorted(response_times)
        n = len(sorted_times)
        
        return {
            'count': n,
            'mean': sum(sorted_times) / n,
            'median': sorted_times[n // 2] if n % 2 == 1 else (sorted_times[n // 2 - 1] + sorted_times[n // 2]) / 2,
            'p95': sorted_times[int(n * 0.95)] if n > 0 else 0,
            'p99': sorted_times[int(n * 0.99)] if n > 0 else 0,
            'std_dev': DataAnalyzer._calculate_std_dev(sorted_times)
        }
    
    @staticmethod
    def _calculate_std_dev(values):
        """计算标准差"""
        if len(values) < 2:
            return 0
        
        mean = sum(values) / len(values)
        variance = sum((x - mean) ** 2 for x in values) / len(values)
        return variance ** 0.5
    
    @staticmethod
    def analyze_question_patterns(conversations):
        """分析问题模式"""
        patterns = {
            'question_lengths': [],
            'answer_lengths': [],
            'common_keywords': Counter(),
            'time_patterns': defaultdict(int)
        }
        
        for conv in conversations:
            for qa in conv.get('qas', []):
                question = qa.get('question', '')
                answer = qa.get('answer', '')
                
                patterns['question_lengths'].append(len(question))
                patterns['answer_lengths'].append(len(answer))
                
                # 提取关键词（简单分词）
                words = question.split()
                for word in words:
                    if len(word) > 2:  # 忽略过短的词
                        patterns['common_keywords'][word] += 1
                
                # 时间模式分析
                timestamp = qa.get('timestamp', '')
                if timestamp:
                    try:
                        dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                        hour = dt.hour
                        patterns['time_patterns'][hour] += 1
                    except:
                        pass
        
        return {
            'avg_question_length': sum(patterns['question_lengths']) / len(patterns['question_lengths']) if patterns['question_lengths'] else 0,
            'avg_answer_length': sum(patterns['answer_lengths']) / len(patterns['answer_lengths']) if patterns['answer_lengths'] else 0,
            'top_keywords': dict(patterns['common_keywords'].most_common(10)),
            'hourly_distribution': dict(patterns['time_patterns'])
        }
    
    @staticmethod
    def generate_report(stats_data):
        """生成分析报告"""
        report = {
            'generated_at': datetime.now().isoformat(),
            'summary': stats_data.get('stats', {}).get('summary', {}),
            'performance_analysis': {},
            'recommendations': []
        }
        
        # 性能分析
        performance = stats_data.get('stats', {}).get('performance', {})
        if performance:
            avg_response = performance.get('avg_response_time', 0)
            
            if avg_response > 10:
                report['recommendations'].append("响应时间较长，建议优化AI模型或网络配置")
            elif avg_response > 5:
                report['recommendations'].append("响应时间偏高，可考虑优化")
            else:
                report['recommendations'].append("响应时间表现良好")
        
        # 成功率分析
        success_rate = stats_data.get('stats', {}).get('success_rate', 0)
        if success_rate < 90:
            report['recommendations'].append(f"成功率较低({success_rate:.1f}%)，需要关注错误处理")
        elif success_rate < 95:
            report['recommendations'].append(f"成功率良好({success_rate:.1f}%)，可进一步优化")
        
        # 问题类型分析
        question_types = stats_data.get('stats', {}).get('question_types', {})
        if question_types:
            most_common = max(question_types.items(), key=lambda x: x[1])
            report['performance_analysis']['dominant_question_type'] = most_common[0]
        
        return report


def create_stats_summary(dialogue_stats, performance_monitor=None):
    """创建统计摘要"""
    summary = {
        'dialogue_stats': dialogue_stats.get_stats(),
        'timestamp': datetime.now().isoformat()
    }
    
    if performance_monitor:
        summary['performance_stats'] = performance_monitor.get_all_stats()
    
    return summary


def export_stats_to_json(stats_data, output_file):
    """导出统计数据到JSON文件"""
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(stats_data, f, ensure_ascii=False, indent=2)
        
        logging.info(f"统计数据已导出到: {output_file}")
        return True
        
    except Exception as e:
        logging.error(f"导出统计数据失败: {str(e)}")
        return False


def compare_stats(stats1, stats2):
    """比较两组统计数据"""
    comparison = {
        'timestamp': datetime.now().isoformat(),
        'improvements': [],
        'regressions': [],
        'unchanged': []
    }
    
    try:
        # 比较关键指标
        metrics_to_compare = [
            ('total_questions', '问题总数'),
            ('total_errors', '错误总数'),
            ('success_rate', '成功率'),
            ('avg_response_time', '平均响应时间')
        ]
        
        summary1 = stats1.get('summary', {})
        summary2 = stats2.get('summary', {})
        perf1 = stats1.get('performance', {})
        perf2 = stats2.get('performance', {})
        
        for metric, display_name in metrics_to_compare:
            val1 = summary1.get(metric, perf1.get(metric, 0))
            val2 = summary2.get(metric, perf2.get(metric, 0))
            
            if val1 == val2:
                comparison['unchanged'].append(f"{display_name}: {val1}")
            elif metric == 'success_rate' and val2 > val1:
                comparison['improvements'].append(f"{display_name}: {val1:.1f}% → {val2:.1f}%")
            elif metric == 'avg_response_time' and val2 < val1:
                comparison['improvements'].append(f"{display_name}: {val1:.2f}s → {val2:.2f}s")
            elif metric in ['total_questions'] and val2 > val1:
                comparison['improvements'].append(f"{display_name}: {val1} → {val2}")
            elif metric == 'total_errors' and val2 < val1:
                comparison['improvements'].append(f"{display_name}: {val1} → {val2}")
            else:
                comparison['regressions'].append(f"{display_name}: {val1} → {val2}")
        
        return comparison
        
    except Exception as e:
        logging.error(f"比较统计数据失败: {str(e)}")
        return {'error': str(e)} 