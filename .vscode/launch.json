{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python Debugger: Current File",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal"
        },
        {
            "name": "Debug TK Chat Automation",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/tk_chat_automation.py",
            "python": "/Users/<USER>/miniforge3/envs/tk_chat/bin/python3",
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}",
            "env": {
                "PYTHONPATH": "${workspaceFolder}",
                "CONDA_DEFAULT_ENV": "tk_chat"
            },
            "args": [],
            "stopOnEntry": false,
            "justMyCode": true
        },
        {
            "name": "Debug TK Chat Automation (Test Mode)",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/tk_chat_automation.py",
            "python": "/Users/<USER>/miniforge3/envs/tk_chat/bin/python3",
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}",
            "env": {
                "PYTHONPATH": "${workspaceFolder}",
                "CONDA_DEFAULT_ENV": "tk_chat"
            },
            "args": ["--test-mode"],
            "stopOnEntry": false,
            "justMyCode": true
        },
        {
            "name": "R-Debug TK Chat Automation (Test Mode)",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/tk_chat_automation_refactored.py",
            "python": "/Users/<USER>/miniforge3/envs/tk_chat/bin/python3",
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}",
            "env": {
                "PYTHONPATH": "${workspaceFolder}",
                "CONDA_DEFAULT_ENV": "tk_chat"
            },
            "args": [],
            "stopOnEntry": false,
            "justMyCode": true
        },
        {
            "name": "Debug TK Chat Automation (Custom Questions)",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/tk_chat_automation.py",
            "python": "/Users/<USER>/miniforge3/envs/tk_chat/bin/python3",
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}",
            "env": {
                "PYTHONPATH": "${workspaceFolder}",
                "CONDA_DEFAULT_ENV": "tk_chat"
            },
            "args": ["--questions-file", "default_questions.json", "--access-mode", "sequential"],
            "stopOnEntry": false,
            "justMyCode": true
        }
    ]
}