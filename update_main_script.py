#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
更新主脚本的选择器逻辑
为tk_chat_automation.py添加多模式支持
"""

import re

# 新的输入框查找函数
NEW_INPUT_FUNCTION = '''
def find_input_element_adaptive(page, timeout=30000):
    """自适应查找输入框元素，支持多种聊天模式"""
    
    # 按优先级排序的选择器列表
    input_selectors = [
        # 基于HTML分析的最高优先级选择器
        'input.flex.h-9.w-full.rounded-md.border.border-input.bg-transparent',
        'input.flex.h-9.w-full',
        'input[class*="border-input"]',
        'input[class*="bg-transparent"]',
        
        # 基于placeholder的选择器
        'input[placeholder*="快来"]',
        'input[placeholder*="聊"]',
        
        # 原有的选择器（保持兼容性）
        'input[placeholder="快来和我聊一聊..."]',
        'input[placeholder="请在这里输入..."]',
        'input[placeholder="输入消息..."]',
        
        # 通用选择器
        'input[type="text"]',
        'textarea',
        
        # 容器内的输入框
        '.fixed.bottom-6 input',
        '[class*="max-w-3xl"] input',
        
        # 备用选择器
        '[contenteditable="true"]',
        '[role="textbox"]'
    ]
    
    logging.info("🔍 自适应查找输入框...")
    
    for i, selector in enumerate(input_selectors, 1):
        try:
            logging.info(f"尝试输入框选择器 {i}/{len(input_selectors)}: {selector}")
            
            # 使用较短的超时时间避免过长等待
            element = page.wait_for_selector(selector, timeout=timeout//len(input_selectors), state='visible')
            
            if element and element.is_visible() and element.is_enabled():
                logging.info(f"✅ 成功找到输入框: {selector}")
                return element
                
        except Exception as e:
            logging.debug(f"选择器 {selector} 失败: {e}")
            continue
    
    # 如果所有选择器都失败，抛出异常
    raise Exception("未找到任何可用的输入框元素")
'''

# 新的发送按钮查找函数
NEW_SEND_FUNCTION = '''
def find_send_button_adaptive(page, timeout=30000):
    """自适应查找发送按钮，支持多种聊天模式"""
    
    # 按优先级排序的选择器列表
    send_selectors = [
        # 基于位置的选择器
        '.fixed.bottom-6 button',
        '[class*="max-w-3xl"] button',
        
        # 基于class的选择器
        'button.inline-flex',
        'button[class*="rounded-md"]',
        'button[class*="p-0"]',
        
        # 基于功能的选择器
        'button[type="submit"]',
        'button:has-text("发送")',
        'button:has-text("Send")',
        
        # 原有的选择器（保持兼容性）
        'button[aria-label="发送"]',
        'button[title="发送"]',
        '.send-button',
        
        # SVG图标按钮
        'button svg',
        'button[class*="send"]',
        
        # 通用选择器
        '[role="button"]'
    ]
    
    logging.info("🔍 自适应查找发送按钮...")
    
    for i, selector in enumerate(send_selectors, 1):
        try:
            logging.info(f"尝试发送按钮选择器 {i}/{len(send_selectors)}: {selector}")
            
            # 对于SVG按钮特殊处理
            if 'svg' in selector.lower():
                elements = page.query_selector_all(selector)
                for svg_element in elements:
                    # 查找包含SVG的按钮
                    button = page.locator('button').filter(has=svg_element)
                    if button.count() > 0 and button.first.is_visible():
                        logging.info(f"✅ 通过SVG找到发送按钮: {selector}")
                        return button.first
            else:
                element = page.wait_for_selector(selector, timeout=timeout//len(send_selectors), state='visible')
                
                if element and element.is_visible() and element.is_enabled():
                    logging.info(f"✅ 成功找到发送按钮: {selector}")
                    return element
                
        except Exception as e:
            logging.debug(f"选择器 {selector} 失败: {e}")
            continue
    
    # 如果找不到发送按钮，返回None（可以尝试回车键发送）
    logging.warning("⚠️ 未找到发送按钮，将尝试回车键发送")
    return None
'''

def update_main_script():
    """更新主脚本"""
    print("📝 准备更新tk_chat_automation.py...")
    
    try:
        # 读取原脚本
        with open('tk_chat_automation.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 备份原文件
        with open('tk_chat_automation_backup.py', 'w', encoding='utf-8') as f:
            f.write(content)
        print("✅ 已创建备份文件: tk_chat_automation_backup.py")
        
        # 在适当位置插入新函数
        # 找到setup_periodic_center_click函数之前插入
        pattern = r'(# 添加定时点击函数\ndef setup_periodic_center_click)'
        replacement = f'{NEW_INPUT_FUNCTION}\n\n{NEW_SEND_FUNCTION}\n\n\\1'
        
        new_content = re.sub(pattern, replacement, content, count=1)
        
        if new_content == content:
            print("⚠️ 未找到插入点，将在文件末尾添加函数")
            new_content = content + '\n\n' + NEW_INPUT_FUNCTION + '\n\n' + NEW_SEND_FUNCTION
        
        # 写回文件
        with open('tk_chat_automation_adaptive.py', 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print("✅ 已创建自适应版本: tk_chat_automation_adaptive.py")
        print("\n📋 更新说明:")
        print("1. 添加了 find_input_element_adaptive() 函数")
        print("2. 添加了 find_send_button_adaptive() 函数")
        print("3. 支持多种聊天模式的选择器")
        print("4. 按优先级尝试不同的选择器")
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")

if __name__ == "__main__":
    update_main_script() 