#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Android设备App流量抓取采集工具 (简化版)
用于监控和统计Android应用的网络流量消耗情况

功能:
- 连接Android设备
- 实时监控App流量
- 生成流量统计报告
- 纯Python内置库实现，无需安装第三方依赖

作者: AI Assistant
"""

import subprocess
import json
import time
import csv
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import threading
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,  # 改为DEBUG级别以显示更多信息
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('traffic_monitor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)


class TrafficData:
    """流量数据结构"""
    
    def __init__(self, timestamp: datetime, package_name: str, app_name: str, 
                 uid: int, rx_bytes: int, tx_bytes: int, rx_packets: int, tx_packets: int):
        self.timestamp = timestamp
        self.package_name = package_name
        self.app_name = app_name
        self.uid = uid
        self.rx_bytes = rx_bytes
        self.tx_bytes = tx_bytes
        self.rx_packets = rx_packets
        self.tx_packets = tx_packets
    
    @property
    def total_bytes(self) -> int:
        return self.rx_bytes + self.tx_bytes
    
    @property
    def total_packets(self) -> int:
        return self.rx_packets + self.tx_packets
    
    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            'timestamp': self.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
            'package_name': self.package_name,
            'app_name': self.app_name,
            'uid': self.uid,
            'rx_bytes': self.rx_bytes,
            'tx_bytes': self.tx_bytes,
            'total_bytes': self.total_bytes,
            'rx_packets': self.rx_packets,
            'tx_packets': self.tx_packets,
            'total_packets': self.total_packets
        }


class AndroidTrafficMonitor:
    """Android流量监控器"""
    
    def __init__(self, device_id: Optional[str] = None):
        self.device_id = device_id
        self.is_monitoring = False
        self.monitoring_thread = None
        self.traffic_history: List[TrafficData] = []
        self.package_to_monitor: Optional[str] = None
        self.monitoring_interval = 5  # 秒
        self.output_dir = Path("traffic_reports")
        self.output_dir.mkdir(exist_ok=True)
        
    def check_adb_connection(self) -> bool:
        """检查ADB连接"""
        try:
            cmd = ["adb", "devices"]
            if self.device_id:
                cmd = ["adb", "-s", self.device_id, "devices"]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                devices = result.stdout.strip().split('\n')[1:]  # 跳过标题行
                connected_devices = [line.split('\t')[0] for line in devices if '\tdevice' in line]
                
                if connected_devices:
                    if not self.device_id:
                        self.device_id = connected_devices[0]
                    logging.info(f"✅ ADB连接成功，设备ID: {self.device_id}")
                    return True
                else:
                    logging.error("❌ 没有找到连接的设备")
                    return False
            else:
                logging.error(f"❌ ADB命令执行失败: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logging.error("❌ ADB连接超时")
            return False
        except FileNotFoundError:
            logging.error("❌ 未找到ADB工具，请确保已安装Android SDK")
            return False
        except Exception as e:
            logging.error(f"❌ ADB连接异常: {str(e)}")
            return False
    
    def get_installed_packages(self) -> Dict[str, str]:
        """获取已安装的应用包名和应用名"""
        try:
            cmd = ["adb", "-s", self.device_id, "shell", "pm", "list", "packages", "-3"]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode != 0:
                logging.error(f"获取应用列表失败: {result.stderr}")
                return {}
            
            packages = {}
            package_lines = result.stdout.strip().split('\n')
            
            for line in package_lines:
                if line.startswith('package:'):
                    package_name = line.replace('package:', '').strip()
                    
                    # 获取应用名称
                    app_name = self.get_app_name(package_name)
                    packages[package_name] = app_name
            
            logging.info(f"📱 找到 {len(packages)} 个第三方应用")
            return packages
            
        except Exception as e:
            logging.error(f"获取应用列表异常: {str(e)}")
            return {}
    
    def get_app_name(self, package_name: str) -> str:
        """获取应用的显示名称"""
        try:
            cmd = ["adb", "-s", self.device_id, "shell", "dumpsys", "package", package_name]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
            
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'applicationInfo' in line and 'name=' in line:
                        # 尝试提取应用名
                        parts = line.split('name=')
                        if len(parts) > 1:
                            app_name = parts[1].split()[0].strip('"')
                            return app_name
            
            # 如果无法获取显示名称，返回包名
            return package_name.split('.')[-1].title()
            
        except Exception:
            return package_name.split('.')[-1].title()
    
    def get_app_uid(self, package_name: str) -> Optional[int]:
        """获取应用的UID"""
        try:
            # 方法1: 使用pm命令获取UID
            cmd = ["adb", "-s", self.device_id, "shell", "pm", "list", "packages", "-U", package_name]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and result.stdout.strip():
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if f'package:{package_name}' in line and 'uid:' in line:
                        # 解析格式: package:com.example.app uid:10123
                        parts = line.split('uid:')
                        if len(parts) > 1:
                            uid_str = parts[1].strip()
                            return int(uid_str)
            
            # 方法2: 使用dumpsys package命令
            cmd = ["adb", "-s", self.device_id, "shell", "dumpsys", "package", package_name]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
            
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                for line in lines:
                    # 查找多种可能的UID格式
                    if 'userId=' in line:
                        parts = line.split('userId=')
                        if len(parts) > 1:
                            uid_str = parts[1].split()[0]
                            return int(uid_str)
                    elif 'appId=' in line:
                        parts = line.split('appId=')
                        if len(parts) > 1:
                            uid_str = parts[1].split()[0]
                            # appId通常需要加上用户ID偏移量(10000 for user 0)
                            return int(uid_str) + 10000
            
            # 方法3: 使用ps命令查找应用进程
            cmd = ["adb", "-s", self.device_id, "shell", "ps | grep", package_name]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and result.stdout.strip():
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    parts = line.split()
                    if len(parts) >= 2 and package_name in line:
                        # ps输出格式通常为: USER PID PPID VSIZE RSS WCHAN ADDR S NAME
                        user_part = parts[0]
                        if user_part.startswith('u0_a'):
                            # 解析u0_a123格式的用户ID
                            app_id = int(user_part[4:])
                            return app_id + 10000
            
            logging.warning(f"⚠️  无法获取应用 {package_name} 的UID，可能应用未运行")
            return None
            
        except Exception as e:
            logging.error(f"获取UID失败: {str(e)}")
            return None
    
    def get_traffic_stats(self, package_name: str) -> Optional[TrafficData]:
        """获取指定应用的流量统计"""
        try:
            uid = self.get_app_uid(package_name)
            if uid is None:
                logging.error(f"无法获取应用 {package_name} 的UID")
                return None
            
            logging.debug(f"📱 正在获取UID {uid} 的流量数据...")
            
            tx_bytes = 0
            rx_bytes = 0
            method_used = "未知"
            
            # 方法1: 尝试读取 /proc/uid_stat/ (需要root权限)
            try:
                cmd = ["adb", "-s", self.device_id, "shell", "cat", f"/proc/uid_stat/{uid}/tcp_snd"]
                tx_result = subprocess.run(cmd, capture_output=True, text=True, timeout=5)
                
                cmd = ["adb", "-s", self.device_id, "shell", "cat", f"/proc/uid_stat/{uid}/tcp_rcv"]
                rx_result = subprocess.run(cmd, capture_output=True, text=True, timeout=5)
                
                if tx_result.returncode == 0 and tx_result.stdout.strip():
                    tx_bytes = int(tx_result.stdout.strip())
                    method_used = "proc文件系统"
                
                if rx_result.returncode == 0 and rx_result.stdout.strip():
                    rx_bytes = int(rx_result.stdout.strip())
                    
                if tx_bytes > 0 or rx_bytes > 0:
                    logging.debug(f"✅ 通过{method_used}获取流量: RX={rx_bytes}, TX={tx_bytes}")
            except Exception as e:
                logging.debug(f"proc方式失败: {str(e)}")
            
            # 方法2: 使用dumpsys netstats (优化解析)
            if tx_bytes == 0 and rx_bytes == 0:
                try:
                    cmd = ["adb", "-s", self.device_id, "shell", "dumpsys", "netstats", "detail"]
                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=20)
                    
                    if result.returncode == 0:
                        lines = result.stdout.split('\n')
                        
                        # 新的解析方法：查找包含uid的行，然后解析NetworkStatsHistory数据
                        import re
                        for i, line in enumerate(lines):
                            if f'uid={uid}' in line and ('set=DEFAULT' in line or 'set=FOREGROUND' in line):
                                logging.debug(f"找到UID行: {line.strip()}")
                                
                                # 在接下来的几行中查找NetworkStatsHistory数据
                                for j in range(i + 1, min(i + 20, len(lines))):
                                    current_line = lines[j].strip()
                                    if not current_line:
                                        continue
                                    
                                    # 查找包含流量数据的行 (格式: st=timestamp rb=bytes rp=packets tb=bytes tp=packets op=ops)
                                    if 'rb=' in current_line and 'tb=' in current_line:
                                        logging.debug(f"找到流量行: {current_line}")
                                        
                                        # 解析rb和tb数据
                                        rb_match = re.search(r'rb=(\d+)', current_line)
                                        tb_match = re.search(r'tb=(\d+)', current_line)
                                        
                                        if rb_match:
                                            current_rx = int(rb_match.group(1))
                                            rx_bytes += current_rx
                                            logging.debug(f"找到RX数据: {current_rx}")
                                        
                                        if tb_match:
                                            current_tx = int(tb_match.group(1))
                                            tx_bytes += current_tx
                                            logging.debug(f"找到TX数据: {current_tx}")
                                    
                                    # 如果遇到下一个ident行，停止解析当前section
                                    elif 'ident=' in current_line and 'uid=' in current_line:
                                        break
                        
                        if tx_bytes > 0 or rx_bytes > 0:
                            method_used = "dumpsys netstats"
                            logging.debug(f"✅ 通过{method_used}获取流量: RX={rx_bytes}, TX={tx_bytes}")
                        else:
                            # 备用解析方法：直接搜索UID相关的所有数据
                            uid_lines = [line for line in lines if str(uid) in line]
                            logging.debug(f"找到 {len(uid_lines)} 行包含UID {uid} 的数据")
                            for uid_line in uid_lines[:5]:  # 只记录前5行
                                logging.debug(f"UID行: {uid_line.strip()}")
                
                except Exception as e:
                    logging.debug(f"dumpsys netstats方式失败: {str(e)}")
            
            # 方法3: 使用netstat命令 (备用方案)
            if tx_bytes == 0 and rx_bytes == 0:
                try:
                    # 尝试通过netstat获取网络连接信息
                    cmd = ["adb", "-s", self.device_id, "shell", "netstat", "-u"]
                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                    
                    if result.returncode == 0:
                        # 这里可以尝试解析netstat输出，但通常不包含字节数
                        logging.debug("netstat命令执行成功，但无法直接获取流量字节数")
                except Exception as e:
                    logging.debug(f"netstat方式失败: {str(e)}")
            
            # 方法4: 使用cat /proc/net/xt_qtaguid/stats (需要高权限)
            if tx_bytes == 0 and rx_bytes == 0:
                try:
                    cmd = ["adb", "-s", self.device_id, "shell", "cat", "/proc/net/xt_qtaguid/stats"]
                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                    
                    if result.returncode == 0:
                        lines = result.stdout.split('\n')
                        for line in lines:
                            if str(uid) in line:
                                parts = line.split()
                                if len(parts) >= 8:
                                    # 格式通常为: idx iface acct_tag_hex uid_tag_int cnt_set rx_bytes rx_packets tx_bytes tx_packets
                                    try:
                                        line_uid = int(parts[3])
                                        if line_uid == uid:
                                            rx_bytes += int(parts[5])
                                            tx_bytes += int(parts[7])
                                            method_used = "xt_qtaguid"
                                    except (ValueError, IndexError):
                                        continue
                        if tx_bytes > 0 or rx_bytes > 0:
                            logging.debug(f"✅ 通过{method_used}获取流量: RX={rx_bytes}, TX={tx_bytes}")
                except Exception as e:
                    logging.debug(f"xt_qtaguid方式失败: {str(e)}")
            
            # 如果所有方法都没有获取到数据，记录详细信息
            if tx_bytes == 0 and rx_bytes == 0:
                logging.warning(f"⚠️  所有方法都无法获取UID {uid} 的流量数据")
                logging.warning(f"💡 可能的原因:")
                logging.warning(f"   1. 应用未产生网络流量")
                logging.warning(f"   2. 设备需要root权限")
                logging.warning(f"   3. Android版本不支持")
                logging.warning(f"   4. 应用未运行")
            else:
                logging.info(f"📊 成功获取流量数据 (方法: {method_used}) - RX: {rx_bytes/1024/1024:.2f}MB, TX: {tx_bytes/1024/1024:.2f}MB")
            
            app_name = self.get_app_name(package_name)
            
            return TrafficData(
                timestamp=datetime.now(),
                package_name=package_name,
                app_name=app_name,
                uid=uid,
                rx_bytes=rx_bytes,
                tx_bytes=tx_bytes,
                rx_packets=0,
                tx_packets=0
            )
            
        except Exception as e:
            logging.error(f"获取流量统计异常: {str(e)}")
            return None
    
    def start_monitoring(self, package_name: str, interval: int = 5):
        """开始监控指定应用的流量"""
        if self.is_monitoring:
            logging.warning("⚠️  监控已在运行中")
            return
        
        if not self.check_adb_connection():
            logging.error("❌ 无法连接到Android设备")
            return
        
        self.package_to_monitor = package_name
        self.monitoring_interval = interval
        self.is_monitoring = True
        self.traffic_history.clear()
        
        # 启动监控线程
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop)
        self.monitoring_thread.daemon = True
        self.monitoring_thread.start()
        
        logging.info(f"🚀 开始监控应用: {package_name}")
        logging.info(f"⏱️  监控间隔: {interval} 秒")
    
    def _monitoring_loop(self):
        """监控循环"""
        previous_data = None
        
        while self.is_monitoring:
            try:
                current_data = self.get_traffic_stats(self.package_to_monitor)
                
                if current_data:
                    self.traffic_history.append(current_data)
                    
                    # 计算流量增量
                    if previous_data:
                        rx_delta = current_data.rx_bytes - previous_data.rx_bytes
                        tx_delta = current_data.tx_bytes - previous_data.tx_bytes
                        total_delta = rx_delta + tx_delta
                        
                        # 实时显示流量变化
                        if total_delta > 0:
                            rx_mb = rx_delta / 1024 / 1024
                            tx_mb = tx_delta / 1024 / 1024
                            total_mb = total_delta / 1024 / 1024
                            
                            logging.info(f"📊 {current_data.app_name} - "
                                       f"下载: {rx_mb:.2f}MB, "
                                       f"上传: {tx_mb:.2f}MB, "
                                       f"总计: {total_mb:.2f}MB")
                    
                    previous_data = current_data
                
                time.sleep(self.monitoring_interval)
                
            except Exception as e:
                logging.error(f"监控循环异常: {str(e)}")
                time.sleep(self.monitoring_interval)
    
    def stop_monitoring(self):
        """停止监控"""
        if not self.is_monitoring:
            logging.warning("⚠️  监控未在运行")
            return
        
        self.is_monitoring = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=10)
        
        logging.info("🛑 监控已停止")
    
    def get_traffic_summary(self) -> Dict:
        """获取流量统计摘要"""
        if not self.traffic_history:
            return {}
        
        first_data = self.traffic_history[0]
        last_data = self.traffic_history[-1]
        
        total_rx = last_data.rx_bytes - first_data.rx_bytes
        total_tx = last_data.tx_bytes - first_data.tx_bytes
        total_traffic = total_rx + total_tx
        
        duration = last_data.timestamp - first_data.timestamp
        
        return {
            'app_name': last_data.app_name,
            'package_name': last_data.package_name,
            'start_time': first_data.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
            'end_time': last_data.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
            'duration_seconds': duration.total_seconds(),
            'total_rx_bytes': total_rx,
            'total_tx_bytes': total_tx,
            'total_traffic_bytes': total_traffic,
            'total_rx_mb': total_rx / 1024 / 1024,
            'total_tx_mb': total_tx / 1024 / 1024,
            'total_traffic_mb': total_traffic / 1024 / 1024,
            'average_speed_mbps': (total_traffic / 1024 / 1024) / max(duration.total_seconds() / 60, 1),
            'sample_count': len(self.traffic_history)
        }
    
    def export_to_csv(self, filename: Optional[str] = None) -> str:
        """导出数据到CSV文件"""
        if not self.traffic_history:
            raise ValueError("没有可导出的数据")
        
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"traffic_data_{self.package_to_monitor}_{timestamp}.csv"
        
        filepath = self.output_dir / filename
        
        with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['timestamp', 'package_name', 'app_name', 'uid',
                         'rx_bytes', 'tx_bytes', 'total_bytes',
                         'rx_packets', 'tx_packets', 'total_packets']
            
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for data in self.traffic_history:
                writer.writerow(data.to_dict())
        
        logging.info(f"📄 数据已导出到: {filepath}")
        return str(filepath)
    
    def generate_text_chart(self, filename: Optional[str] = None) -> str:
        """生成文本格式的流量图表"""
        if len(self.traffic_history) < 2:
            raise ValueError("数据不足，无法生成图表")
        
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"traffic_chart_{self.package_to_monitor}_{timestamp}.txt"
        
        filepath = self.output_dir / filename
        
        # 计算流量增量
        rx_deltas = []
        tx_deltas = []
        timestamps = []
        
        for i in range(1, len(self.traffic_history)):
            prev_data = self.traffic_history[i-1]
            curr_data = self.traffic_history[i]
            
            rx_delta = max(0, (curr_data.rx_bytes - prev_data.rx_bytes) / 1024 / 1024)  # MB
            tx_delta = max(0, (curr_data.tx_bytes - prev_data.tx_bytes) / 1024 / 1024)  # MB
            
            rx_deltas.append(rx_delta)
            tx_deltas.append(tx_delta)
            timestamps.append(curr_data.timestamp.strftime('%H:%M:%S'))
        
        # 生成文本图表
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(f"{self.traffic_history[0].app_name} - 流量使用报告\n")
            f.write("=" * 60 + "\n\n")
            
            # 流量数据表格
            f.write("时间间隔流量统计:\n")
            f.write("-" * 60 + "\n")
            f.write(f"{'时间':<12} {'下载(MB)':<12} {'上传(MB)':<12} {'总计(MB)':<12}\n")
            f.write("-" * 60 + "\n")
            
            for i, (ts, rx, tx) in enumerate(zip(timestamps, rx_deltas, tx_deltas)):
                total = rx + tx
                f.write(f"{ts:<12} {rx:<12.2f} {tx:<12.2f} {total:<12.2f}\n")
            
            # 累计统计
            f.write("\n累计流量统计:\n")
            f.write("-" * 40 + "\n")
            total_rx = sum(rx_deltas)
            total_tx = sum(tx_deltas)
            total_traffic = total_rx + total_tx
            
            f.write(f"累计下载: {total_rx:.2f} MB\n")
            f.write(f"累计上传: {total_tx:.2f} MB\n")
            f.write(f"总计流量: {total_traffic:.2f} MB\n")
            
            if timestamps:
                duration_minutes = len(timestamps) * self.monitoring_interval / 60
                avg_speed = total_traffic / max(duration_minutes, 1)
                f.write(f"平均速率: {avg_speed:.2f} MB/分钟\n")
        
        logging.info(f"📈 文本图表已保存到: {filepath}")
        return str(filepath)
    
    def print_summary_report(self):
        """打印流量统计报告"""
        summary = self.get_traffic_summary()
        if not summary:
            print("❌ 没有可用的统计数据")
            return
        
        print("\n" + "="*60)
        print("📊 Android应用流量统计报告")
        print("="*60)
        print(f"应用名称: {summary['app_name']}")
        print(f"包名: {summary['package_name']}")
        print(f"监控开始时间: {summary['start_time']}")
        print(f"监控结束时间: {summary['end_time']}")
        print(f"监控时长: {summary['duration_seconds']:.1f} 秒")
        print(f"采样次数: {summary['sample_count']}")
        print("-"*60)
        print(f"下载流量: {summary['total_rx_mb']:.2f} MB")
        print(f"上传流量: {summary['total_tx_mb']:.2f} MB")
        print(f"总流量: {summary['total_traffic_mb']:.2f} MB")
        print(f"平均速率: {summary['average_speed_mbps']:.2f} MB/分钟")
        print("="*60)


def main():
    """主函数"""
    print("📱 Android应用流量监控工具 (简化版)")
    print("="*50)
    
    monitor = AndroidTrafficMonitor()
    
    # 检查ADB连接
    if not monitor.check_adb_connection():
        print("❌ 请确保:")
        print("1. Android设备已连接并开启USB调试")
        print("2. 已安装Android SDK Platform Tools")
        print("3. 设备已授权ADB调试")
        return
    
    # 获取已安装的应用
    print("\n🔍 正在获取已安装的应用列表...")
    packages = monitor.get_installed_packages()
    
    if not packages:
        print("❌ 未找到可监控的应用")
        return
    
    # 显示应用列表
    print(f"\n📋 找到 {len(packages)} 个可监控的应用:")
    package_list = list(packages.items())
    for i, (package_name, app_name) in enumerate(package_list, 1):
        print(f"{i:2d}. {app_name} ({package_name})")
    
    # 用户选择应用
    try:
        while True:
            choice = input(f"\n请选择要监控的应用 (1-{len(package_list)}): ").strip()
            if choice.isdigit():
                index = int(choice) - 1
                if 0 <= index < len(package_list):
                    selected_package = package_list[index][0]
                    selected_app = package_list[index][1]
                    break
            print("❌ 无效选择，请重新输入")
    
        # 设置监控参数
        interval = 5
        try:
            interval_input = input(f"请输入监控间隔(秒，默认{interval}): ").strip()
            if interval_input:
                interval = max(1, int(interval_input))
        except ValueError:
            pass
        
        print(f"\n🎯 已选择监控: {selected_app}")
        print(f"⏱️  监控间隔: {interval} 秒")
        print("💡 按 Ctrl+C 停止监控")
        
        # 开始监控
        monitor.start_monitoring(selected_package, interval)
        
        # 等待用户中断
        try:
            while monitor.is_monitoring:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n\n⏹️  收到停止信号...")
        
        # 停止监控
        monitor.stop_monitoring()
        
        # 生成报告
        if monitor.traffic_history:
            print("\n📊 正在生成报告...")
            
            # 打印摘要
            monitor.print_summary_report()
            
            # 导出数据
            try:
                csv_file = monitor.export_to_csv()
                print(f"✅ CSV数据已导出: {csv_file}")
            except Exception as e:
                print(f"❌ CSV导出失败: {str(e)}")
            
            # 生成文本图表
            try:
                chart_file = monitor.generate_text_chart()
                print(f"✅ 文本图表已生成: {chart_file}")
            except Exception as e:
                print(f"❌ 图表生成失败: {str(e)}")
        
        print("\n🏁 监控完成!")
        
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")


if __name__ == "__main__":
    main() 