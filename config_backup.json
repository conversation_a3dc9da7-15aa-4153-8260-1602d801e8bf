{"application": {"name": "TK Chat Automation", "version": "2.0.0", "target_url": "https://chatbot.tika.style/", "test_mode": false, "conversation_count": 10, "test_conversation_count": 5}, "browser": {"headless": false, "viewport": {"width": 1280, "height": 720}, "timeout": 30000, "keep_alive_interval": 600}, "logging": {"level": "INFO", "file_enabled": true, "console_enabled": true, "max_bytes": 10485760, "backup_count": 5, "log_directory": "logs"}, "question_config": {"default_file": "questions.json", "selection_method": "random", "question_count_per_conversation": 5, "formats_supported": ["json", "csv", "txt"]}, "ai_config": {"generation": {"enabled": true, "default_provider": "ollama", "providers": {"ollama": {"provider": "ollama", "model": "deepseek-r1:14b", "base_url": "http://localhost:11434", "temperature": 0.7, "max_tokens": 1000, "timeout": 120}, "openai": {"provider": "openai", "model": "gpt-3.5-turbo", "api_key": "${OPENAI_API_KEY}", "base_url": "https://api.openai.com/v1", "temperature": 0.7, "max_tokens": 1000, "timeout": 120}}, "generation_settings": {"default_count": 3, "max_count": 10, "fallback_to_example": true, "retry_attempts": 2}}, "evaluation": {"enabled": true, "default_provider": "ollama_eval", "providers": {"ollama_eval": {"provider": "ollama", "model": "deepseek-r1:14b", "base_url": "http://localhost:11434", "temperature": 0.3, "max_tokens": 1500, "timeout": 180}, "openai_eval": {"provider": "openai", "model": "gpt-4", "api_key": "${OPENAI_API_KEY}", "base_url": "https://api.openai.com/v1", "temperature": 0.3, "max_tokens": 1500, "timeout": 60}}, "evaluation_settings": {"score_scale": 5, "evaluation_dimensions": ["理解力", "记忆力", "专业性", "情商话术", "知识储备", "合规性"], "domain": "时尚", "retry_attempts": 2}}}, "storage": {"oss": {"enabled": true, "access_key_id": "${OSS_ACCESS_KEY_ID}", "access_key_secret": "${OSS_ACCESS_KEY_SECRET}", "bucket_name": "${OSS_BUCKET_NAME}", "endpoint": "${OSS_ENDPOINT}", "folder_name": "tk_automation", "auto_cleanup_days": 30}, "local": {"screenshots_dir": "screenshots", "results_dir": "results", "logs_dir": "logs"}}, "notification": {"enabled_services": ["<PERSON><PERSON><PERSON>"], "dingtalk": {"webhook_url": "${DINGTALK_WEBHOOK_URL}", "at_phone": null, "enabled": false}, "email": {"enabled": false, "smtp": {"host": "smtp.example.com", "port": 587, "username": "${EMAIL_USERNAME}", "password": "${EMAIL_PASSWORD}"}, "to_emails": ["<EMAIL>"]}, "slack": {"enabled": false, "webhook_url": "${SLACK_WEBHOOK_URL}", "channel": "#automation"}}, "image_processing": {"download_enabled": true, "download_dir": "downloaded_images", "validation_enabled": true, "max_file_size_mb": 10, "resize": {"enabled": true, "max_width": 800, "max_height": 600}, "compression": {"enabled": true, "quality": 85, "max_file_size_mb": 2}}, "reporting": {"formats": ["html", "excel"], "include_ai_evaluation": true, "include_screenshots": true, "auto_upload": true, "convert_txt_to_html": false}, "performance": {"monitoring_enabled": true, "collect_browser_console": true, "collect_network_requests": true, "response_time_tracking": true}, "debug": {"enabled": false, "collect_system_info": true, "save_debug_screenshots": true, "verbose_logging": false}}