# -*- coding: utf-8 -*-
import time
import sys
import os
import random
import json
import logging
import datetime
import requests
import traceback
import hmac
import hashlib
import base64
import urllib.parse
import mimetypes
from playwright.sync_api import sync_playwright, TimeoutError
import itertools
import threading
import psutil
import argparse
import io
import oss2
import xlsxwriter
from PIL import Image
import re
import uuid
import pytz
import shutil
from pathlib import Path
import zipfile
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment
from openpyxl.utils import get_column_letter

# 钉钉消息接收处理
RESTART_COMMAND = "重启"  # 钉钉机器人重启命令
RESTART_FLAG_FILE = "restart_flag.txt"  # 重启标志文件

# 阿里云OSS配置
# 使用前请先安装依赖: pip install oss2
oss_access_key_id = 'LTAI5t6yLR9SawePy4TxhihE'
oss_access_key_secret = '******************************'
endpoint = 'http://oss-cn-shanghai.aliyuncs.com'
bucket_name = 'cloudgame-test'
bucket_domain = 'cloudgame-test.oss-cn-shanghai.aliyuncs.com'  # OSS外网域名
folder_name = 'tk_automation'

# 文件转换配置
convert_text_to_html = False  # 是否将文本文件转换为HTML以便查看

# 配置日志
def setup_logging():
    log_dir = os.path.join(os.getcwd(), 'logs')
    os.makedirs(log_dir, exist_ok=True)
    log_file = os.path.join(log_dir, f'chat_log_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    
    # 配置根日志记录器
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )
    return log_file

# 上传图片或文本文件到OSS
def upload_image_to_oss(image_path, max_retries=3):
    """将图片或文本文件上传到OSS，返回可访问的URL"""
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            # 如果这是重试，添加随机延迟
            if retry_count > 0:
                retry_delay = random.uniform(1, 3)  # 1-3秒随机延迟
                logging.info(f"上传重试 {retry_count}/{max_retries}，等待 {retry_delay:.2f} 秒...")
                time.sleep(retry_delay)
            
            # 尝试导入oss2模块，如果未安装则提示安装
            try:
                import oss2
            except ImportError:
                logging.error("未找到oss2模块，请安装依赖: pip install oss2")
                return None
                
            logging.info(f"上传文件到OSS: {image_path}")
            
            # 检查文件是否存在
            if not os.path.exists(image_path):
                logging.error(f"要上传的文件不存在: {image_path}")
                return None
                
            # 创建OSS认证和Bucket对象
            auth = oss2.Auth(oss_access_key_id, oss_access_key_secret)
            bucket = oss2.Bucket(auth, endpoint, bucket_name)
            
            # 构建OSS对象名称，包含日期前缀和文件夹名
            date_prefix = datetime.datetime.now().strftime('%Y%m%d')
            filename = os.path.basename(image_path)
            object_name = f"{folder_name}/{date_prefix}/{filename}"
            
            # 确保目录结构存在（对象存储没有目录概念，但我们可以创建空对象表示目录）
            ensure_oss_dir(bucket, f"{folder_name}/{date_prefix}/")
            
            # 获取文件类型，为设置Content-Type做准备
            content_type = mimetypes.guess_type(image_path)[0]
            headers = {}
            # 新增：如果是png图片，强制设置Content-Type
            if filename.lower().endswith('.png'):
                content_type = 'image/png'
            # 如果是文本文件，特别处理
            if filename.endswith('.txt'):
                content_type = 'text/plain; charset=utf-8'
                headers['Content-Disposition'] = f'inline; filename="{filename}"'
                # 如果需要转换为HTML，则进行转换
                if convert_text_to_html:
                    # 创建HTML版本的文件
                    html_path = convert_txt_to_html(image_path)
                    if html_path:
                        # 上传HTML版本
                        html_object_name = f"{folder_name}/{date_prefix}/{os.path.basename(html_path)}"
                        html_content_type = 'text/html; charset=utf-8'
                        html_headers = {'Content-Type': html_content_type}
                        bucket.put_object_from_file(html_object_name, html_path, headers=html_headers)
                        # 修改返回的文件路径为HTML版本
                        object_name = html_object_name
                        content_type = html_content_type
                        # 删除临时HTML文件
                        try:
                            os.remove(html_path)
                        except:
                            pass
            
            if content_type:
                headers['Content-Type'] = content_type
            
            # 设置上传超时时间
            bucket.timeout = 600  # 设置30秒超时
            
            # 上传文件到OSS，带上内容类型
            result = bucket.put_object_from_file(object_name, image_path, headers=headers)
            
            # 检查上传结果
            if result.status == 200:
                # 构建可访问的URL
                url = f"https://{bucket_domain}/{object_name}"
                logging.info(f"文件上传成功: {url}")
                return url
            else:
                logging.error(f"文件上传失败，状态码: {result.status}")
                retry_count += 1
                continue
                
        except Exception as e:
            logging.error(f"上传文件到OSS失败 (尝试 {retry_count+1}/{max_retries}): {str(e)}")
            
            # 特别处理OSS权限相关错误
            if "AccessDenied" in str(e) or "403" in str(e):
                logging.error("OSS访问被拒绝，可能的原因：")
                logging.error("1. OSS访问密钥没有上传权限")
                logging.error("2. Bucket策略限制了当前用户的操作")
                logging.error("3. 文件路径或Bucket名称不正确")
                logging.error(f"当前尝试上传到: {object_name}")
                logging.error(f"使用的Bucket: {bucket_name}")
                logging.error(f"使用的Endpoint: {endpoint}")
                
                # 权限错误通常不需要重试，直接返回
                if retry_count >= max_retries - 1:
                    logging.error("OSS权限错误，停止重试")
                    return None
                    
            elif "SignatureDoesNotMatch" in str(e):
                logging.error("OSS签名验证失败，请检查AccessKey和AccessKeySecret是否正确")
                return None
                
            elif "NoSuchBucket" in str(e):
                logging.error(f"OSS Bucket不存在: {bucket_name}")
                return None
            
            if retry_count < max_retries - 1:
                retry_count += 1
                continue
            else:
                logging.error("已达到最大重试次数，上传失败")
                logging.error(traceback.format_exc())
                return None
    
    return None

def convert_txt_to_html(txt_path):
    """将文本文件转换为HTML格式，以便于浏览器查看"""
    try:
        # 生成HTML文件路径
        html_path = txt_path.replace('.txt', '.html')
        
        # 读取文本内容
        with open(txt_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 转义HTML特殊字符
        import html as html_module
        escaped_content = html_module.escape(content)
        
        # 替换换行符为<br>标签
        html_content = escaped_content.replace('\n', '<br>\n')
        
        # 添加样式的HTML模板
        html_template = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天记录</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}
        h1 {{
            color: #333;
            text-align: center;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }}
        .question {{
            color: #2c3e50;
            font-weight: bold;
        }}
        .answer {{
            color: #16a085;
        }}
        .error {{
            color: #e74c3c;
        }}
        .dialogue {{
            margin-bottom: 30px;
            border-bottom: 1px dashed #ddd;
            padding-bottom: 20px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>太古数字人聊天记录</h1>
        <div class="content">
"""
        
        # 处理内容转换，不在f-string中使用复杂操作
        processed_content = html_content
        processed_content = processed_content.replace('问: ', '<span class="question">问: </span>')
        processed_content = processed_content.replace('答: ', '<span class="answer">答: </span>')
        processed_content = processed_content.replace('错误: ', '<span class="error">错误: </span>')
        processed_content = processed_content.replace('对话 ', '<div class="dialogue"><strong>对话 ')
        processed_content = processed_content.replace('-'*40, '</strong>')
        processed_content = processed_content.replace('\n\n', '</div>\n\n')
        
        # 完成HTML模板
        html_template += processed_content
        html_template += """
        </div>
    </div>
</body>
</html>"""
        
        # 写入HTML文件
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_template)
            
        logging.info(f"成功将文本文件转换为HTML: {html_path}")
        return html_path
    except Exception as e:
        logging.error(f"转换文本到HTML失败: {str(e)}")
        logging.error(traceback.format_exc())
        return None

def ensure_oss_dir(bucket, dir_path):
    """确保OSS目录结构存在，不存在则创建"""
    try:
        # 对象存储没有目录的概念，但可以通过创建一个空对象来模拟目录
        # 检查目录是否存在
        if not dir_path.endswith('/'):
            dir_path += '/'
        
        # 优化：跳过目录检查，因为OSS会在上传文件时自动创建目录结构
        # 而且list_objects可能受到bucket policy限制
        logging.info(f"跳过OSS目录检查，上传时将自动创建: {dir_path}")
        return True
        
        # 原有的检查逻辑注释掉，避免权限问题
        # try:
        #     # 使用适当的迭代方式检查目录是否存在
        #     # 使用delimiter参数以目录方式列出对象
        #     objects = bucket.list_objects(prefix=dir_path, delimiter='/', max_keys=1)
        #     
        #     # 检查返回的结果
        #     if len(list(objects.object_list)) > 0:
        #         exist = True
        #         logging.info(f"OSS目录已存在: {dir_path}")
        #     else:
        #         logging.info(f"OSS目录不存在: {dir_path}")
        # except Exception as e:
        #     logging.warning(f"检查OSS目录时可能遇到权限限制: {str(e)}")
        #     # 权限不足时也继续执行，因为上传时会自动创建目录
        #     logging.info("继续执行上传，OSS将自动创建目录结构")
        #     return True
        
        # # 如果目录不存在，创建一个空对象表示目录
        # if not exist:
        #     logging.info(f"在OSS中创建目录: {dir_path}")
        #     bucket.put_object(dir_path, '') 
            
    except Exception as e:
        logging.warning(f"确保OSS目录存在时出错: {str(e)}")
        # 即使出错也继续上传，因为OSS会自动创建目录结构
        logging.info("继续执行上传，OSS将自动创建目录结构")
        return True

# 更新钉钉通知函数，添加回复服务器URL以及处理命令的功能
def send_dingtalk_notification(webhook_url, message, is_error=False, at_phone=None, image_url=None):
    """发送钉钉通知"""
    if not webhook_url or webhook_url == "DISABLE_NOTIFICATION":
        logging.info(f"[钉钉通知-已禁用] 消息内容: {message}")
        if at_phone:
            logging.info(f"[钉钉通知-已禁用] @用户: {at_phone}")
        if image_url:
            logging.info(f"[钉钉通知-已禁用] 图片URL: {image_url}")
        return
        
    headers = {'Content-Type': 'application/json'}
    
    # 图片消息
    if image_url:
        data = {
            "msgtype": "markdown",
            "markdown": {
                "title": "聊天机器人测试通知",
                "text": message + f"\n\n![screenshot]({image_url})"
            }
        }
    else:
        # 普通文本消息
        data = {
            "msgtype": "markdown",
            "markdown": {
                "title": "聊天机器人测试通知",
                "text": message
            }
        }
    
    # 添加 @ 通知
    if at_phone:
        data["at"] = {
            "atMobiles": [at_phone] if isinstance(at_phone, str) else at_phone,
            "isAtAll": False
        }
    
    try:
        response = requests.post(webhook_url, headers=headers, data=json.dumps(data))
        if response.status_code == 200:
            result = response.json()
            if result.get("errcode") == 0:
                logging.info(f"钉钉通知发送成功: {response.text}")
                return True
            else:
                logging.error(f"钉钉通知发送失败: {response.text}")
        else:
            logging.error(f"钉钉通知发送失败，状态码: {response.status_code}, 响应: {response.text}")
    except Exception as e:
        logging.error(f"发送钉钉通知时出错: {str(e)}")
    
    return False

def load_questions(questions_file=None, access_mode=None):
    """加载问题语料，支持新的统一格式和旧格式
    
    Args:
        questions_file: 问题文件路径，默认None使用默认文件
        access_mode: 访问模式，'random'或'sequential'，默认None使用文件中的设置或默认为sequential
    """
    single_questions = []
    multi_questions = []
    image_text_questions = []
    unified_questions = []  # 新的统一问题列表
    mode = access_mode
    
    # 如果未指定问题文件，则使用默认文件
    if questions_file is None:
        questions_file = "questions.json"
        logging.info(f"未指定问题文件，将使用默认文件: {questions_file}")
    
    # 加载JSON文件
    if os.path.exists(questions_file):
        try:
            with open(questions_file, "r", encoding="utf-8") as f:
                data = json.load(f)
            
            # 检查是否是新的统一格式
            if "questions" in data:
                logging.info("检测到新的统一问题格式")
                unified_questions = data.get("questions", [])
                
                # 为了兼容旧代码，将统一格式转换为分类格式
                for question_data in unified_questions:
                    # 兼容处理：支持字符串格式和对象格式
                    if isinstance(question_data, str):
                        # 字符串格式：直接作为single问题处理
                        single_questions.append(question_data)
                    elif isinstance(question_data, dict):
                        # 对象格式：按照question_type分类处理
                        question_type = question_data.get("question_type", "single")
                        content = question_data.get("content")
                        
                        if question_type == "single":
                            single_questions.append(content)
                        elif question_type == "multi":
                            multi_questions.append(content)
                        elif question_type == "image_text":
                            image_text_questions.append(content)
                        elif question_type == "ai_generated":
                            # AI生成问题保持在统一格式中处理，不转换为旧格式
                            pass
                        elif question_type == "ai_followup":
                            # AI追问对话保持在统一格式中处理，不转换为旧格式
                            pass
                        else:
                            logging.warning(f"未知的问题类型: {question_type}")
                    else:
                        logging.warning(f"不支持的问题数据格式: {type(question_data)}, 内容: {question_data}")
                
                logging.info(f"已从统一格式转换: {len(single_questions)} 个单问题")
                logging.info(f"已转换: {len(multi_questions)} 组多问题对话")
                logging.info(f"已转换: {len(image_text_questions)} 个图文问题")
            else:
                # 旧格式兼容
                logging.info("检测到旧的分类问题格式")
                single_questions = data.get("single_questions", [])
                multi_questions = data.get("multi_questions", [])
                image_text_questions = data.get("image_text_questions", [])
                
                logging.info(f"已从旧格式加载 {len(single_questions)} 个单问题")
                logging.info(f"已加载 {len(multi_questions)} 组多问题对话")
                logging.info(f"已加载 {len(image_text_questions)} 个图文问题")
            
            # 如果未指定访问模式，则使用文件中的设置
            if not mode:
                mode = data.get("mode", "sequential")
                
            logging.info(f"访问模式: {mode}")
            
        except Exception as e:
            logging.error(f"加载问题文件失败: {str(e)}")
            logging.error(traceback.format_exc())
    
    # 如果问题列表为空，尝试从单独的文件加载
    if not single_questions and not multi_questions and not image_text_questions:
        try:
            with open("single_questions.txt", "r", encoding="utf-8") as f:
                single_questions = [line.strip() for line in f.readlines() if line.strip()]
            logging.info(f"已加载 {len(single_questions)} 个单问题")
        except Exception as e:
            logging.error(f"加载单问题失败: {str(e)}")
            single_questions = ["你好", "介绍一下自己", "帮我推荐几款黑色包包"]
    
    if not multi_questions:
        try:
            with open("multi_questions.txt", "r", encoding="utf-8") as f:
                multi_questions = json.load(f)
            logging.info(f"已加载 {len(multi_questions)} 组多问题对话")
        except Exception as e:
            logging.error(f"加载多问题对话失败: {str(e)}")
            multi_questions = [["你好", "介绍一下自己", "你能做什么"]]
    
    # 如果还没有设置访问模式，默认使用sequential
    if not mode:
        mode = "sequential"
    
    # 返回四个值：三个分类列表和统一列表
    return single_questions, multi_questions, image_text_questions, unified_questions

def get_next_questions(single_questions, multi_questions, image_text_questions, used_single_indices, used_multi_indices, used_image_text_indices, mode="sequential"):
    """选择下一组问题
    
    Args:
        single_questions: 单问题列表
        multi_questions: 多问题组列表
        image_text_questions: 图文问题列表
        used_single_indices: 已使用的单问题索引集合
        used_multi_indices: 已使用的多问题组索引集合
        used_image_text_indices: 已使用的图文问题索引集合
        mode: 访问模式，'random'或'sequential'
    """
    if not single_questions and not multi_questions and not image_text_questions:
        return None
    
    # 检查是否所有问题都已使用过
    all_single_used = len(used_single_indices) >= len(single_questions)
    all_multi_used = len(used_multi_indices) >= len(multi_questions)
    all_image_text_used = len(used_image_text_indices) >= len(image_text_questions)
    
    # 如果所有问题都已使用，则返回None
    if all_single_used and all_multi_used and all_image_text_used:
        return None
    
    if mode == "random":
        # 随机模式 - 随机选择问题类型
        available_types = []
        if not all_single_used:
            available_types.append('single')
        if not all_multi_used:
            available_types.append('multi')
        if not all_image_text_used:
            available_types.append('image_text')
        
        if not available_types:
            return None
        
        question_type = random.choice(available_types)
        
        if question_type == 'single':
            # 选择未使用过的单问题
            available_indices = [i for i in range(len(single_questions)) if i not in used_single_indices]
            if not available_indices:
                return None
            
            selected_index = random.choice(available_indices)
            used_single_indices.add(selected_index)
            return {'type': 'text', 'questions': [single_questions[selected_index]]}
            
        elif question_type == 'multi':
            # 选择未使用过的多问题组
            available_indices = [i for i in range(len(multi_questions)) if i not in used_multi_indices]
            if not available_indices:
                return None
            
            selected_index = random.choice(available_indices)
            used_multi_indices.add(selected_index)
            return {'type': 'text', 'questions': multi_questions[selected_index]}
            
        else:  # image_text
            # 选择未使用过的图文问题
            available_indices = [i for i in range(len(image_text_questions)) if i not in used_image_text_indices]
            if not available_indices:
                return None
            
            selected_index = random.choice(available_indices)
            used_image_text_indices.add(selected_index)
            return {'type': 'image_text', 'question_data': image_text_questions[selected_index]}
    else:
        # 顺序模式 - 先使用所有单问题，再使用多问题，最后使用图文问题
        if not all_single_used:
            # 顺序选择未使用的单问题
            for i in range(len(single_questions)):
                if i not in used_single_indices:
                    used_single_indices.add(i)
                    return {'type': 'text', 'questions': [single_questions[i]]}
        elif not all_multi_used:
            # 顺序选择未使用的多问题组
            for i in range(len(multi_questions)):
                if i not in used_multi_indices:
                    used_multi_indices.add(i)
                    return {'type': 'text', 'questions': multi_questions[i]}
        else:
            # 顺序选择未使用的图文问题
            for i in range(len(image_text_questions)):
                if i not in used_image_text_indices:
                    used_image_text_indices.add(i)
                    return {'type': 'image_text', 'question_data': image_text_questions[i]}
        
        return None

def restart_chat_session(page, screenshots_dir, target_url):
    """尝试重新启动聊天会话"""
    logging.info("正在尝试重新启动聊天会话...")
    try:
        # 尝试直接导航到聊天页面
        page.goto(target_url, timeout=60000)
        page.wait_for_load_state("networkidle", timeout=30000)
        page.screenshot(path=os.path.join(screenshots_dir, "restart_home_page.png"))
        
        # 第一步：点击"点击开始"按钮
        logging.info("重启: 尝试点击'点击开始'按钮...")
        start_button_found = False
        try:
            start_button = page.locator('text="点击开始"').first
            if start_button.is_visible(timeout=10000):
                logging.info("重启: 找到'点击开始'按钮，点击开始流程")
                start_button.click(force=True, timeout=30000)
                start_button_found = True
                time.sleep(2)  # 等待页面响应
                page.screenshot(path=os.path.join(screenshots_dir, "restart_after_start_click.png"))
            else:
                logging.warning("重启: 未找到'点击开始'按钮，尝试其他启动方式")
        except Exception as e:
            logging.warning(f"重启: 点击'点击开始'按钮时出错: {str(e)}")
        
        # 第二步：检查是否存在"轻触开启聊天"按钮（5秒内）
        logging.info("重启: 检查是否存在'轻触开启聊天'按钮...")
        chat_button_found = False
        
        # 轮询检查按钮是否出现（因为按钮显示有延迟）
        max_wait_time = 5.0  # 最大等待5秒
        check_interval = 0.5  # 每0.5秒检查一次
        start_time = time.time()
        
        chat_selectors = [
            'text="轻触开启聊天"',
            'button:has-text("轻触开启聊天")',
            'div:has-text("轻触开启聊天")',
            'a:has-text("轻触开启聊天")',
            'text="开启聊天"',
            'button:has-text("开启聊天")',
            'div.button:has-text("开启聊天")',
            'a:has-text("开启聊天")',
            'div[role="button"]:has-text("聊天")',
            '.chat-button'
        ]
        
        while time.time() - start_time < max_wait_time and not chat_button_found:
            for selector in chat_selectors:
                try:
                    chat_button = page.locator(selector).first
                    if chat_button.is_visible(timeout=100):  # 很短的超时，快速检查
                        logging.info(f"重启: 找到'轻触开启聊天'按钮 (选择器: {selector})，点击进入聊天页面")
                        chat_button.click(force=True, timeout=30000)
                        chat_button_found = True
                        time.sleep(2)  # 等待页面响应
                        page.screenshot(path=os.path.join(screenshots_dir, "restart_after_chat_click.png"))
                        break
                except Exception as e:
                    logging.debug(f"重启: 轮询检查选择器 {selector} 失败: {str(e)}")
                    continue
            
            if not chat_button_found:
                time.sleep(check_interval)  # 等待后再次检查
        
        if not chat_button_found:
            elapsed_time = time.time() - start_time
            logging.info(f"重启: 轮询检查{elapsed_time:.1f}秒后未找到'轻触开启聊天'按钮，直接进入下一步")
        
        # 如果两个按钮都没找到，尝试点击页面中央偏上位置作为备用方案
        if not start_button_found and not chat_button_found:
            logging.warning("重启: 未找到任何启动按钮，尝试点击页面中央偏上位置...")
            try:
                size = page.viewport_size
                page.click('body', position={"x": size["width"] // 2, "y": size["height"] // 3})
                logging.info("重启: 已点击页面中央作为备用启动方式")
                time.sleep(2)
            except Exception as e:
                logging.error(f"重启: 点击页面中央时出错: {str(e)}")
        
        logging.info("重启: 启动流程完成，等待聊天界面加载...")
        
        # 第三步：等待聊天界面加载
        logging.info("等待聊天界面加载...")
        
        # 定义多个可能的输入框选择器
        input_selectors = [
            'input[placeholder="快来和我聊一聊..."]',
            'input[placeholder]',
            '.chat-input input', 
            'input.chat-input',
            'div.chat-footer input',
            'input[type="text"]',
            'textarea.chat-input',
            'textarea[placeholder]'
        ]
        
        # 尝试找到输入框
        input_found = False
        for attempt in range(3):  # 重试3次
            for selector in input_selectors:
                try:
                    if page.locator(selector).first.is_visible(timeout=10000):
                        logging.info(f"重启: 找到输入框: {selector}")
                        input_found = True
                        break
                except:
                    continue
                    
            if input_found:
                break
                
            if attempt < 2:  # 如果不是最后一次尝试
                logging.warning(f"重启: 第{attempt+1}次尝试未找到输入框，等待5秒后重试...")
                time.sleep(5)
                page.screenshot(path=os.path.join(screenshots_dir, f"restart_retry_find_input_{attempt+1}.png"))
        
        if input_found:
            logging.info("找到输入框，聊天界面加载完成")
            page.screenshot(path=os.path.join(screenshots_dir, "restart_input_ready.png"))
            logging.info("聊天会话已重新启动")
            return True
        else:
            # 在抛出异常前，尝试多次智能点击来激活界面
            logging.info("重启: 开始尝试多次智能点击来激活聊天界面...")
            click_attempts = 10  # 最多尝试10次
            click_interval = 3   # 每次间隔3秒
            
            for click_attempt in range(click_attempts):
                try:
                    # 使用智能点击来尝试激活聊天界面
                    button_clicked = smart_click_to_activate_chat(page, screenshots_dir, click_attempt)
                    
                    # 立即检查是否出现了输入框
                    input_found_after_click = False
                    for selector in input_selectors:
                        try:
                            if page.locator(selector).first.is_visible(timeout=2000):
                                logging.info(f"重启: 智能点击后找到输入框: {selector} (第{click_attempt + 1}次点击)")
                                input_found = True
                                input_found_after_click = True
                                break
                        except:
                            continue
                    
                    if input_found_after_click:
                        click_type = "智能按钮点击" if button_clicked else "备用位置点击"
                        logging.info(f"重启: 成功！经过{click_attempt + 1}次{click_type}后找到了输入框")
                        page.screenshot(path=os.path.join(screenshots_dir, "restart_input_found_after_clicks.png"))
                        logging.info("聊天会话已重新启动")
                        return True
                    
                    # 如果不是最后一次尝试，等待指定间隔
                    if click_attempt < click_attempts - 1:
                        logging.info(f"重启: 未找到输入框，{click_interval}秒后进行下一次点击尝试...")
                        time.sleep(click_interval)
                        
                except Exception as click_error:
                    logging.warning(f"重启: 第{click_attempt + 1}次智能点击尝试失败: {str(click_error)}")
                    if click_attempt < click_attempts - 1:
                        time.sleep(click_interval)
                    continue
            
            # 经过所有点击尝试后，如果仍未找到输入框，则抛出异常
            logging.error(f"重启: 经过{click_attempts}次点击尝试后，仍未找到输入框")
            page.screenshot(path=os.path.join(screenshots_dir, "restart_final_click_attempts_failed.png"))
            raise Exception("重启: 无法找到聊天界面输入框")
            
    except Exception as e:
        logging.error(f"重新启动聊天会话失败: {str(e)}")
        return False

# 捕获浏览器的console日志
def setup_console_logger(page, screenshots_dir):
    """设置console日志捕获"""
    # 添加时间戳到日志文件名
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    console_log_path = os.path.join(screenshots_dir, f"browser_console_{timestamp}.log")
    network_log_path = os.path.join(screenshots_dir, f"browser_network_{timestamp}.log")
    
    # 创建日志文件
    with open(console_log_path, 'w', encoding='utf-8') as f:
        f.write(f"浏览器Console日志 - {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("=" * 80 + "\n\n")
    
    with open(network_log_path, 'w', encoding='utf-8') as f:
        f.write(f"浏览器Network请求日志 - {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("=" * 80 + "\n\n")
    
    # 监听console事件
    page.on("console", lambda msg: log_console_message(msg, console_log_path))
    
    # 监听网络请求
    page.on("request", lambda request: log_network_request(request, network_log_path))
    page.on("response", lambda response: log_network_response(response, network_log_path))
    
    return console_log_path, network_log_path

def log_console_message(msg, log_path):
    """记录console消息到日志文件"""
    try:
        with open(log_path, 'a', encoding='utf-8') as f:
            log_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            f.write(f"[{log_time}] [{msg.type}] {msg.text}\n")
            
            # 如果有堆栈跟踪，也记录下来
            if msg.args:
                for arg in msg.args:
                    try:
                        # 尝试获取堆栈信息
                        val = arg.json_value()
                        if isinstance(val, str):
                            f.write(f"  Arg: {val}\n")
                        else:
                            f.write(f"  Arg: {json.dumps(val, ensure_ascii=False)}\n")
                    except:
                        pass
            f.write("\n")
    except Exception as e:
        logging.error(f"记录console消息时出错: {str(e)}")

def log_network_request(request, log_path):
    """记录网络请求到日志文件"""
    try:
        with open(log_path, 'a', encoding='utf-8') as f:
            log_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            f.write(f"[{log_time}] 请求: {request.method} {request.url}\n")
            
            # 记录请求头信息
            f.write("  请求头:\n")
            for key, value in request.headers.items():
                f.write(f"    {key}: {value}\n")
            
            # 尝试记录请求体 (如果可用)
            try:
                post_data = request.post_data
                if post_data:
                    f.write("  请求体:\n")
                    # 如果是JSON，尝试格式化显示
                    try:
                        json_data = json.loads(post_data)
                        formatted_json = json.dumps(json_data, indent=2, ensure_ascii=False)
                        f.write(f"    {formatted_json}\n")
                    except:
                        # 非JSON格式，直接显示
                        f.write(f"    {post_data}\n")
            except:
                pass
            
            f.write("\n")
    except Exception as e:
        logging.error(f"记录网络请求时出错: {str(e)}")

def should_log_response_body(response):
    """判断是否应该记录响应体内容"""
    try:
        content_type = response.headers.get('content-type', '').lower()
        url = response.url.lower()
        
        # JSON接口响应 - 需要记录
        if 'json' in content_type:
            return True
            
        # API接口路径 - 需要记录
        if '/api/' in url or '/gateway/' in url or url.endswith('/api') or 'api.' in url:
            return True
            
        # WebSocket升级响应 - 需要记录
        if response.status == 101:
            return True
            
        # 错误响应状态 - 需要记录以便调试
        if response.status >= 400:
            return True
            
        # 静态资源 - 不记录响应体
        static_content_types = [
            'text/html',
            'text/css', 
            'text/javascript',
            'application/javascript',
            'application/x-javascript',
            'text/plain',
            'image/',
            'font/',
            'audio/',
            'video/',
            'application/octet-stream',
            'application/wasm',
            'text/xml',
            'application/xml'
        ]
        
        for static_type in static_content_types:
            if static_type in content_type:
                return False
                
        # 静态资源文件扩展名 - 不记录响应体
        static_extensions = [
            '.js', '.css', '.html', '.htm', '.png', '.jpg', '.jpeg', '.gif', '.svg', 
            '.ico', '.woff', '.woff2', '.ttf', '.eot', '.mp4', '.mp3', '.pdf', '.zip'
        ]
        
        for ext in static_extensions:
            if url.endswith(ext) or ext + '?' in url:
                return False
        
        # 默认情况：如果是文本类型且不是静态资源，则记录
        if 'text' in content_type and content_type not in ['text/html', 'text/css', 'text/javascript']:
            return True
            
        return False
        
    except Exception as e:
        logging.debug(f"判断是否记录响应体时出错: {str(e)}")
        return False

def log_network_response(response, log_path):
    """记录网络响应到日志文件"""
    try:
        with open(log_path, 'a', encoding='utf-8') as f:
            log_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            f.write(f"[{log_time}] 响应: {response.status} {response.url}\n")
            
            # 记录响应头信息
            f.write("  响应头:\n")
            for key, value in response.headers.items():
                f.write(f"    {key}: {value}\n")
            
            # 判断是否需要记录响应体
            if should_log_response_body(response) and response.status != 204:
                try:
                    f.write("  响应体 (完整内容):\n")
                    resp_text = response.text()
                    
                    content_type = response.headers.get('content-type', '').lower()
                    # 如果是JSON，尝试格式化显示
                    if 'json' in content_type:
                        try:
                            json_data = json.loads(resp_text)
                            formatted_json = json.dumps(json_data, indent=2, ensure_ascii=False)
                            f.write(f"    {formatted_json}\n")
                        except:
                            f.write(f"    {resp_text}\n")
                    else:
                        f.write(f"    {resp_text}\n")
                except:
                    f.write("    无法获取响应体内容\n")
            else:
                content_type = response.headers.get('content-type', '')
                f.write(f"  响应体: [跳过记录 - 静态资源] Content-Type: {content_type}\n")
            
            f.write("\n")
    except Exception as e:
        logging.error(f"记录网络响应时出错: {str(e)}")

def collect_debug_information(page, browser, error, screenshots_dir, notification_files=None):
    """收集调试信息，包括截图、控制台日志和网络请求"""
    if notification_files is None:
        notification_files = []
    
    debug_info = {}
    oss_links = []
    local_files = {}
    
    try:
        # 截取当前页面截图
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        screenshot_path = os.path.join(screenshots_dir, f"error_{timestamp}.png")
        
        try:
            page.screenshot(path=screenshot_path)
            debug_info["screenshot"] = screenshot_path
            notification_files.append(screenshot_path)
            local_files["screenshot"] = screenshot_path
            
            # 上传截图到OSS
            oss_url = upload_image_to_oss(screenshot_path)
            if oss_url:
                oss_links.append(f"错误截图: {oss_url}")
                debug_info["screenshot_url"] = oss_url
                debug_info["has_screenshot"] = True
        except Exception as screenshot_error:
            logging.error(f"截图失败: {str(screenshot_error)}")
            debug_info["has_screenshot"] = False
        
        # 查找最新的控制台日志
        console_log_path = find_latest_log_file(screenshots_dir, "browser_console_", ".log")
        console_log_content = ""
        
        if console_log_path and os.path.exists(console_log_path):
            debug_info["console_log"] = console_log_path
            notification_files.append(console_log_path)
            local_files["console_log"] = console_log_path
            
            # 读取日志内容
            try:
                with open(console_log_path, 'r', encoding='utf-8') as f:
                    console_log_content = f.read()
            except Exception as log_read_error:
                logging.error(f"读取控制台日志失败: {str(log_read_error)}")
                console_log_content = f"无法读取日志: {str(log_read_error)}"
            
            # 上传控制台日志到OSS，确保以.txt结尾
            console_filename = os.path.basename(console_log_path).replace('.log', '.txt')
            oss_url = upload_text_to_oss(console_log_path, console_filename)
            if oss_url:
                oss_links.append(f"控制台日志: {oss_url}")
                debug_info["console_log_url"] = oss_url
        
        # 查找最新的网络请求日志
        network_log_path = find_latest_log_file(screenshots_dir, "browser_network_", ".log")
        network_log_content = ""
        
        if network_log_path and os.path.exists(network_log_path):
            debug_info["network_log"] = network_log_path
            notification_files.append(network_log_path)
            local_files["network_log"] = network_log_path
            
            # 读取日志内容
            try:
                with open(network_log_path, 'r', encoding='utf-8') as f:
                    network_log_content = f.read()
            except Exception as log_read_error:
                logging.error(f"读取网络请求日志失败: {str(log_read_error)}")
                network_log_content = f"无法读取日志: {str(log_read_error)}"
            
            # 上传网络请求日志到OSS，确保以.txt结尾
            network_filename = os.path.basename(network_log_path).replace('.log', '.txt')
            oss_url = upload_text_to_oss(network_log_path, network_filename)
            if oss_url:
                oss_links.append(f"网络请求日志: {oss_url}")
                debug_info["network_log_url"] = oss_url
        
        # 生成增强版HTML调试报告（直接包含日志内容）
        debug_report_path = os.path.join(screenshots_dir, f"debug_report_{timestamp}.html")
        
        # 将错误信息转换为HTML格式
        error_detail = str(error)
        error_html = error_detail.replace('\n', '<br>').replace(' ', '&nbsp;')
        
        # 处理日志内容以HTML格式显示
        def format_log_to_html(log_content):
            if not log_content:
                return "<p>没有日志内容</p>"
            
            # 转义HTML特殊字符
            import html as html_module
            escaped_content = html_module.escape(log_content)
            
            # 高亮警告和错误信息
            escaped_content = escaped_content.replace('[ERROR]', '<span class="log-error">[ERROR]</span>')
            escaped_content = escaped_content.replace('[WARN]', '<span class="log-warn">[WARN]</span>')
            escaped_content = escaped_content.replace('[WARNING]', '<span class="log-warn">[WARNING]</span>')
            escaped_content = escaped_content.replace('[INFO]', '<span class="log-info">[INFO]</span>')
            
            # 将换行符转换为<br>标签
            formatted_content = escaped_content.replace('\n', '<br>\n')
            
            return formatted_content
        
        # 准备截图HTML部分
        screenshot_html_content = ""
        if debug_info.get("has_screenshot") and "screenshot_url" in debug_info:
            screenshot_html_content = f'<div class="screenshot-box"><h2>错误截图</h2><img src="{debug_info["screenshot_url"]}" alt="错误截图" class="screenshot"></div>'
        else:
            screenshot_html_content = '<div class="error-box"><h2>错误截图</h2><p>无法获取错误截图</p></div>'
        
        # 资源链接HTML
        resource_links_html = ""
        for link in oss_links:
            parts = link.split(': ')
            if len(parts) == 2:
                resource_links_html += f'<li><a href="{parts[1]}" target="_blank">{parts[0]}</a></li>'
        
        # 创建HTML内容 - 使用字符串连接而不是f-string嵌套
        html_content = '''<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>聊天机器人调试报告 - TIMESTAMP</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background-color: #000;
            color: #fff;
            padding: 20px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }
        h1, h2, h3 {
            margin-top: 0;
        }
        h2 {
            border-bottom: 2px solid #ddd;
            padding-bottom: 10px;
            margin-top: 40px;
            color: #2c3e50;
        }
        .screenshot-box {
            margin: 20px 0;
            text-align: center;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .screenshot {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
        }
        .error-box {
            background-color: #fff0f0;
            border-left: 5px solid #e74c3c;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 3px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .log-container {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 3px;
            padding: 15px;
            margin: 20px 0;
            max-height: 500px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
            font-size: 14px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .log-error {
            color: #e74c3c;
            font-weight: bold;
        }
        .log-warn {
            color: #f39c12;
            font-weight: bold;
        }
        .log-info {
            color: #3498db;
        }
        .links-box {
            background-color: #fff;
            border-left: 5px solid #3498db;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 3px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .tab {
            overflow: hidden;
            border: 1px solid #ccc;
            background-color: #f1f1f1;
            border-top-left-radius: 3px;
            border-top-right-radius: 3px;
        }
        .tab button {
            background-color: inherit;
            float: left;
            border: none;
            outline: none;
            cursor: pointer;
            padding: 10px 16px;
            transition: 0.3s;
            font-size: 16px;
        }
        .tab button:hover {
            background-color: #ddd;
        }
        .tab button.active {
            background-color: #fff;
            border-bottom: 3px solid #3498db;
        }
        .tabcontent {
            display: none;
            padding: 20px;
            border: 1px solid #ccc;
            border-top: none;
            border-bottom-left-radius: 3px;
            border-bottom-right-radius: 3px;
            background-color: #fff;
        }
        .info-box {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 20px;
        }
        .info-item {
            flex: 1;
            min-width: 250px;
            background-color: #fff;
            padding: 15px;
            border-radius: 3px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .info-title {
            font-weight: bold;
            margin-bottom: 5px;
            color: #555;
        }
        .info-value {
            color: #333;
        }
        .json-syntax {
            color: #000;
        }
        .json-syntax .key {
            color: #881391;
        }
        .json-syntax .string {
            color: #a31515;
        }
        .json-syntax .number {
            color: #1c00cf;
        }
        .json-syntax .boolean {
            color: #0000ff;
        }
        .json-syntax .null {
            color: #0000ff;
        }
        .image-text-note{color:#888;font-size:11px;font-style:italic;margin-top:3px;}
        .question-image-container{margin:10px 0;padding:10px;border:1px solid #eee;border-radius:4px;background:#f9f9f9;}
        .question-image{max-width:300px;max-height:200px;border:1px solid #ddd;border-radius:4px;cursor:pointer;transition:box-shadow .2s;}
        .question-image:hover{box-shadow:0 0 8px #e74c3c55;}
    </style>
</head>
<body>
    <header>
        <h1>聊天机器人调试报告</h1>
        <p>时间: CURRENT_TIME</p>
    </header>
    
    <div class="container">
        <div class="info-box">
            <div class="info-item">
                <div class="info-title">报告生成时间</div>
                <div class="info-value">CURRENT_TIME</div>
            </div>
            <div class="info-item">
                <div class="info-title">调试ID</div>
                <div class="info-value">TIMESTAMP</div>
            </div>
            <div class="info-item">
                <div class="info-title">页面URL</div>
                <div class="info-value">PAGE_URL</div>
            </div>
        </div>
        
        <div class="error-box">
            <h2>错误信息</h2>
            <pre style="overflow-x: auto;">ERROR_HTML</pre>
        </div>
        
        SCREENSHOT_HTML
        
        <h2>调试日志</h2>
        <div class="tab">
            <button class="tablinks active" onclick="openLog(event, 'ConsoleLog')">控制台日志</button>
            <button class="tablinks" onclick="openLog(event, 'NetworkLog')">网络请求日志</button>
        </div>

        <div id="ConsoleLog" class="tabcontent" style="display: block;">
            <div class="log-container">
                CONSOLE_LOG_HTML
            </div>
        </div>

        <div id="NetworkLog" class="tabcontent">
            <div class="log-container">
                NETWORK_LOG_HTML
            </div>
        </div>
        
        <div class="links-box">
            <h2>调试资源链接</h2>
            <ul>
                RESOURCE_LINKS_HTML
            </ul>
        </div>
    </div>
    
    <script>
    function openLog(evt, logName) {
        var i, tabcontent, tablinks;
        
        // 隐藏所有标签内容
        tabcontent = document.getElementsByClassName("tabcontent");
        for (i = 0; i < tabcontent.length; i++) {
            tabcontent[i].style.display = "none";
        }
        
        // 移除所有标签按钮的"active"类
        tablinks = document.getElementsByClassName("tablinks");
        for (i = 0; i < tablinks.length; i++) {
            tablinks[i].className = tablinks[i].className.replace(" active", "");
        }
        
        // 显示当前标签，并将按钮标记为活动状态
        document.getElementById(logName).style.display = "block";
        evt.currentTarget.className += " active";
    }
    </script>
</body>
</html>'''
        
        # 替换模板中的变量
        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        html_content = html_content.replace('TIMESTAMP', timestamp)
        html_content = html_content.replace('CURRENT_TIME', current_time)
        html_content = html_content.replace('PAGE_URL', page.url)
        html_content = html_content.replace('ERROR_HTML', error_html)
        html_content = html_content.replace('SCREENSHOT_HTML', screenshot_html_content)
        html_content = html_content.replace('CONSOLE_LOG_HTML', format_log_to_html(console_log_content))
        html_content = html_content.replace('NETWORK_LOG_HTML', format_log_to_html(network_log_content))
        html_content = html_content.replace('RESOURCE_LINKS_HTML', resource_links_html)
        
        # 写入HTML文件
        with open(debug_report_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        # 上传HTML报告到OSS - 修改为.txt扩展名但保持HTML内容
        oss_url = upload_text_to_oss(debug_report_path, f"debug_report_{timestamp}.txt")
        if oss_url:
            oss_links.append(f"完整调试报告: {oss_url}")
            debug_info["debug_report"] = oss_url
        
    except Exception as debug_error:
        logging.error(f"收集调试信息失败: {str(debug_error)}")
        logging.error(traceback.format_exc())
    
    return debug_info, oss_links

def find_latest_log_file(directory, prefix, suffix):
    """查找具有指定前缀和后缀的最新日志文件"""
    try:
        matching_files = []
        for file in os.listdir(directory):
            if file.startswith(prefix) and file.endswith(suffix):
                file_path = os.path.join(directory, file)
                matching_files.append((file_path, os.path.getmtime(file_path)))
        
        # 如果找到匹配的文件，按修改时间排序并返回最新的
        if matching_files:
            latest_file = sorted(matching_files, key=lambda x: x[1], reverse=True)[0][0]
            logging.info(f"找到最新的{prefix}日志文件: {latest_file}")
            return latest_file
        else:
            logging.warning(f"未找到{prefix}日志文件")
            return None
    except Exception as e:
        logging.error(f"查找日志文件时出错: {str(e)}")
        return None

def upload_text_to_oss(local_file, oss_filename=None):
    """将文本文件上传到OSS并返回URL"""
    if not os.path.exists(local_file):
        logging.error(f"上传文件失败: 文件不存在 {local_file}")
        return None
    
    try:
        # 如果未指定OSS文件名，则使用本地文件名
        if not oss_filename:
            oss_filename = os.path.basename(local_file)
        
        # 确保文件名为小写（用户期望的格式）
        oss_filename = oss_filename.lower()
        
        # 特殊处理调试报告文件 - 即使扩展名是.txt，仍设置正确的Content-Type
        is_debug_report = False
        if "debug_report" in oss_filename:
            is_debug_report = True
        
        # 生成OSS路径
        timestamp = datetime.datetime.now().strftime("%Y%m%d")
        oss_path = f"debug/{timestamp}/{oss_filename}"
        
        # 直接读取二进制内容，避免编码问题
        with open(local_file, 'rb') as f:
            content = f.read()
        
        # 检查文件内容是否为HTML
        is_html_content = content.startswith(b'<!DOCTYPE html>') or content.startswith(b'<html>') or b'<html ' in content[:1000]
        
        # 使用二进制方式上传文件内容，但对于HTML文件使用特殊处理
        if is_debug_report or is_html_content:
            return upload_binary_to_oss(content, oss_path, '.html')
        else:
            return upload_binary_to_oss(content, oss_path, os.path.splitext(local_file)[1])
        
    except Exception as e:
        logging.error(f"上传文本文件到OSS失败: {str(e)}")
        logging.error(traceback.format_exc())
        return None

def upload_binary_to_oss(content, oss_path, extension=None):
    """将二进制内容上传到OSS并返回URL"""
    try:
        # OSS配置参数
        auth = oss2.Auth(oss_access_key_id, oss_access_key_secret)
        bucket = oss2.Bucket(auth, endpoint, bucket_name)
        
        # 根据扩展名设置Content-Type
        headers = {
            'Content-Disposition': 'inline'
        }
        
        # 特殊处理调试报告，即使扩展名是.txt，也设置为HTML类型
        if extension == '.html' or oss_path.endswith('.html') or 'debug_report' in oss_path:
            headers['Content-Type'] = 'text/html; charset=utf-8'
        elif extension == '.log' or oss_path.endswith('.log'):
            headers['Content-Type'] = 'text/plain; charset=utf-8'
        elif extension == '.txt' or oss_path.endswith('.txt'):
            headers['Content-Type'] = 'text/plain; charset=utf-8'
        else:
            headers['Content-Type'] = 'application/octet-stream'
        
        result = bucket.put_object(oss_path, content, headers=headers)
        
        if result.status == 200:
            # 生成可访问的URL
            url = f"https://{bucket_domain}/{oss_path}"
            logging.info(f"内容已上传到OSS: {url}")
            return url
        else:
            logging.error(f"上传内容到OSS失败: 状态码 {result.status}")
            return None
            
    except Exception as e:
        logging.error(f"上传内容到OSS失败: {str(e)}")
        
        # 特别处理OSS权限相关错误
        if "AccessDenied" in str(e) or "403" in str(e):
            logging.error("OSS访问被拒绝，可能的原因：")
            logging.error("1. OSS访问密钥没有上传权限")
            logging.error("2. Bucket策略限制了当前用户的操作")
            logging.error("3. 文件路径或Bucket名称不正确")
            logging.error(f"当前尝试上传到: {oss_path}")
            logging.error(f"使用的Bucket: {bucket_name}")
            logging.error(f"使用的Endpoint: {endpoint}")
            
        elif "SignatureDoesNotMatch" in str(e):
            logging.error("OSS签名验证失败，请检查AccessKey和AccessKeySecret是否正确")
            
        elif "NoSuchBucket" in str(e):
            logging.error(f"OSS Bucket不存在: {bucket_name}")
        
        logging.error(traceback.format_exc())
        return None

# 添加定时点击函数
def setup_periodic_center_click(page, screenshots_dir, interval_seconds=600):
    """设置定时点击页面中央偏上的功能
    
    Args:
        page: Playwright页面对象
        screenshots_dir: 截图保存目录
        interval_seconds: 点击间隔时间，默认600秒（10分钟）
    """
    stop_event = threading.Event()
    last_click_time = {'value': time.time()}  # 使用字典以便能在内部函数中修改
    click_count = {'value': 0}  # 同样使用字典
    
    def check_and_request_click():
        """检查是否需要点击，如果需要则请求主线程执行点击"""
        if stop_event.is_set():
            return
            
        current_time = time.time()
        # 如果距离上次点击已经过了间隔时间
        if current_time - last_click_time['value'] >= interval_seconds:
            try:
                click_count['value'] += 1
                logging.info(f"计划执行定时点击页面中央偏上位置（第{click_count['value']}次）")
                
                # 记录这次点击的时间，即使还没执行也先记录，避免连续触发
                last_click_time['value'] = current_time
                
                # 设置一个标记，告诉主循环需要执行点击
                return True
            except Exception as e:
                logging.error(f"计划定时点击时出错: {str(e)}")
        
        return False
    
    # 用于存储主线程需要执行的点击操作
    click_requested = {'value': False}
    
    def monitor_thread_func():
        """监控线程，定期检查是否需要点击"""
        while not stop_event.is_set():
            try:
                if check_and_request_click():
                    click_requested['value'] = True
                time.sleep(1)  # 每秒检查一次
            except Exception as e:
                logging.error(f"定时点击监控线程出错: {str(e)}")
    
    # 创建并启动监控线程
    monitor_thread = threading.Thread(target=monitor_thread_func)
    monitor_thread.daemon = True
    monitor_thread.start()
    
    logging.info(f"已启动定时点击监控，每{interval_seconds}秒点击一次页面中央偏上位置")
    
    # 返回停止事件和点击请求状态，供主线程使用
    return stop_event, click_requested

def execute_center_click(page, screenshots_dir, click_count):
    """在主线程中执行页面中央偏上位置的点击操作"""
    try:
        logging.info(f"执行定时点击页面中央偏上位置（第{click_count}次）")
        
        # 获取页面尺寸
        size = page.viewport_size
        center_x = size["width"] // 2
        # 将y坐标设置为页面高度的三分之一处
        upper_center_y = size["height"] // 3
        
        # 点击中央偏上位置
        page.click('body', position={"x": center_x, "y": upper_center_y}, force=True)
        
        # 可选：截图记录点击后的状态
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        screenshot_path = os.path.join(screenshots_dir, f"keep_alive_click_{timestamp}.png")
        page.screenshot(path=screenshot_path)
        
        logging.info(f"定时点击完成，点击位置: x={center_x}, y={upper_center_y}，页面状态已保存到：{screenshot_path}")
        return True
    except Exception as e:
        logging.error(f"执行定时点击页面中央偏上位置时出错: {str(e)}")

def smart_click_to_activate_chat(page, screenshots_dir, click_attempt):
    """智能点击来激活聊天界面
    
    Args:
        page: Playwright页面对象
        screenshots_dir: 截图目录
        click_attempt: 当前尝试次数
        
    Returns:
        bool: 是否成功点击了按钮
    """
    try:
        logging.info(f"第{click_attempt + 1}次智能点击尝试...")
        
        # 首先尝试查找并点击"点击开始"按钮
        start_button_clicked = False
        start_selectors = [
            'text="点击开始"',
            'button:has-text("点击开始")', 
            'div:has-text("点击开始")',
            'a:has-text("点击开始")',
            '[role="button"]:has-text("点击开始")',
            '.start-button',
            'button.start'
        ]
        
        for selector in start_selectors:
            try:
                start_button = page.locator(selector).first
                if start_button.is_visible(timeout=1000):
                    logging.info(f"找到'点击开始'按钮 (选择器: {selector})，执行点击")
                    start_button.click(force=True, timeout=5000)
                    start_button_clicked = True
                    page.screenshot(path=os.path.join(screenshots_dir, f"smart_click_start_{click_attempt + 1}_{time.strftime('%Y%m%d_%H%M%S')}.png"))
                    break
            except Exception as e:
                logging.debug(f"尝试点击'点击开始'按钮失败 (选择器: {selector}): {str(e)}")
                continue
        
        # 如果没找到"点击开始"按钮，尝试"轻触开启聊天"按钮
        chat_button_clicked = False
        if not start_button_clicked:
            chat_selectors = [
                'text="轻触开启聊天"',
                'button:has-text("轻触开启聊天")',
                'div:has-text("轻触开启聊天")',
                'a:has-text("轻触开启聊天")',
                'text="开启聊天"',
                'button:has-text("开启聊天")',
                'div:has-text("开启聊天")',
                'a:has-text("开启聊天")',
                '[role="button"]:has-text("聊天")',
                '.chat-button'
            ]
            
            for selector in chat_selectors:
                try:
                    chat_button = page.locator(selector).first
                    if chat_button.is_visible(timeout=1000):
                        logging.info(f"找到'轻触开启聊天'按钮 (选择器: {selector})，执行点击")
                        chat_button.click(force=True, timeout=5000)
                        chat_button_clicked = True
                        page.screenshot(path=os.path.join(screenshots_dir, f"smart_click_chat_{click_attempt + 1}_{time.strftime('%Y%m%d_%H%M%S')}.png"))
                        break
                except Exception as e:
                    logging.debug(f"尝试点击'轻触开启聊天'按钮失败 (选择器: {selector}): {str(e)}")
                    continue
        
        # 如果两个按钮都没找到，执行备用点击策略
        if not start_button_clicked and not chat_button_clicked:
            logging.info("未找到已知按钮，执行备用点击策略...")
            
            # 获取页面尺寸
            size = page.viewport_size
            
            # 尝试点击不同的位置来激活界面
            click_positions = [
                {"x": size["width"] // 2, "y": size["height"] // 2},      # 页面中央
                {"x": size["width"] // 2, "y": size["height"] // 3},      # 页面中上部
                {"x": size["width"] // 2, "y": size["height"] * 2 // 3}, # 页面中下部
                {"x": size["width"] // 3, "y": size["height"] // 2},      # 页面左中
                {"x": size["width"] * 2 // 3, "y": size["height"] // 2}  # 页面右中
            ]
            
            # 循环使用不同的点击位置
            click_pos = click_positions[click_attempt % len(click_positions)]
            
            # 点击页面
            page.click('body', position=click_pos, timeout=3000)
            logging.info(f"已点击页面位置: ({click_pos['x']}, {click_pos['y']})")
            page.screenshot(path=os.path.join(screenshots_dir, f"smart_click_fallback_{click_attempt + 1}_{time.strftime('%Y%m%d_%H%M%S')}.png"))
        
        # 等待界面响应
        time.sleep(1)
        
        return start_button_clicked or chat_button_clicked
        
    except Exception as e:
        logging.warning(f"智能点击过程中出错: {str(e)}")
        return False
        logging.error(traceback.format_exc())
        return False

def run_tk_chat(test_mode=False, questions_file=None, access_mode=None):
    # 设置日志
    log_file = setup_logging()
    
    # 读取环境变量中的URL或ticket配置
    target_url = os.environ.get('TARGET_URL')
    assigned_ticket = os.environ.get('ASSIGNED_TICKET')
    stress_test_mode = os.environ.get('STRESS_TEST_MODE', 'false').lower() == 'true'
    stress_process_id = os.environ.get('STRESS_PROCESS_ID', '0')
    # tkmates.swireproperties.com.cn
    # 如果没有从环境变量获取到URL，使用默认URL
    if not target_url:
        if assigned_ticket:
            # 如果有分配的ticket，构建URL
            target_url = f"https://tkmates.swireproperties.com.cn/?ticket={assigned_ticket}&mallCode=TKH&devtool=false"
            logging.info(f"使用分配的ticket构建URL: {target_url}")
        else:
            # 使用默认的硬编码URL
            target_url = "https://tkmates.swireproperties.com.cn/?ticket=61653113-3c5f-4bdb-9f73-3be15272d1as&mallCode=TKH&devtool=false"
            logging.info(f"使用默认URL: {target_url}")
    else:
        logging.info(f"使用环境变量中的URL: {target_url}")
    
    if stress_test_mode:
        logging.info(f"压力测试模式: 进程ID {stress_process_id}, 使用ticket: {assigned_ticket or '默认'}")
    
    # 创建截图目录
    screenshots_dir = os.path.join(os.getcwd(), 'screenshots')
    os.makedirs(screenshots_dir, exist_ok=True)
    
    # 创建结果目录
    results_dir = os.path.join(os.getcwd(), 'results')
    os.makedirs(results_dir, exist_ok=True)
    
    # 获取当前时间戳作为结果文件名
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = os.path.join(results_dir, f"conversation_results_{timestamp}.txt")
    
    # 初始化统计类
    stats = DialogueStats()
    stats.set_current_results_file(results_file)
    
    # 创建结果文件和写入标题
    with open(results_file, 'w', encoding='utf-8') as f:
        f.write(f"聊天机器人对话记录 - {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("=" * 80 + "\n\n")
    
    # 配置参数
    max_retry_count = 3  # 问题重试次数
    max_restart_failures = 3  # 最大连续重启失败次数
    # 不再限制对话轮次，允许无限进行直到所有问题都被问完
    
    # 间隔时间（秒）
    min_delay = 2 # 最小延迟
    max_delay = 3  # 最大延迟
    
    # 定义钉钉通知Webhook（请替换为您自己的URL）
    dingtalk_webhook = "https://oapi.dingtalk.com/robot/send?access_token=2ee3739f45ba4beeb3246b0fc4a2b5c605b5d461040d46d867dd1346ccc1578d"
    # 定义钉钉加签密钥（可选）
    #dingtalk_secret = "SECc710068d7e9122c2d86137e45d4a9a16941e47b9cb7e31c9055c92d2dd479aaf"
    
    # 通知手机号（钉钉机器人@对象）
    at_phone = "15021182310"
    
    # 初始化console日志和network日志路径
    console_log_path = os.path.join(screenshots_dir, "browser_console.log")
    network_log_path = os.path.join(screenshots_dir, "browser_network.log")
    
    # 在函数开头定义keep_alive_stop_event变量，确保在finally块中可以访问
    keep_alive_stop_event = None
    keep_alive_click_requested = None
    click_executed_count = 0
    
    try:
        # 加载问题集
        single_questions, multi_questions, image_text_questions, unified_questions = load_questions(questions_file, access_mode)
        # 使用默认的sequential模式，或者从命令行参数传入的模式
        question_mode = access_mode if access_mode else "sequential"
        
        # 优先使用统一问题格式
        use_unified_format = len(unified_questions) > 0
        
        if use_unified_format:
            logging.info(f"使用统一问题格式，共{len(unified_questions)}个问题")
            used_unified_indices = set()
            total_questions_count = len(unified_questions)
        else:
            logging.info(f"使用传统格式，单问题{len(single_questions)}个，多问题{len(multi_questions)}组，图文问题{len(image_text_questions)}个")
            # 随机选择问题集的索引
            used_single_indices = set()
            used_multi_indices = set()
            used_image_text_indices = set()
            total_questions_count = len(single_questions) + len(multi_questions) + len(image_text_questions)
        
        # 记录启动通知
        send_dingtalk_notification(
            dingtalk_webhook,
            # dingtalk_secret,
            f"### 🤖 聊天机器人测试启动\n" +
            f"**时间**: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n" +
            f"**模式**: {'测试模式（最多3轮对话）' if test_mode else '正常模式'}\n" +
            f"**问题数**: 单轮问题 {len(single_questions)}个, 多轮问题 {len(multi_questions)}组, 图文问题 {len(image_text_questions)}个, 统一问题 {len(unified_questions)}个",
            is_error=False
        )
        
        # 使用Playwright
        with sync_playwright() as p:
            logging.info("正在初始化Playwright...")
            
            # 尝试使用系统安装的Chrome
            chrome_paths = [
                '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',  # macOS路径
                'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',    # Windows路径
                'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe',
                '/usr/bin/google-chrome',  # Linux路径
            ]
            
            chrome_executable = None
            for path in chrome_paths:
                if os.path.exists(path):
                    chrome_executable = path
                    logging.info(f"找到Chrome浏览器: {path}")
                    break
            
            if chrome_executable:
                browser = p.chromium.launch(
                    headless=False,  # 设为True可以在后台运行
                    slow_mo=50,  # 稍微减慢操作速度，以便观察
                    executable_path=chrome_executable
                )
            else:
                logging.info("未找到系统Chrome，将使用内置浏览器")
                browser = p.chromium.launch(
                    headless=False,  # 设为True可以在后台运行
                    slow_mo=50,  # 稍微减慢操作速度，以便观察
                )
            device = p.devices['iPhone 13 Pro']

            # 创建浏览器上下文，使用简单的移动设备模拟
            context = browser.new_context(
                **device,  # 应用设备参数（包括视口、User-Agent等）
                locale="zh-CN",          # 设置语言环境
                timezone_id="Asia/Shanghai"  # 设置时区
            )
            
            
            # 创建页面
            page = context.new_page()
            
            # 设置console日志和network日志捕获
            console_log_path, network_log_path = setup_console_logger(page, screenshots_dir)
            
            # 主对话循环
            conversation_count = 0
            restart_failure_count = 0  # 重启失败计数
            
            try:
                # 首先打开聊天页面
                try:
                    logging.info("正在导航到首页...")
                    page.goto(target_url, timeout=60000)
                    logging.info("执行goto完成")
                    page.wait_for_load_state("networkidle", timeout=30000)
                    page.screenshot(path=os.path.join(screenshots_dir, "01_home_page.png"))
                    
                    # 第一步：点击"点击开始"按钮
                    logging.info("尝试点击'点击开始'按钮...")
                    start_button_found = False
                    try:
                        start_button = page.locator('text="点击开始"').first
                        if start_button.is_visible(timeout=10000):
                            logging.info("找到'点击开始'按钮，点击开始流程")
                            start_button.click(force=True, timeout=30000)
                            start_button_found = True
                            time.sleep(2)  # 等待页面响应
                            page.screenshot(path=os.path.join(screenshots_dir, "02_after_start_click.png"))
                        else:
                            logging.warning("未找到'点击开始'按钮，尝试其他启动方式")
                    except Exception as e:
                        logging.warning(f"点击'点击开始'按钮时出错: {str(e)}")
                    
                    # 第二步：检查是否存在"轻触开启聊天"按钮（5秒内）
                    logging.info("检查是否存在'轻触开启聊天'按钮...")
                    chat_button_found = False
                    
                    # 轮询检查按钮是否出现（因为按钮显示有延迟）
                    max_wait_time = 5.0  # 最大等待5秒
                    check_interval = 0.5  # 每0.5秒检查一次
                    start_time = time.time()
                    
                    chat_selectors = [
                        'text="轻触开启聊天"',
                        'button:has-text("轻触开启聊天")',
                        'div:has-text("轻触开启聊天")',
                        'a:has-text("轻触开启聊天")',
                        'text="开启聊天"',
                        'button:has-text("开启聊天")',
                        'div.button:has-text("开启聊天")',
                        'a:has-text("开启聊天")',
                        'div[role="button"]:has-text("聊天")',
                        '.chat-button'
                    ]
                    
                    while time.time() - start_time < max_wait_time and not chat_button_found:
                        for selector in chat_selectors:
                            try:
                                chat_button = page.locator(selector).first
                                if chat_button.is_visible(timeout=100):  # 很短的超时，快速检查
                                    logging.info(f"找到'轻触开启聊天'按钮 (选择器: {selector})，点击进入聊天页面")
                                    chat_button.click(force=True, timeout=30000)
                                    chat_button_found = True
                                    time.sleep(2)  # 等待页面响应
                                    page.screenshot(path=os.path.join(screenshots_dir, "03_after_chat_click.png"))
                                    break
                            except Exception as e:
                                logging.debug(f"轮询检查选择器 {selector} 失败: {str(e)}")
                                continue
                        
                        if not chat_button_found:
                            time.sleep(check_interval)  # 等待后再次检查
                    
                    if not chat_button_found:
                        elapsed_time = time.time() - start_time
                        logging.info(f"轮询检查{elapsed_time:.1f}秒后未找到'轻触开启聊天'按钮，直接进入下一步")
                    
                    # 如果两个按钮都没找到，尝试点击页面中央偏上位置作为备用方案
                    if not start_button_found and not chat_button_found:
                        logging.warning("未找到任何启动按钮，尝试点击页面中央偏上位置...")
                        try:
                            size = page.viewport_size
                            page.click('body', position={"x": size["width"] // 2, "y": size["height"] // 3})
                            logging.info("已点击页面中央作为备用启动方式")
                            time.sleep(2)
                        except Exception as e:
                            logging.error(f"点击页面中央时出错: {str(e)}")
                    
                    logging.info("启动流程完成，等待聊天界面加载...")
                    
                    # 第三步：等待聊天界面加载
                    logging.info("等待聊天界面加载...")
                    
                    # 定义多个可能的输入框选择器
                    input_selectors = [
                        'input[placeholder="快来和我聊一聊..."]',
                        'input[placeholder]',
                        '.chat-input input', 
                        'input.chat-input',
                        'div.chat-footer input',
                        'input[type="text"]',
                        'textarea.chat-input',
                        'textarea[placeholder]'
                    ]
                    
                    # 尝试找到输入框
                    input_field = None
                    for attempt in range(3):  # 重试3次
                        found = False
                        for selector in input_selectors:
                            try:
                                if page.locator(selector).first.is_visible(timeout=10000):
                                    logging.info(f"找到输入框: {selector}")
                                    input_field = page.locator(selector).first
                                    found = True
                                    break
                            except:
                                continue
                                
                        if found:
                            break
                            
                        if attempt < 2:  # 如果不是最后一次尝试
                            logging.warning(f"第{attempt+1}次尝试未找到输入框，等待5秒后重试...")
                            # 尝试点击页面以激活可能的交互
                            try:
                                size = page.viewport_size
                                page.click('body', position={"x": size["width"] // 2, "y": size["height"] // 2})
                            except:
                                pass
                            time.sleep(5)
                            page.screenshot(path=os.path.join(screenshots_dir, f"retry_find_input_{attempt+1}.png"))
                    
                    # 如果找到输入框，则聊天界面已加载完成
                    if input_field:
                        logging.info("聊天界面已加载完成，找到输入框")
                        page.screenshot(path=os.path.join(screenshots_dir, "04_chat_ready.png"))
                        
                        # 修改：设置定时点击监控，但实际点击在主线程执行
                        keep_alive_stop_event, keep_alive_click_requested = setup_periodic_center_click(page, screenshots_dir, interval_seconds=600)
                        logging.info("已启动页面保活功能，每10分钟点击一次页面中央偏上位置")
                    else:
                        # 如果找不到输入框，尝试检查页面状态并尝试继续
                        logging.warning("未找到任何已知的输入框，尝试判断页面是否已进入聊天状态...")
                        html_file = os.path.join(screenshots_dir, "page_html.html")
                        with open(html_file, 'w', encoding='utf-8') as f:
                            f.write(page.content())
                        logging.info("已保存页面HTML内容以供分析")
                        
                        # 尝试重新加载页面
                        logging.info("尝试重新加载页面...")
                        page.reload(timeout=30000)
                        page.wait_for_load_state("networkidle", timeout=20000)
                        page.screenshot(path=os.path.join(screenshots_dir, "after_reload.png"))
                        
                        # 再次尝试查找输入框
                        input_found = False
                        for selector in input_selectors:
                            try:
                                if page.locator(selector).first.is_visible(timeout=5000):
                                    logging.info(f"重新加载后找到输入框: {selector}")
                                    input_field = page.locator(selector).first
                                    input_found = True
                                    break
                            except:
                                continue
                        
                        if not input_found:
                            # 在抛出异常前，尝试多次智能点击来激活界面
                            logging.info("开始尝试多次智能点击来激活聊天界面...")
                            click_attempts = 10  # 最多尝试10次
                            click_interval = 3   # 每次间隔3秒
                            
                            for click_attempt in range(click_attempts):
                                try:
                                    # 使用智能点击来尝试激活聊天界面
                                    button_clicked = smart_click_to_activate_chat(page, screenshots_dir, click_attempt)
                                    
                                    # 立即检查是否出现了输入框
                                    input_found_after_click = False
                                    for selector in input_selectors:
                                        try:
                                            if page.locator(selector).first.is_visible(timeout=2000):
                                                logging.info(f"智能点击后找到输入框: {selector} (第{click_attempt + 1}次点击)")
                                                input_field = page.locator(selector).first
                                                input_found = True
                                                input_found_after_click = True
                                                break
                                        except:
                                            continue
                                    
                                    if input_found_after_click:
                                        click_type = "智能按钮点击" if button_clicked else "备用位置点击"
                                        logging.info(f"成功！经过{click_attempt + 1}次{click_type}后找到了输入框")
                                        break
                                    
                                    # 如果不是最后一次尝试，等待指定间隔
                                    if click_attempt < click_attempts - 1:
                                        logging.info(f"未找到输入框，{click_interval}秒后进行下一次点击尝试...")
                                        time.sleep(click_interval)
                                        
                                except Exception as click_error:
                                    logging.warning(f"第{click_attempt + 1}次智能点击尝试失败: {str(click_error)}")
                                    if click_attempt < click_attempts - 1:
                                        time.sleep(click_interval)
                                    continue
                            
                            # 经过所有点击尝试后，如果仍未找到输入框，则抛出异常
                            if not input_found:
                                logging.error(f"经过{click_attempts}次点击尝试后，仍未找到输入框")
                                page.screenshot(path=os.path.join(screenshots_dir, "final_click_attempts_failed.png"))
                                raise Exception("无法确认是否已进入聊天界面，未找到已知的输入框元素")
                except Exception as e:
                    # 记录错误并进行诊断，但尝试继续执行
                    logging.error(f"导航到首页时出错: {str(e)}")
                    error_screenshot_path = os.path.join(screenshots_dir, "error_navigation.png")
                    page.screenshot(path=error_screenshot_path)
                    
                    # 添加诊断信息
                    try:
                        current_url = page.url
                        current_title = page.title()
                        logging.error(f"错误发生时 - URL: {current_url}, 标题: {current_title}")
                        
                        # 保存页面HTML以便调试
                        html_file = os.path.join(screenshots_dir, "error_page_html.html")
                        with open(html_file, 'w', encoding='utf-8') as f:
                            f.write(page.content())
                        logging.error(f"错误页面HTML已保存到: {html_file}")
                    except Exception as diag_error:
                        logging.error(f"收集诊断信息时出错: {str(diag_error)}")
                    
                    # 简单重试一次导航
                    try:
                        logging.info("尝试重新导航到首页...")
                        page.goto(target_url, timeout=60000)
                        page.wait_for_load_state("networkidle", timeout=30000)
                        page.screenshot(path=os.path.join(screenshots_dir, "retry_home_page.png"))
                        
                        # 尝试直接进入聊天页面
                        logging.info("尝试直接进入聊天页面...")
                        # 构建聊天页面URL：取target_url的域名部分 + "/chat"
                        from urllib.parse import urlparse
                        parsed_url = urlparse(target_url)
                        chat_url = f"{parsed_url.scheme}://{parsed_url.netloc}/chat"
                        page.goto(chat_url, timeout=60000)
                        page.wait_for_load_state("networkidle", timeout=30000)
                        page.screenshot(path=os.path.join(screenshots_dir, "direct_chat_page.png"))
                        
                        # 检查是否有输入框
                        input_found = False
                        for selector in input_selectors:
                            try:
                                if page.locator(selector).first.is_visible(timeout=5000):
                                    logging.info(f"直接导航后找到输入框: {selector}")
                                    input_found = True
                                    
                                    # 找到输入框后启动保活功能
                                    if not keep_alive_stop_event:
                                        keep_alive_stop_event, keep_alive_click_requested = setup_periodic_center_click(page, screenshots_dir, interval_seconds=600)
                                        logging.info("重试导航成功后启动页面保活功能")
                                    
                                    break
                            except:
                                continue
                        
                        if not input_found:
                            # 在抛出异常前，尝试多次智能点击来激活界面
                            logging.info("重试导航: 开始尝试多次智能点击来激活聊天界面...")
                            click_attempts = 10  # 最多尝试10次
                            click_interval = 3   # 每次间隔3秒
                            
                            for click_attempt in range(click_attempts):
                                try:
                                    # 使用智能点击来尝试激活聊天界面
                                    button_clicked = smart_click_to_activate_chat(page, screenshots_dir, click_attempt)
                                    
                                    # 立即检查是否出现了输入框
                                    input_found_after_click = False
                                    for selector in input_selectors:
                                        try:
                                            if page.locator(selector).first.is_visible(timeout=2000):
                                                logging.info(f"重试导航: 智能点击后找到输入框: {selector} (第{click_attempt + 1}次点击)")
                                                input_found = True
                                                input_found_after_click = True
                                                
                                                # 找到输入框后启动保活功能
                                                if not keep_alive_stop_event:
                                                    keep_alive_stop_event, keep_alive_click_requested = setup_periodic_center_click(page, screenshots_dir, interval_seconds=600)
                                                    logging.info("重试导航点击成功后启动页面保活功能")
                                                
                                                break
                                        except:
                                            continue
                                    
                                    if input_found_after_click:
                                        click_type = "智能按钮点击" if button_clicked else "备用位置点击"
                                        logging.info(f"重试导航: 成功！经过{click_attempt + 1}次{click_type}后找到了输入框")
                                        page.screenshot(path=os.path.join(screenshots_dir, "retry_navigation_input_found_after_clicks.png"))
                                        break
                                    
                                    # 如果不是最后一次尝试，等待指定间隔
                                    if click_attempt < click_attempts - 1:
                                        logging.info(f"重试导航: 未找到输入框，{click_interval}秒后进行下一次点击尝试...")
                                        time.sleep(click_interval)
                                        
                                except Exception as click_error:
                                    logging.warning(f"重试导航: 第{click_attempt + 1}次智能点击尝试失败: {str(click_error)}")
                                    if click_attempt < click_attempts - 1:
                                        time.sleep(click_interval)
                                    continue
                            
                            # 经过所有点击尝试后，如果仍未找到输入框，则抛出异常
                            if not input_found:
                                logging.error(f"重试导航: 经过{click_attempts}次点击尝试后，仍未找到输入框")
                                page.screenshot(path=os.path.join(screenshots_dir, "retry_navigation_final_click_attempts_failed.png"))
                                raise Exception("无法进入聊天界面: 未找到已知的输入框元素")
                    except Exception as retry_error:
                        logging.error(f"重试导航到聊天页面失败: {str(retry_error)}")
                        raise
                
                # 开始向聊天机器人提问
                total_start_time = time.time()
                
                # 持续聊天，直到所有问题都已提问或出错
                # 在测试模式下限制对话数量
                max_conversations = 1 if test_mode else None
                
                while True:
                    # 检查测试模式下的对话数量限制
                    if test_mode and conversation_count >= max_conversations:
                        logging.info(f"测试模式：已达到最大对话数限制 {max_conversations} 轮，结束测试")
                        break
                        
                    # 检查是否需要执行点击操作（在主线程中执行）
                    if keep_alive_click_requested and keep_alive_click_requested['value']:
                        click_executed_count += 1
                        execute_center_click(page, screenshots_dir, click_executed_count)
                        keep_alive_click_requested['value'] = False
                        
                    # 获取下一组问题
                    if use_unified_format:
                        next_questions = get_next_question_unified(
                            unified_questions, used_unified_indices, question_mode
                        )
                    else:
                        next_questions = get_next_questions(
                            single_questions, multi_questions, image_text_questions, 
                            used_single_indices, used_multi_indices, used_image_text_indices,
                            question_mode
                        )
                    
                    if not next_questions:
                        logging.info("所有问题已发送完毕")
                        break
                    
                    conversation_count += 1
                    logging.info(f"\n===== 开始第 {conversation_count} 轮对话 =====")
                    
                    # 记录本次对话内容
                    conversation_log = []
                    
                    # 执行对话，允许重试
                    retry_count = 0
                    dialogue_success = False
                    
                    while not dialogue_success and retry_count < max_retry_count:
                        try:
                            # 检查问题类型并处理图文问题
                            if next_questions['type'] == 'image_text':
                                # 处理图文问题
                                question_data = next_questions['question_data']
                                text_message = question_data['text']
                                image_info = question_data['image']
                                image_type = question_data.get('imageType', 'url')
                                
                                logging.info(f"处理图文问题: {text_message}")
                                logging.info(f"图片: {image_info} (类型: {image_type})")
                                
                                # 准备图片文件
                                local_image_path = None
                                if image_type == 'url':
                                    # 从URL下载图片
                                    local_image_path = download_image_from_url(image_info, 
                                                                              os.path.join(screenshots_dir, "downloaded_images"))
                                else:
                                    # 本地文件
                                    local_image_path = image_info
                                    if not os.path.isabs(local_image_path):
                                        # 相对路径，转换为绝对路径
                                        local_image_path = os.path.abspath(local_image_path)
                                
                                if local_image_path and validate_image_file(local_image_path):
                                    # 首先将原始图片上传到OSS，以便在报告中展示
                                    logging.info("正在上传原始问题图片到OSS...")
                                    original_image_oss_url = upload_image_to_oss(local_image_path)
                                    if original_image_oss_url:
                                        logging.info(f"原始问题图片已上传到OSS: {original_image_oss_url}")
                                    else:
                                        logging.warning("原始问题图片上传到OSS失败")
                                    
                                    # 上传图片到聊天界面
                                    logging.info("正在上传图片到聊天界面...")
                                    upload_success = upload_image_to_chat(page, local_image_path, screenshots_dir)
                                    
                                    if upload_success:
                                        logging.info("图片上传成功，现在发送文本消息")
                                        conversation_log.append(f"问: [图片] {text_message}")
                                        # 记录图片路径信息，便于后续报告生成时使用
                                        conversation_log.append(f"图片路径: {local_image_path}")
                                        # 记录图片OSS URL，便于在报告中展示
                                        if original_image_oss_url:
                                            conversation_log.append(f"问题图片: {original_image_oss_url}")
                                        
                                        # 发送文本消息
                                        try:
                                            chat_input = page.locator('input[placeholder="快来和我聊一聊..."]').first
                                            if chat_input and chat_input.is_visible(timeout=15000):
                                                chat_input.fill(text_message)
                                                logging.info(f"已在输入框中填入: '{text_message}'")
                                                
                                                # 点击发送按钮
                                                send_button = page.locator('button:has(img[alt="箭头向上"])').first
                                                if send_button:
                                                    send_button.click()
                                                    logging.info("点击发送按钮")
                                                else:
                                                    logging.info("未找到发送按钮，尝试使用Enter键发送")
                                                    chat_input.press("Enter")
                                                
                                                # 等待回复（与普通文本消息相同的逻辑）
                                                logging.info("等待数字人回复...")
                                                reply_wait_start = time.time()
                                                time.sleep(1)
                                                max_reply_wait = 150
                                                stop_btn_selector = 'img[alt="停止"]'
                                                try:
                                                    page.wait_for_selector(stop_btn_selector, state="hidden", timeout=max_reply_wait*1000)
                                                    reply_wait_end = time.time()
                                                    reply_wait_time = reply_wait_end - reply_wait_start
                                                    logging.info(f"AI回复等待时长: {reply_wait_time:.2f}秒")
                                                    conversation_log.append(f"回复等待时长: {reply_wait_time:.2f}秒")
                                                except Exception as e:
                                                    reply_wait_end = time.time()
                                                    reply_wait_time = reply_wait_end - reply_wait_start
                                                    logging.warning(f"AI回复等待超时({max_reply_wait}s): {str(e)}")
                                                    conversation_log.append(f"回复等待时长: >{max_reply_wait}秒")
                                                
                                                # 截图并获取回复
                                                page.screenshot(path=os.path.join(screenshots_dir, f"conv{conversation_count}_image_text_reply.png"))
                                                
                                                # 获取回复内容
                                                try:
                                                    response_element = page.locator('#ai-agent-wrapper > div.max-w-3xl.mx-auto.relative.mb-6.max-h-44.px-2.overflow-hidden > div > div > div.max-w-none > p').first
                                                    
                                                    if not response_element or not response_element.is_visible():
                                                        response_element = page.locator('//*[@id="ai-agent-wrapper"]/div[1]/div/div/div[1]/p').first
                                                    
                                                    if not response_element or not response_element.is_visible():
                                                        response_element = page.locator('p.text-sm.text-justify').first
                                                    
                                                    if not response_element or not response_element.is_visible():
                                                        response_elements = page.locator("div.text-wrap p").all()
                                                        if response_elements and len(response_elements) > 0:
                                                            latest_response = response_elements[-1].text_content()
                                                        else:
                                                            logging.info("未找到回复内容")
                                                            conversation_log.append("答: [未能获取回复]")
                                                    else:
                                                        latest_response = response_element.text_content()
                                                        logging.info(f"数字人回复: {latest_response}")
                                                        conversation_log.append(f"答: {latest_response}")
                                                        
                                                    # AI回答质量评测
                                                    reply_screenshot_timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
                                                    reply_screenshot_path = os.path.join(screenshots_dir, f"conv{conversation_count}_image_text_reply_{reply_screenshot_timestamp}.png")
                                                    page.screenshot(path=reply_screenshot_path)
                                                    reply_oss_url = upload_image_to_oss(reply_screenshot_path)
                                                    if reply_oss_url:
                                                        conversation_log.append(f"截图: {reply_oss_url}")
                                                except Exception as e:
                                                    logging.error(f"获取回复内容出错: {str(e)}")
                                                    conversation_log.append("答: [获取回复出错]")
                                                
                                            else:
                                                logging.error("未找到输入框")
                                                conversation_log.append("答: [未找到输入框]")
                                                raise Exception("图文消息：未找到输入框")
                                        except Exception as e:
                                            logging.error(f"发送图文消息文本部分时出错: {str(e)}")
                                            raise e
                                    else:
                                        logging.error("图片上传失败")
                                        conversation_log.append("错误: 图片上传失败")
                                        raise Exception("图片上传失败")
                                else:
                                    logging.error(f"图片文件无效或不存在: {local_image_path}")
                                    conversation_log.append("错误: 图片文件无效")
                                    raise Exception("图片文件无效或不存在")
                                
                                # 图文问题处理完成
                                dialogue_success = True
                                
                            elif next_questions['type'] == 'ai_followup':
                                # 处理AI追问对话
                                metadata = next_questions['metadata']
                                domain = metadata.get('domain', '')
                                scenario = metadata.get('scenario', '')
                                provider_name = metadata.get('provider')
                                
                                total_rounds = next_questions['total_rounds']
                                round_type = next_questions['round_type']
                                current_round = next_questions['current_round']
                                conversation_history = next_questions['conversation_history']
                                
                                logging.info(f"AI追问对话 - 领域: {domain}, 场景: {scenario}")
                                logging.info(f"轮次配置: {round_type}, 当前: {current_round}/{total_rounds}")
                                
                                # 如果是第一轮，使用初始问题
                                if current_round == 1:
                                    current_question = next_questions['initial_question']
                                    logging.info(f"第{current_round}轮(初始问题): {current_question}")
                                else:
                                    # 生成追问问题
                                    followup_result = generate_followup_question(
                                        conversation_history, domain, scenario, 
                                        current_round, total_rounds, round_type, provider_name
                                    )
                                    
                                    if followup_result and followup_result.get('question'):
                                        current_question = followup_result['question']
                                        should_end = followup_result.get('should_end', False)
                                        reason = followup_result.get('reason', '')
                                        
                                        logging.info(f"第{current_round}轮(AI生成): {current_question}")
                                        logging.info(f"生成原因: {reason}")
                                        
                                        # 如果AI判断应该结束
                                        if should_end and round_type == "ai_judge":
                                            logging.info("AI判断对话应该结束")
                                            current_question = current_question  # 发送最后一个问题
                                    else:
                                        # AI生成失败，使用默认问题
                                        current_question = f"这是第{current_round}轮追问，还有其他问题吗？"
                                        logging.warning("AI生成追问失败，使用默认问题")
                                
                                # 发送当前问题
                                # 在进行对话前确保保活功能已启动
                                if not keep_alive_stop_event:
                                    keep_alive_stop_event, keep_alive_click_requested = setup_periodic_center_click(page, screenshots_dir, interval_seconds=600)
                                    logging.info("在AI追问对话开始前启动页面保活功能")
                                
                                # 等待一段时间，确保页面稳定
                                time.sleep(3)
                                
                                # 记录问题信息
                                conversation_log.append(f"问: {current_question}")
                                conversation_log.append(f"AI追问对话 - 领域: {domain}, 场景: {scenario}, 轮次: {current_round}/{total_rounds}")
                                
                                try:
                                    # 发送问题
                                    input_timeout = 15000  # 15秒
                                    chat_input = page.locator('input[placeholder="快来和我聊一聊..."]').first
                                    if not chat_input or not chat_input.is_visible(timeout=input_timeout):
                                        raise Exception("找不到可见的输入框")
                                        
                                    # 输入消息
                                    chat_input.fill(current_question)
                                    logging.info(f"已在输入框中填入: '{current_question}'")
                                    
                                    # 点击发送按钮
                                    send_button = page.locator('button:has(img[alt="箭头向上"])').first
                                    if send_button:
                                        send_button.click()
                                        logging.info("点击发送按钮")
                                    else:
                                        logging.info("未找到发送按钮，尝试使用Enter键发送")
                                        chat_input.press("Enter")
                                    
                                    # 等待回复
                                    logging.info("等待数字人回复...")
                                    reply_wait_start = time.time()
                                    time.sleep(1)
                                    max_reply_wait = 150
                                    stop_btn_selector = 'img[alt="停止"]'
                                    try:
                                        page.wait_for_selector(stop_btn_selector, state="hidden", timeout=max_reply_wait*1000)
                                        reply_wait_end = time.time()
                                        reply_wait_time = reply_wait_end - reply_wait_start
                                        logging.info(f"AI回复等待时长: {reply_wait_time:.2f}秒")
                                        conversation_log.append(f"回复等待时长: {reply_wait_time:.2f}秒")
                                    except Exception as e:
                                        reply_wait_end = time.time()
                                        reply_wait_time = reply_wait_end - reply_wait_start
                                        logging.warning(f"AI回复等待超时({max_reply_wait}s): {str(e)}")
                                        conversation_log.append(f"回复等待时长: >{max_reply_wait}秒")
                                    
                                    # 获取回复内容
                                    current_answer = ""
                                    try:
                                        response_element = page.locator('#ai-agent-wrapper > div.max-w-3xl.mx-auto.relative.mb-6.max-h-44.px-2.overflow-hidden > div > div > div.max-w-none > p').first
                                        
                                        if not response_element or not response_element.is_visible():
                                            response_element = page.locator('//*[@id="ai-agent-wrapper"]/div[1]/div/div/div[1]/p').first
                                        
                                        if not response_element or not response_element.is_visible():
                                            response_element = page.locator('p.text-sm.text-justify').first
                                        
                                        if not response_element or not response_element.is_visible():
                                            response_elements = page.locator("div.text-wrap p").all()
                                            if response_elements and len(response_elements) > 0:
                                                current_answer = response_elements[-1].text_content()
                                            else:
                                                logging.info("未找到回复内容")
                                                current_answer = "[未能获取回复]"
                                                conversation_log.append("答: [未能获取回复]")
                                        else:
                                            current_answer = response_element.text_content()
                                            logging.info(f"数字人回复: {current_answer}")
                                            conversation_log.append(f"答: {current_answer}")
                                            
                                            # AI回答质量评测
                                            try:
                                                evaluation_result = evaluate_ai_response(
                                                    current_question, 
                                                    current_answer, 
                                                    conversation_history[-3:] if conversation_history else None  # 传递最近3轮对话历史
                                                )
                                                if evaluation_result:
                                                    # 记录评测结果
                                                    conversation_log.append(f"🔍 AI回答评测:")
                                                    conversation_log.append(f"综合评分: {evaluation_result.get('overall_score', 'N/A')}/5")
                                                    
                                                    # 记录各维度评分
                                                    detailed_scores = evaluation_result.get('detailed_scores', {})
                                                    for dimension, score_info in detailed_scores.items():
                                                        if isinstance(score_info, dict) and 'score' in score_info:
                                                            conversation_log.append(f"{dimension}: {score_info['score']}/5 - {score_info.get('reason', '')}")
                                                    
                                                    if evaluation_result.get('evaluation_summary'):
                                                        conversation_log.append(f"评价总结: {evaluation_result['evaluation_summary']}")
                                                    
                                                    if evaluation_result.get('suggestions'):
                                                        conversation_log.append(f"改进建议: {evaluation_result['suggestions']}")
                                                        
                                                    # 将评测结果添加到对话历史中
                                                    if conversation_history:
                                                        conversation_history[-1]['evaluation'] = evaluation_result
                                                    
                                                    logging.info(f"✅ AI回答评测完成，综合评分: {evaluation_result.get('overall_score', 'N/A')}/5")
                                                else:
                                                    logging.debug("🔍 AI回答评测未启用或失败")
                                            except Exception as eval_error:
                                                logging.error(f"AI回答评测出错: {str(eval_error)}")
                                            
                                        # 截图并上传
                                        reply_screenshot_timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
                                        reply_screenshot_path = os.path.join(screenshots_dir, f"conv{conversation_count}_followup_r{current_round}_reply_{reply_screenshot_timestamp}.png")
                                        page.screenshot(path=reply_screenshot_path)
                                        reply_oss_url = upload_image_to_oss(reply_screenshot_path)
                                        if reply_oss_url:
                                            conversation_log.append(f"截图: {reply_oss_url}")
                                    except Exception as e:
                                        logging.error(f"获取回复内容出错: {str(e)}")
                                        current_answer = "[获取回复出错]"
                                        conversation_log.append("答: [获取回复出错]")
                                    
                                    # 保存对话历史
                                    conversation_history.append({
                                        'question': current_question,
                                        'answer': current_answer
                                    })
                                    
                                    # 判断是否继续下一轮
                                    should_continue = True
                                    
                                    # 检查结束条件
                                    if round_type == "fixed" and current_round >= total_rounds:
                                        should_continue = False
                                        logging.info(f"固定轮次对话完成: {current_round}/{total_rounds}")
                                    elif round_type == "random" and current_round >= total_rounds:
                                        should_continue = False
                                        logging.info(f"随机轮次对话完成: {current_round}/{total_rounds}")
                                    elif round_type == "ai_judge":
                                        if current_round >= total_rounds:
                                            should_continue = False
                                            logging.info(f"AI判断对话达到最大轮次: {current_round}/{total_rounds}")
                                        elif current_round > 1:  # 不是第一轮才检查AI判断
                                            if 'followup_result' in locals() and followup_result and followup_result.get('should_end'):
                                                should_continue = False
                                                logging.info("AI判断对话应该结束")
                                    
                                    if should_continue and current_round < total_rounds:
                                        # 准备下一轮 - 修复：在原地进行循环，不重新插入队列
                                        logging.info(f"准备进行第{current_round + 1}轮追问")
                                        
                                        # 继续下一轮对话循环
                                        while should_continue and current_round < total_rounds:
                                            current_round += 1
                                            
                                            # 生成下一轮问题
                                            followup_result = generate_followup_question(
                                                conversation_history, domain, scenario, 
                                                current_round, total_rounds, round_type, provider_name
                                            )
                                            
                                            if followup_result and followup_result.get('question'):
                                                current_question = followup_result['question']
                                                should_end = followup_result.get('should_end', False)
                                                reason = followup_result.get('reason', '')
                                                
                                                logging.info(f"第{current_round}轮(AI生成): {current_question}")
                                                logging.info(f"生成原因: {reason}")
                                                
                                                # 如果AI判断应该结束
                                                if should_end and round_type == "ai_judge":
                                                    logging.info("AI判断对话应该结束")
                                                    break
                                            else:
                                                # AI生成失败，使用默认问题
                                                current_question = f"这是第{current_round}轮追问，还有其他问题吗？"
                                                logging.warning("AI生成追问失败，使用默认问题")
                                            
                                            # 发送问题并获取回复
                                            conversation_log.append(f"问: {current_question}")
                                            conversation_log.append(f"AI追问对话 - 领域: {domain}, 场景: {scenario}, 轮次: {current_round}/{total_rounds}")
                                            # 添加AI生成原因到对话日志
                                            reason = followup_result.get('reason', '默认追问') if followup_result else '生成失败'
                                            if reason:
                                                conversation_log.append(f"📝 生成原因: {reason}")
                                            
                                            # 等待一段时间，确保页面稳定
                                            time.sleep(3)
                                            
                                            try:
                                                # 发送问题
                                                chat_input = page.locator('input[placeholder="快来和我聊一聊..."]').first
                                                if not chat_input or not chat_input.is_visible(timeout=15000):
                                                    raise Exception("找不到可见的输入框")
                                                    
                                                # 输入消息
                                                chat_input.fill(current_question)
                                                logging.info(f"已在输入框中填入: '{current_question}'")
                                                
                                                # 点击发送按钮
                                                send_button = page.locator('button:has(img[alt="箭头向上"])').first
                                                if send_button:
                                                    send_button.click()
                                                    logging.info("点击发送按钮")
                                                else:
                                                    logging.info("未找到发送按钮，尝试使用Enter键发送")
                                                    chat_input.press("Enter")
                                                
                                                # 等待回复
                                                logging.info("等待数字人回复...")
                                                reply_wait_start = time.time()
                                                time.sleep(1)
                                                max_reply_wait = 150
                                                stop_btn_selector = 'img[alt="停止"]'
                                                try:
                                                    page.wait_for_selector(stop_btn_selector, state="hidden", timeout=max_reply_wait*1000)
                                                    reply_wait_end = time.time()
                                                    reply_wait_time = reply_wait_end - reply_wait_start
                                                    logging.info(f"AI回复等待时长: {reply_wait_time:.2f}秒")
                                                    conversation_log.append(f"回复等待时长: {reply_wait_time:.2f}秒")
                                                except Exception as e:
                                                    reply_wait_end = time.time()
                                                    reply_wait_time = reply_wait_end - reply_wait_start
                                                    logging.warning(f"AI回复等待超时({max_reply_wait}s): {str(e)}")
                                                    conversation_log.append(f"回复等待时长: >{max_reply_wait}秒")
                                                
                                                # 获取回复内容
                                                current_answer = ""
                                                try:
                                                    response_element = page.locator('#ai-agent-wrapper > div.max-w-3xl.mx-auto.relative.mb-6.max-h-44.px-2.overflow-hidden > div > div > div.max-w-none > p').first
                                                    
                                                    if not response_element or not response_element.is_visible():
                                                        response_element = page.locator('//*[@id="ai-agent-wrapper"]/div[1]/div/div/div[1]/p').first
                                                    
                                                    if not response_element or not response_element.is_visible():
                                                        response_element = page.locator('p.text-sm.text-justify').first
                                                    
                                                    if not response_element or not response_element.is_visible():
                                                        response_elements = page.locator("div.text-wrap p").all()
                                                        if response_elements and len(response_elements) > 0:
                                                            current_answer = response_elements[-1].text_content()
                                                        else:
                                                            logging.info("未找到回复内容")
                                                            current_answer = "[未能获取回复]"
                                                            conversation_log.append("答: [未能获取回复]")
                                                    else:
                                                        current_answer = response_element.text_content()
                                                        logging.info(f"数字人回复: {current_answer}")
                                                        conversation_log.append(f"答: {current_answer}")
                                                        
                                                    # 截图并上传
                                                    reply_screenshot_timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
                                                    reply_screenshot_path = os.path.join(screenshots_dir, f"conv{conversation_count}_followup_r{current_round}_reply_{reply_screenshot_timestamp}.png")
                                                    page.screenshot(path=reply_screenshot_path)
                                                    reply_oss_url = upload_image_to_oss(reply_screenshot_path)
                                                    if reply_oss_url:
                                                        conversation_log.append(f"截图: {reply_oss_url}")
                                                except Exception as e:
                                                    logging.error(f"获取回复内容出错: {str(e)}")
                                                    current_answer = "[获取回复出错]"
                                                    conversation_log.append("答: [获取回复出错]")
                                                
                                                # 保存对话历史
                                                conversation_history.append({
                                                    'question': current_question,
                                                    'answer': current_answer
                                                })
                                                
                                                # 检查结束条件
                                                if round_type == "fixed" and current_round >= total_rounds:
                                                    should_continue = False
                                                    logging.info(f"固定轮次对话完成: {current_round}/{total_rounds}")
                                                elif round_type == "random" and current_round >= total_rounds:
                                                    should_continue = False
                                                    logging.info(f"随机轮次对话完成: {current_round}/{total_rounds}")
                                                elif round_type == "ai_judge":
                                                    if current_round >= total_rounds:
                                                        should_continue = False
                                                        logging.info(f"AI判断对话达到最大轮次: {current_round}/{total_rounds}")
                                                    elif 'followup_result' in locals() and followup_result and followup_result.get('should_end'):
                                                        should_continue = False
                                                        logging.info("AI判断对话应该结束")
                                                
                                            except Exception as e:
                                                logging.error(f"AI追问对话出错: {str(e)}")
                                                should_continue = False
                                                break
                                        
                                        dialogue_success = True
                                    else:
                                        dialogue_success = True
                                    
                                    logging.info("AI追问对话完成")
                                
                                except Exception as e:
                                    logging.error(f"AI追问对话出错: {str(e)}")
                                    raise e
                                
                            else:
                                # 处理普通文本问题（原有逻辑）
                                for i, message in enumerate(next_questions['questions']):
                                    # 检查是否需要执行点击操作（在主线程中执行）
                                    if keep_alive_click_requested and keep_alive_click_requested['value']:
                                        click_executed_count += 1
                                        execute_center_click(page, screenshots_dir, click_executed_count)
                                        keep_alive_click_requested['value'] = False
                                    
                                    # 检查输入框是否可用
                                    logging.info(f"第{i+1}个问题: 发送 '{message}'")
                                    
                                    # 在进行对话前确保保活功能已启动
                                    if not keep_alive_stop_event:
                                        keep_alive_stop_event, keep_alive_click_requested = setup_periodic_center_click(page, screenshots_dir, interval_seconds=600)
                                        logging.info("在对话开始前启动页面保活功能")
                                
                                    # 等待一段时间，确保页面稳定
                                    time.sleep(3)
                                    
                                    # 记录问题（支持AI生成问题的元数据）
                                    if next_questions.get('type') == 'ai_generated' and next_questions.get('metadata'):
                                        metadata = next_questions['metadata']
                                        conversation_log.append(f"问: {message}")
                                        conversation_log.append(f"AI生成问题 - 领域: {metadata.get('domain', '')}, 意图: {metadata.get('intent_type', '')}")
                                        if metadata.get('fallback'):
                                            conversation_log.append("备注: AI生成失败，使用示例问题")
                                    elif next_questions.get('type') == 'ai_followup' and next_questions.get('metadata'):
                                        metadata = next_questions['metadata']
                                        conversation_log.append(f"问: {message}")
                                        round_info = f"{next_questions.get('current_round', 1)}/{next_questions.get('total_rounds', 1)}"
                                        conversation_log.append(f"AI追问对话 - 领域: {metadata.get('domain', '')}, 场景: {metadata.get('scenario', '')}, 轮次: {round_info}")
                                    else:
                                        conversation_log.append(f"问: {message}")
                                    
                                    # 尝试查找输入框
                                    try:
                                        # 设置更短的超时时间
                                        input_timeout = 15000  # 15秒
                                        
                                        # 尝试找到输入框
                                        chat_input = page.locator('input[placeholder="快来和我聊一聊..."]').first
                                        if not chat_input or not chat_input.is_visible(timeout=input_timeout):
                                            raise Exception("找不到可见的输入框")
                                            
                                        # 输入消息
                                        chat_input.fill(message)
                                        logging.info(f"已在输入框中填入: '{message}'")
                                        
                                        # 点击发送按钮
                                        send_button = page.locator('button:has(img[alt="箭头向上"])').first
                                        if send_button:
                                            send_button.click()
                                            logging.info("点击发送按钮")
                                        else:
                                            logging.info("未找到发送按钮，尝试使用Enter键发送")
                                            chat_input.press("Enter")
                                        
                                        # 等待回复
                                        logging.info("等待数字人回复...")
                                        reply_wait_start = time.time()
                                        time.sleep(1)
                                        # 等待发送按钮重新出现或变为可用，最长90秒
                                        max_reply_wait = 150
                                        # 新的等待逻辑：等待"停止连接"按钮消失
                                        stop_btn_selector = 'img[alt="停止"]'
                                        try:
                                            page.wait_for_selector(stop_btn_selector, state="hidden", timeout=max_reply_wait*1000)
                                            reply_wait_end = time.time()
                                            reply_wait_time = reply_wait_end - reply_wait_start
                                            logging.info(f"AI回复等待时长: {reply_wait_time:.2f}秒")
                                            conversation_log.append(f"回复等待时长: {reply_wait_time:.2f}秒")
                                        except Exception as e:
                                            reply_wait_end = time.time()
                                            reply_wait_time = reply_wait_end - reply_wait_start
                                            logging.warning(f"AI回复等待超时({max_reply_wait}s): {str(e)}")
                                            conversation_log.append(f"回复等待时长: >{max_reply_wait}秒")
                                        
                                        # 截图并获取回复
                                        page.screenshot(path=os.path.join(screenshots_dir, f"conv{conversation_count}_q{i+1}_reply.png"))
                                        
                                        # 获取回复内容
                                        try:
                                            response_element = page.locator('#ai-agent-wrapper > div.max-w-3xl.mx-auto.relative.mb-6.max-h-44.px-2.overflow-hidden > div > div > div.max-w-none > p').first
                                            
                                            if not response_element or not response_element.is_visible():
                                                response_element = page.locator('//*[@id="ai-agent-wrapper"]/div[1]/div/div/div[1]/p').first
                                            
                                            if not response_element or not response_element.is_visible():
                                                response_element = page.locator('p.text-sm.text-justify').first
                                            
                                            if not response_element or not response_element.is_visible():
                                                response_elements = page.locator("div.text-wrap p").all()
                                                if response_elements and len(response_elements) > 0:
                                                    latest_response = response_elements[-1].text_content()
                                                else:
                                                    logging.info("未找到回复内容")
                                                    conversation_log.append("答: [未能获取回复]")
                                            else:
                                                latest_response = response_element.text_content()
                                                logging.info(f"数字人回复: {latest_response}")
                                                conversation_log.append(f"答: {latest_response}")
                                                
                                            # 截图并上传
                                            reply_screenshot_timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
                                            reply_screenshot_path = os.path.join(screenshots_dir, f"conv{conversation_count}_q{i+1}_reply_{reply_screenshot_timestamp}.png")
                                            page.screenshot(path=reply_screenshot_path)
                                            reply_oss_url = upload_image_to_oss(reply_screenshot_path)
                                            if reply_oss_url:
                                                conversation_log.append(f"截图: {reply_oss_url}")
                                        except Exception as e:
                                            logging.error(f"获取回复内容出错: {str(e)}")
                                            conversation_log.append("答: [获取回复出错]")
                                        
                                        # 尝试获取回复内容
                                        try:
                                            # 使用用户提供的更精确的选择器
                                            # 方法1: 使用CSS选择器
                                            response_element = page.locator('#ai-agent-wrapper > div.max-w-3xl.mx-auto.relative.mb-6.max-h-44.px-2.overflow-hidden > div > div > div.max-w-none > p').first
                                            
                                            # 方法2: 使用XPath
                                            if not response_element or not response_element.is_visible():
                                                response_element = page.locator('//*[@id="ai-agent-wrapper"]/div[1]/div/div/div[1]/p').first
                                            
                                            # 方法3: 使用更通用的选择器（备用）
                                            if not response_element or not response_element.is_visible():
                                                response_element = page.locator('p.text-sm.text-justify').first
                                            
                                            # 最后尝试原始方法
                                            if not response_element or not response_element.is_visible():
                                                response_elements = page.locator("div.text-wrap p").all()
                                                if response_elements and len(response_elements) > 0:
                                                    latest_response = response_elements[-1].text_content()
                                                else:
                                                    logging.info("未找到回复内容")
                                                    conversation_log.append("答: [未能获取回复]")
                                            else:
                                                latest_response = response_element.text_content()
                                                logging.info(f"数字人回复: {latest_response}")
                                                conversation_log.append(f"答: {latest_response}")
                                            # 新增：每次获取到回复内容后截图并上传OSS，并记录到对话日志
                                            reply_screenshot_timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
                                            reply_screenshot_path = os.path.join(screenshots_dir, f"conv{conversation_count}_q{i+1}_reply_{reply_screenshot_timestamp}.png")
                                            page.screenshot(path=reply_screenshot_path)
                                            reply_oss_url = upload_image_to_oss(reply_screenshot_path)
                                            if reply_oss_url:
                                                conversation_log.append(f"截图: {reply_oss_url}")
                                        except Exception as e:
                                            logging.error(f"获取回复内容出错: {str(e)}")
                                            conversation_log.append("答: [获取回复出错]")
                                        logging.info(f"第{i+1}个问题完成")
                                            
                                    except TimeoutError as te:
                                        logging.error(f"操作超时: {str(te)}")
                                        error_screenshot_path = os.path.join(screenshots_dir, f"conv{conversation_count}_q{i+1}_timeout.png")
                                        page.screenshot(path=error_screenshot_path)
                                        conversation_log.append(f"错误: 操作超时 - {str(te)}")
                                        raise Exception(f"操作超时：{str(te)}")
                                    except Exception as e:
                                        logging.error(f"发送消息时出错: {str(e)}")
                                        # 错误情况下截图
                                        error_screenshot_path = os.path.join(screenshots_dir, f"conv{conversation_count}_q{i+1}_error.png")
                                        page.screenshot(path=error_screenshot_path)
                                        conversation_log.append(f"错误: {str(e)}")
                                        raise e
                                        
                                    # 在对话间稍作停顿
                                    if i < len(next_questions['questions']) - 1:
                                        time.sleep(5)
                                
                            # 如果到达这里，表示该轮对话成功
                            dialogue_success = True
                        
                        except Exception as e:
                            retry_count += 1
                            error_detail = traceback.format_exc()
                            logging.error(f"发送问题时出错: {str(e)}")
                            logging.error(error_detail)
                            
                            # 记录错误时的浏览器状态
                            error_screenshot_path = os.path.join(screenshots_dir, f"error_send_question_conv{conversation_count}_try{retry_count}.png")
                            try:
                                page.screenshot(path=error_screenshot_path)
                                error_image_url = upload_image_to_oss(error_screenshot_path)
                            except:
                                error_image_url = None
                            
                            # 捕获浏览器调试信息
                            debug_info, oss_links = collect_debug_information(page, browser, str(e), screenshots_dir)
                            
                            # 上传console日志和network日志
                            console_log_url = upload_text_to_oss(console_log_path, "browser_console.txt")
                            network_log_url = upload_text_to_oss(network_log_path, "browser_network.txt")
                            
                            # 上传当前结果文件，以便查看出错前的对话内容
                            error_results_url = None
                            if os.path.exists(stats.current_results_file):
                                error_results_url = upload_text_to_oss(stats.current_results_file, os.path.basename(stats.current_results_file))
                                if error_results_url:
                                    logging.info(f"错误情况下的结果文件已上传: {error_results_url}")
                                    stats.current_results_url = error_results_url
                            
                            # 构建错误通知消息
                            error_message = (
                                f"### ❌ 聊天机器人测试错误\n" + 
                                f"**时间**: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n" +
                                f"**错误**: 第 {conversation_count} 轮对话失败 尝试: {retry_count}/{max_retry_count}\n" +
                                f"**详情**: {str(e)}\n"
                            )
                            
                            # 根据问题类型添加问题详情
                            if next_questions['type'] == 'image_text':
                                question_data = next_questions['question_data']
                                error_message += f"**问题**: 图文问题 - {question_data['text']}"
                            else:
                                error_message += f"**问题**: {next_questions['questions'] if 'questions' in next_questions else '多轮对话'}"
                            
                            # 如果有结果文件，添加链接
                            if stats.current_results_url:
                                error_message += f"\n\n**[查看详细对话记录]({stats.current_results_url})**"
                            
                            # 只使用OSS链接，而不是本地路径
                            if debug_info.get("debug_report"):
                                error_message += f"\n\n**[查看浏览器调试信息]({debug_info['debug_report']})**"
                            
                            if console_log_url:
                                error_message += f"\n\n**[查看浏览器Console日志]({console_log_url})**"
                            
                            if network_log_url:
                                error_message += f"\n\n**[查看网络请求日志]({network_log_url})**"
                            
                          
                            # 仅在最后一次重试时发送通知
                            if retry_count >= max_retry_count:
                                send_dingtalk_notification(
                                    dingtalk_webhook, 
                                    # dingtalk_secret,
                                    error_message,
                                    is_error=True,
                                    at_phone=at_phone,
                                    image_url=error_image_url
                                )
                                
                                stats.add_conversation(success=False)
                                # 根据问题类型正确统计失败的问题数量
                                if next_questions['type'] == 'image_text':
                                    # 图文问题只有一个问题
                                    stats.add_question(success=False)
                                elif next_questions['type'] == 'ai_followup':
                                    # AI追问对话每轮算一个问题
                                    stats.add_question(success=False)
                                else:
                                    # 文本问题可能有多个
                                    for _ in next_questions['questions']:
                                        stats.add_question(success=False)
                                    
                                # 写入对话结果
                                with open(results_file, 'a', encoding='utf-8') as f:
                                    f.write(f"【对话 {conversation_count} - 失败】\n")
                                    f.write(f"时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                                    if next_questions['type'] == 'image_text':
                                        question_data = next_questions['question_data']
                                        f.write(f"问题: 图文问题 - {question_data['text']}\n")
                                        f.write(f"图片: {question_data['image']}\n")
                                    elif next_questions['type'] == 'ai_followup':
                                        f.write(f"问题: AI追问对话 - 领域: {next_questions['metadata']['domain']}, 场景: {next_questions['metadata']['scenario']}, 轮次: {next_questions['current_round']}/{next_questions['total_rounds']}\n")
                                    else:
                                        for i, q in enumerate(next_questions['questions'], 1):
                                            f.write(f"问题{i}: {q}\n")
                                    f.write(f"错误: {str(e)}\n")
                                    f.write("\n" + "-" * 50 + "\n\n")
                                
                                # 尝试重启会话
                                if restart_chat_session(page, screenshots_dir, target_url):
                                    logging.info("已重启聊天会话")
                                    
                                                                    # 重启成功后重新启动保活功能
                                if keep_alive_stop_event:
                                    # 停止旧的监控线程
                                    if isinstance(keep_alive_stop_event, tuple):
                                        old_stop_event, _ = keep_alive_stop_event
                                        old_stop_event.set()
                                    else:
                                        keep_alive_stop_event.set()
                                    logging.info("停止旧的页面保活功能")
                                    time.sleep(1)  # 给一点时间让线程停止
                                    
                                    # 创建新的保活监控
                                    try:
                                        keep_alive_stop_event, keep_alive_click_requested = setup_periodic_center_click(
                                            page, screenshots_dir, interval_seconds=600
                                        )
                                        logging.info("重启聊天会话后启动新的页面保活功能，每10分钟点击一次页面中央偏上位置")
                                    except Exception as ke:
                                        logging.error(f"重启后设置保活功能时出错: {str(ke)}")
                                        logging.error(traceback.format_exc())
                                        # 继续执行，即使保活功能设置失败
                                    
                                    # 重启成功，重置失败计数
                                    restart_failure_count = 0
                                else:
                                    logging.error("重启聊天会话失败，终止测试")
                                    # 增加重启失败计数
                                    restart_failure_count += 1
                                    logging.warning(f"重启失败次数: {restart_failure_count}/{max_restart_failures}")
                                    
                                    # 检查是否超过最大重启失败次数
                                    if restart_failure_count >= max_restart_failures:
                                        error_msg = f"重启聊天会话连续失败 {restart_failure_count} 次，超过最大限制 {max_restart_failures} 次，脚本将终止"
                                        logging.critical(error_msg)
                                        break
                        
                    # 记录本次对话结果
                    with open(results_file, "a", encoding="utf-8") as f:
                        f.write(f"对话 {conversation_count}\n")
                        f.write("-" * 40 + "\n")
                        for line in conversation_log:
                            f.write(line + "\n")
                        f.write("\n")
                    
                    # 计算已处理的问题数
                    if use_unified_format:
                        processed_questions = len(used_unified_indices)
                        total_questions_for_log = len(unified_questions)
                    else:
                        processed_questions = len(used_single_indices) + len(used_multi_indices) + len(used_image_text_indices)
                        total_questions_for_log = len(single_questions) + len(multi_questions) + len(image_text_questions)
                    
                    logging.info(f"已处理 {processed_questions}/{total_questions_for_log} 组问题")
                    
                    # 更新统计数据
                    stats.add_conversation(success=dialogue_success)
                    # 根据问题类型正确统计问题数量
                    if next_questions['type'] == 'image_text':
                        # 图文问题只有一个问题
                        stats.add_question(success=dialogue_success)
                    elif next_questions['type'] == 'ai_followup':
                        # AI追问对话每轮算一个问题
                        stats.add_question(success=dialogue_success)
                    else:
                        # 文本问题可能有多个
                        for _ in next_questions['questions']:
                            stats.add_question(success=dialogue_success)
                    
                    # 根据时间间隔发送统计通知
                    if stats.should_send_notification(3600):
                        # 检查是否需要执行点击操作（在主线程中执行）
                        if keep_alive_click_requested and keep_alive_click_requested['value']:
                            click_executed_count += 1
                            execute_center_click(page, screenshots_dir, click_executed_count)
                            keep_alive_click_requested['value'] = False
                        
                        # 截取当前页面作为统计图片
                        stats_screenshot_path = os.path.join(screenshots_dir, f"stats_{time.strftime('%Y%m%d_%H%M%S')}.png")
                        page.screenshot(path=stats_screenshot_path)
                        stats.set_last_screenshot(stats_screenshot_path)
                        
                        # 上传截图到OSS
                        image_url = upload_image_to_oss(stats_screenshot_path)
                        
                        # 上传当前结果文件到OSS
                        if os.path.exists(stats.current_results_file):
                            results_url = upload_text_to_oss(stats.current_results_file, os.path.basename(stats.current_results_file))
                            if results_url:
                                stats.set_current_results_file(stats.current_results_file, results_url)
                                logging.info(f"结果文件已上传: {results_url}")
                        
                        # 发送带图片的统计通知
                        send_dingtalk_notification(
                            dingtalk_webhook, 
                            # dingtalk_secret,
                            stats.get_stats_message(),
                            image_url=image_url
                        )
                    
                    # 休息一会，准备下一轮对话，同时检查是否需要点击
                    for _ in range(5):  # 休息5秒
                        if keep_alive_click_requested and keep_alive_click_requested['value']:
                            click_executed_count += 1
                            execute_center_click(page, screenshots_dir, click_executed_count)
                            keep_alive_click_requested['value'] = False
                        time.sleep(1)
                
                # 最后一轮对话后，多等待一段时间以确保完成
                logging.info("额外等待15秒，确保最后一轮对话完成...")
                for _ in range(15):  # 等待15秒
                    if keep_alive_click_requested and keep_alive_click_requested['value']:
                        click_executed_count += 1
                        execute_center_click(page, screenshots_dir, click_executed_count)
                        keep_alive_click_requested['value'] = False
                    time.sleep(1)
                
                if test_mode:
                    logging.info(f"测试模式完成，共 {conversation_count} 轮对话（限制：最多3轮）")
                else:
                    logging.info(f"所有对话已完成，共 {conversation_count} 轮")
                final_screenshot_path = os.path.join(screenshots_dir, "final_complete.png")
                page.screenshot(path=final_screenshot_path)
                
                # 上传最终截图到OSS
                final_image_url = upload_image_to_oss(final_screenshot_path)
                
                # 上传最终结果文件到OSS
                final_results_url = None
                if os.path.exists(stats.current_results_file):
                    final_results_url = upload_text_to_oss(stats.current_results_file, os.path.basename(stats.current_results_file))
                    if final_results_url:
                        stats.set_current_results_file(stats.current_results_file, final_results_url)
                        logging.info(f"最终结果文件已上传: {final_results_url}")
                
                # 构建完成通知消息
                complete_message = (
                    f"### 🎉 聊天机器人测试完成\n" + 
                    f"**时间**: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n" +
                    f"**模式**: {'测试模式（限制3轮对话）' if test_mode else '正常模式'}\n" +
                    f"**总结**: 完成了 {conversation_count} 轮对话\n" +
                    f"**处理问题**: {processed_questions}/{total_questions_for_log}\n" +
                    f"**日志文件**: {log_file}"
                )
                
                # 如果有结果文件URL，添加链接
                # if final_results_url:
                #     complete_message += f"\n\n**[查看完整对话记录]({final_results_url})**"
                
                # 生成对话报告HTML
                report_data = []
                # 重新读取results_file，按对话分组，提取问答和截图
                with open(results_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                conv = None
                last_q = None
                last_a = None
                last_img = None
                last_wait = None
                last_question_image = None  # 添加图片路径跟踪
                last_question_image_url = None  # 添加问题图片URL跟踪
                last_ai_info = None  # 添加AI生成信息跟踪
                image_text_questions = []  # 存储图文问题信息
                
                for line in lines:
                    line = line.strip('\n')
                    if line.startswith('对话 '):
                        if conv:
                            report_data.append(conv)
                        cid = int(line.replace('对话 ', '').strip())
                        conv = {'conversation_id': cid, 'qas': []}
                        last_q = None
                        last_a = None
                        last_img = None
                        last_wait = None
                        last_question_image = None  # 添加图片路径跟踪
                        last_question_image_url = None  # 添加问题图片URL跟踪
                        last_ai_info = None  # 添加AI生成信息跟踪
                    elif line.startswith('问: '):
                        last_q = line[3:]
                        last_a = None
                        last_img = None
                        last_wait = None
                        last_question_image = None  # 添加图片路径跟踪
                        last_question_image_url = None  # 添加问题图片URL跟踪
                        last_ai_info = None  # 重置AI信息
                    elif line.startswith('AI生成问题 - '):
                        # 解析AI生成问题信息
                        last_ai_info = {'is_ai_generated': True}
                        ai_line = line.replace('AI生成问题 - ', '')
                        if '领域:' in ai_line and '意图:' in ai_line:
                            parts = ai_line.split(',')
                            for part in parts:
                                if '领域:' in part:
                                    last_ai_info['domain'] = part.split('领域:')[1].strip()
                                elif '意图:' in part:
                                    last_ai_info['intent_type'] = part.split('意图:')[1].strip()
                    elif line.startswith('AI追问对话 - '):
                        # 解析AI追问对话信息
                        last_ai_info = {'is_ai_followup': True}
                        ai_line = line.replace('AI追问对话 - ', '')
                        if '领域:' in ai_line:
                            parts = ai_line.split(',')
                            for part in parts:
                                if '领域:' in part:
                                    last_ai_info['domain'] = part.split('领域:')[1].strip()
                                elif '场景:' in part:
                                    last_ai_info['scenario'] = part.split('场景:')[1].strip()
                                elif '轮次:' in part:
                                    round_info = part.split('轮次:')[1].strip()
                                    last_ai_info['round_info'] = round_info
                    elif line.startswith('备注: '):
                        # 处理AI生成备注信息
                        if last_ai_info:
                            last_ai_info['fallback'] = 'AI生成失败' in line
                    elif line.startswith('图片路径: '):
                        # 解析图片路径信息
                        last_question_image = line[5:]
                    elif line.startswith('问题图片: '):
                        # 解析问题图片OSS URL
                        last_question_image_url = line[5:]
                    elif line.startswith('答: '):
                        last_a = line[3:]
                    elif line.startswith('截图: '):
                        last_img = line[4:]
                        if last_q is not None and last_a is not None:
                            # 创建问答对，包含额外的图文问题信息
                            qa_data = {
                                'question': last_q, 
                                'answer': last_a, 
                                'screenshot': last_img, 
                                'reply_wait': last_wait
                            }
                            # 检查是否是图文问题并添加标记
                            if '[图片]' in last_q:
                                qa_data['is_image_text'] = True
                                qa_data['question_type'] = 'image_text'
                                # 保存图片路径信息
                                qa_data['question_image'] = last_question_image
                                # 保存问题图片URL
                                qa_data['question_image_url'] = last_question_image_url
                            else:
                                qa_data['is_image_text'] = False
                                qa_data['question_type'] = 'text'
                                qa_data['question_image'] = None
                                qa_data['question_image_url'] = None
                            
                            # 检查是否是AI生成问题并添加标记
                            if last_ai_info:
                                if last_ai_info.get('is_ai_generated'):
                                    qa_data['is_ai_generated'] = True
                                    qa_data['ai_metadata'] = last_ai_info.copy()
                                    # 将AI信息合并到问题文本中以便解析函数识别
                                    ai_info_text = f"\nAI生成问题 - 领域: {last_ai_info.get('domain', '')}, 意图: {last_ai_info.get('intent_type', '')}"
                                    if last_ai_info.get('fallback'):
                                        ai_info_text += "\n备注: AI生成失败，使用示例问题"
                                    qa_data['question'] = f"问: {last_q}{ai_info_text}"
                                elif last_ai_info.get('is_ai_followup'):
                                    qa_data['is_ai_followup'] = True
                                    qa_data['ai_metadata'] = last_ai_info.copy()
                                    # 将AI追问对话信息合并到问题文本中
                                    ai_info_text = f"\nAI追问对话 - 领域: {last_ai_info.get('domain', '')}, 场景: {last_ai_info.get('scenario', '')}, 轮次: {last_ai_info.get('round_info', '')}"
                                    qa_data['question'] = f"问: {last_q}{ai_info_text}"
                                else:
                                    qa_data['is_ai_generated'] = False
                                    qa_data['is_ai_followup'] = False
                                    qa_data['ai_metadata'] = None
                            else:
                                qa_data['is_ai_generated'] = False
                                qa_data['is_ai_followup'] = False
                                qa_data['ai_metadata'] = None
                            conv['qas'].append(qa_data)
                            last_q, last_a, last_img, last_wait = None, None, None, None
                            last_question_image = None  # 添加图片路径跟踪
                            last_question_image_url = None  # 添加问题图片URL跟踪
                            last_ai_info = None  # 重置AI信息
                    elif line.startswith('回复等待时长: '):
                        last_wait = line.replace('回复等待时长: ', '')
                    elif last_q is not None and last_a is not None:
                        # 没有截图也要记录
                        qa_data = {
                            'question': last_q, 
                            'answer': last_a, 
                            'screenshot': None, 
                            'reply_wait': last_wait
                        }
                        # 检查是否是图文问题并添加标记
                        if '[图片]' in last_q:
                            qa_data['is_image_text'] = True
                            qa_data['question_type'] = 'image_text'
                        else:
                            qa_data['is_image_text'] = False
                            qa_data['question_type'] = 'text'
                        
                        # 检查是否是AI生成问题并添加标记
                        if last_ai_info:
                            qa_data['is_ai_generated'] = True
                            qa_data['ai_metadata'] = last_ai_info.copy()
                            # 将AI信息合并到问题文本中以便解析函数识别
                            ai_info_text = f"\nAI生成问题 - 领域: {last_ai_info.get('domain', '')}, 意图: {last_ai_info.get('intent_type', '')}"
                            if last_ai_info.get('fallback'):
                                ai_info_text += "\n备注: AI生成失败，使用示例问题"
                            qa_data['question'] = f"问: {last_q}{ai_info_text}"
                        else:
                            qa_data['is_ai_generated'] = False
                            qa_data['ai_metadata'] = None
                        
                        conv['qas'].append(qa_data)
                        last_q, last_a, last_img, last_wait = None, None, None, None
                        last_question_image = None  # 添加图片路径跟踪
                        last_question_image_url = None  # 添加问题图片URL跟踪
                        last_ai_info = None  # 重置AI信息
                if conv:
                    report_data.append(conv)
                # 生成HTML报告
                report_html_path = os.path.join(results_dir, f"conversation_report_{timestamp}.html")
                generate_conversation_report_html(report_data, report_html_path)
                # 上传到OSS
                report_oss_url = upload_text_to_oss(report_html_path, f"对话报告_{timestamp}.txt")
                if report_oss_url:
                    complete_message += f"\n\n**[查看对话报告]({report_oss_url})**"
                
                # 生成Excel报告
                report_excel_path = os.path.join(results_dir, f"conversation_report_{timestamp}.xlsx")
                generate_conversation_report_excel(report_data, report_excel_path)
                report_excel_oss_url = upload_text_to_oss(report_excel_path, f"对话报告_{timestamp}.xlsx")
                if report_excel_oss_url:
                    complete_message += f"\n\n**[Excel对话报告]({report_excel_oss_url})**"
                
                # 发送完成通知
                send_dingtalk_notification(
                    dingtalk_webhook, 
                    # dingtalk_secret,
                    complete_message,
                    image_url=final_image_url
                )
                
                # 主动调用浏览器返回，触发业务的正常终止流程
                logging.info("主动调用浏览器返回，触发业务终止流程...")
                try:
                    # 尝试多种返回方式，确保能够触发业务的正常终止流程
                    page.go_back(timeout=5000)
                    logging.info("已执行浏览器返回操作")
                    time.sleep(2)  # 等待返回操作完成
                    
                    # 如果返回没有效果，尝试导航到首页
                    current_url = page.url
                    if "chat" in current_url.lower():
                        logging.info("尝试导航到首页以触发正常退出...")
                        from urllib.parse import urlparse
                        parsed_url = urlparse(current_url)
                        home_url = f"{parsed_url.scheme}://{parsed_url.netloc}/"
                        page.goto(home_url, timeout=10000, wait_until="domcontentloaded")
                        logging.info("已导航到首页")
                        time.sleep(1)
                    
                except Exception as nav_error:
                    logging.warning(f"浏览器返回操作失败，但不影响测试完成: {str(nav_error)}")
                
                # 保持浏览器窗口打开一段时间，以便查看结果
                logging.info("保持浏览器窗口打开5秒...")
                time.sleep(5)
                
            except Exception as e:
                error_detail = traceback.format_exc()
                logging.error(f"执行过程中出错: {str(e)}")
                logging.error(error_detail)
                
                # 记录错误时的浏览器状态
                try:
                    final_error_path = os.path.join(screenshots_dir, "final_error_state.png")
                    page.screenshot(path=final_error_path)
                    error_image_url = upload_image_to_oss(final_error_path)
                except:
                    error_image_url = None
                
                # 上传当前结果文件，以便查看出错前的对话内容
                error_results_url = None
                if os.path.exists(stats.current_results_file):
                    error_results_url = upload_text_to_oss(stats.current_results_file, os.path.basename(stats.current_results_file))
                    if error_results_url:
                        logging.info(f"错误情况下的结果文件已上传: {error_results_url}")
                
                # 捕获浏览器调试信息
                debug_info, oss_links = collect_debug_information(page, browser, str(e), screenshots_dir)
                
                # 精简错误通知消息，只包含核心链接
                error_message = (
                    f"### ❌ 聊天机器人测试错误\n" + 
                    f"**时间**: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n" +
                    f"**错误**: {str(e)}\n"
                )
                
                # 只添加对话记录和调试信息两个链接
                links = []
                
                # 添加对话记录链接 (最重要的)
                if error_results_url:
                    links.append(f"**[查看详细对话记录]({error_results_url})**")
                
                # 添加调试信息链接 (其次重要的)
                if debug_info.get("debug_report"):
                    links.append(f"**[查看浏览器调试信息]({debug_info['debug_report']})**")
                
                # 添加链接到错误消息
                if links:
                    error_message += "\n" + "\n".join(links)
                
                # 发送错误通知
                send_dingtalk_notification(
                    dingtalk_webhook, 
                    error_message,
                    is_error=True,
                    at_phone=at_phone,
                    image_url=error_image_url
                )
                
                raise
            finally:
                # 安全地停止定时点击
                try:
                    if keep_alive_stop_event:
                        # 处理两种情况：tuple或单个Event对象
                        if isinstance(keep_alive_stop_event, tuple):
                            stop_event, _ = keep_alive_stop_event
                            stop_event.set()
                        else:
                            # 兼容旧代码中可能直接是Event对象的情况
                            keep_alive_stop_event.set()
                        logging.info("已停止页面定时点击保活功能")
                        time.sleep(1)  # 给线程一点时间停止
                except Exception as stop_error:
                    logging.error(f"停止页面定时点击保活功能时出错: {str(stop_error)}")
                
                # 关闭浏览器
                logging.info("正在关闭浏览器...")
                try:
                    # 设置超时
                    close_timeout = 10  # 最多等待10秒
                    
                    # 尝试优雅关闭
                    logging.info("尝试关闭上下文...")
                    context_close_thread = threading.Thread(target=context.close)
                    context_close_thread.daemon = True
                    context_close_thread.start()
                    context_close_thread.join(timeout=close_timeout)
                    
                    logging.info("尝试关闭浏览器...")
                    browser_close_thread = threading.Thread(target=browser.close)
                    browser_close_thread.daemon = True
                    browser_close_thread.start()
                    browser_close_thread.join(timeout=close_timeout)
                    
                    # 检查是否超时
                    if context_close_thread.is_alive() or browser_close_thread.is_alive():
                        logging.warning("浏览器关闭操作超时，强制结束进程")
                        # 如果你想要强制关闭浏览器进程，可以这样做
                        for proc in psutil.process_iter(['pid', 'name']):
                            try:
                                if 'chrome' in proc.info['name'].lower() or 'chromium' in proc.info['name'].lower():
                                    logging.info(f"强制结束浏览器进程: {proc.info['name']} (PID: {proc.info['pid']})")
                                    proc.kill()
                            except (psutil.NoSuchProcess, psutil.AccessDenied):
                                pass
                    else:
                        logging.info("浏览器已成功关闭")
                        
                except Exception as e:
                    logging.error(f"关闭浏览器时出错: {str(e)}")
                    logging.error(traceback.format_exc())
                
    except Exception as e:
        error_detail = traceback.format_exc()
        logging.error(f"初始化过程中出错: {str(e)}")
        logging.error(error_detail)
        
        # 发送严重错误通知
        send_dingtalk_notification(
            dingtalk_webhook, 
            # dingtalk_secret,
            f"### ❌ 聊天机器人测试严重错误\n" + 
            f"**时间**: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n" +
            f"**错误**: 初始化失败\n" +
            f"**详情**: {str(e)}\n" +
            f"**日志文件**: {log_file}",
            is_error=True,
            at_phone=at_phone
        )

# 定义新的统计追踪变量
class DialogueStats:
    """对话统计类"""
    def __init__(self):
        self.total_conversations = 0  # 总对话轮次
        self.successful_conversations = 0  # 成功的对话轮次
        self.failed_conversations = 0  # 失败的对话轮次
        self.total_questions = 0  # 总问题数
        self.successful_questions = 0  # 成功回答的问题数
        self.failed_questions = 0  # 失败的问题数
        self.last_notification_time = 0  # 上次发送统计通知的时间戳
        self.last_screenshot_path = ""  # 最新的截图路径
        self.current_results_file = ""  # 当前结果文件路径
        self.current_results_url = ""  # 当前结果文件OSS URL
        
    def set_current_results_file(self, file_path, file_url=None):
        """设置当前结果文件路径和URL"""
        self.current_results_file = file_path
        self.current_results_url = file_url
        
    def get_success_rate(self):
        """获取成功率"""
        if self.total_conversations == 0:
            return 0
        return (self.successful_conversations / self.total_conversations) * 100
        
    def get_question_success_rate(self):
        """获取问题回答成功率"""
        if self.total_questions == 0:
            return 0
        return (self.successful_questions / self.total_questions) * 100
        
    def add_conversation(self, success=True):
        """添加对话记录"""
        self.total_conversations += 1
        if success:
            self.successful_conversations += 1
        else:
            self.failed_conversations += 1
            
    def add_question(self, success=True):
        """添加问题记录"""
        self.total_questions += 1
        if success:
            self.successful_questions += 1
        else:
            self.failed_questions += 1
            
    def should_send_notification(self, interval_seconds=1800):
        """是否应该发送统计通知"""
        current_time = time.time()
        if current_time - self.last_notification_time >= interval_seconds:
            self.last_notification_time = current_time
            return True
        return False
        
    def set_last_screenshot(self, path):
        """设置最新截图路径"""
        self.last_screenshot_path = path
        
    def get_stats_message(self):
        """获取统计信息通知内容"""
        message = (
            f"### 📊 聊天机器人运行统计\n" +
            f"**时间**: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n" +
            f"**总对话**: {self.total_conversations} 轮\n" +
            f"**成功率**: {self.get_success_rate():.1f}%\n" +
            f"**问题总数**: {self.total_questions} 个\n" +
            f"**问题回答率**: {self.get_question_success_rate():.1f}%\n" +
            f"**成功**: {self.successful_conversations} 轮\n" +
            f"**失败**: {self.failed_conversations} 轮"
        )
        
        # 如果有结果文件URL，添加链接
        if self.current_results_url:
            message += f"\n\n**[查看详细对话记录]({self.current_results_url})**"
            
        return message

# 在run_tk_chat函数前添加：
def generate_conversation_report_html(all_conversations, output_path):
    """
    生成对话HTML报告，all_conversations为list，每项为dict：
    {
        'conversation_id': int,
        'qas': [
            {'question': str, 'answer': str, 'screenshot': str (oss url), 'reply_wait': str, 'is_image_text': bool, 'is_ai_generated': bool, 'ai_metadata': dict},
            ...
        ]
    }
    output_path: 保存的html文件路径
    """
    import html as html_module
    import re
    
    def extract_image_from_question(question_text):
        """从问题文本中提取图片信息
        格式: [图片] 问题文本 或 问: [图片] 问题文本
        """
        # 检查是否是图文问题
        if '[图片]' in question_text:
            # 提取纯文本部分（去掉[图片]标记）
            text_part = question_text.replace('[图片]', '').strip()
            return True, text_part
        return False, question_text
    
    def extract_ai_generated_info(question_text):
        """从问题文本中提取AI生成信息"""
        lines = question_text.split('\n')
        ai_info = {}
        pure_question = question_text
        
        for line in lines:
            if 'AI生成问题' in line:
                # 解析AI生成信息
                if '领域:' in line and '意图:' in line:
                    parts = line.split(',')
                    for part in parts:
                        if '领域:' in part:
                            ai_info['domain'] = part.split('领域:')[1].strip()
                        elif '意图:' in part:
                            ai_info['intent_type'] = part.split('意图:')[1].strip()
                ai_info['is_ai_generated'] = True
            elif 'AI追问对话' in line:
                # 解析AI追问对话信息
                ai_info['is_ai_followup'] = True
                if '领域:' in line:
                    domain_match = line.split('领域:')[1].split(',')[0].strip() if '领域:' in line else ''
                    if domain_match:
                        ai_info['domain'] = domain_match
                if '场景:' in line:
                    scenario_match = line.split('场景:')[1].split(',')[0].strip() if '场景:' in line else ''
                    if scenario_match:
                        ai_info['scenario'] = scenario_match
                if '轮次:' in line:
                    round_match = line.split('轮次:')[1].strip() if '轮次:' in line else ''
                    if round_match:
                        ai_info['round_info'] = round_match
            elif line.startswith('备注:'):
                ai_info['fallback'] = 'AI生成失败' in line
            elif line.startswith('📝 生成原因:'):
                ai_info['reason'] = line[8:].strip()  # 去掉"📝 生成原因:"前缀
            elif line.startswith('问:'):
                pure_question = line[2:].strip()
        
        return ai_info, pure_question
    
    html = []
    html.append('<!DOCTYPE html>')
    html.append('<html lang="zh-CN">')
    html.append('<head>')
    html.append('<meta charset="UTF-8">')
    html.append('<title>对话报告</title>')
    html.append('<style>')
    html.append('body{font-family:Arial,sans-serif;background:#f7f7f7;margin:0;padding:0;}')
    html.append('.container{max-width:1200px;margin:30px auto;background:#fff;padding:30px 40px;border-radius:8px;box-shadow:0 2px 8px rgba(0,0,0,0.08);}')
    html.append('h1{text-align:center;color:#222;margin-bottom:10px;}')
    html.append('.summary{text-align:center;color:#666;margin-bottom:30px;padding:15px;background:#f8f9fa;border-radius:5px;}')
    html.append('table{width:100%;border-collapse:collapse;margin-top:30px;}')
    html.append('th,td{border:1px solid #e0e0e0;padding:12px 8px;text-align:left;vertical-align:top;}')
    html.append('th{background:#f0f4fa;color:#333;font-weight:bold;}')
    html.append('tr:nth-child(even){background:#fafbfc;}')
    html.append('.q{color:#2c3e50;font-weight:bold;}')
    html.append('.a{color:#16a085;}')
    html.append('.q-image-text{color:#e74c3c;font-weight:bold;}')  # 图文问题特殊样式
    html.append('.q-ai-generated{color:#9b59b6;font-weight:bold;}')  # AI生成问题特殊样式
    html.append('.image-in-question{max-width:200px;max-height:150px;border:1px solid #ddd;border-radius:4px;margin:8px 0;cursor:pointer;transition:box-shadow .2s;}')
    html.append('.image-in-question:hover{box-shadow:0 0 8px #e74c3c55;}')
    html.append('.img-thumb{max-width:180px;max-height:120px;border:1px solid #eee;border-radius:4px;cursor:pointer;transition:box-shadow .2s;}')
    html.append('.img-thumb:hover{box-shadow:0 0 8px #16a08555;}')
    html.append('.cid{color:#888;font-size:13px;margin-top:20px;font-weight:bold;}')
    html.append('.img-modal{display:none;position:fixed;z-index:9999;left:0;top:0;width:100vw;height:100vh;background:rgba(0,0,0,0.7);align-items:center;justify-content:center;}')
    html.append('.img-modal img{max-width:90vw;max-height:90vh;border-radius:8px;box-shadow:0 4px 32px #0008;}')
    html.append('.img-modal.active{display:flex;}')
    html.append('.img-modal-close{position:absolute;top:30px;right:50px;font-size:40px;color:#fff;cursor:pointer;font-weight:bold;}')
    html.append('.question-content{display:flex;flex-direction:column;gap:8px;}')
    html.append('.image-text-badge{background:#e74c3c;color:white;font-size:12px;padding:3px 8px;border-radius:12px;display:inline-block;margin-bottom:5px;font-weight:bold;}')
    html.append('.ai-generated-badge{background:#9b59b6;color:white;font-size:12px;padding:3px 8px;border-radius:12px;display:inline-block;margin-bottom:5px;font-weight:bold;}')
    html.append('.ai-fallback-badge{background:#f39c12;color:white;font-size:12px;padding:3px 8px;border-radius:12px;display:inline-block;margin-bottom:5px;font-weight:bold;}')
    html.append('.text-badge{background:#3498db;color:white;font-size:12px;padding:3px 8px;border-radius:12px;display:inline-block;margin-bottom:5px;font-weight:bold;}')
    html.append('.conv-table{margin-bottom:40px;}')
    html.append('.image-text-note{color:#888;font-size:11px;font-style:italic;margin-top:3px;}')
    html.append('.ai-metadata{color:#666;font-size:11px;font-style:italic;margin-top:3px;padding:5px;background:#f8f9fa;border-radius:3px;}')
    html.append('.question-image-container{margin:10px 0;padding:10px;border:1px solid #eee;border-radius:4px;background:#f9f9f9;}')
    html.append('.question-image{max-width:300px;max-height:200px;border:1px solid #ddd;border-radius:4px;cursor:pointer;transition:box-shadow .2s;}')
    html.append('.question-image:hover{box-shadow:0 0 8px #e74c3c55;}')
    html.append('</style>')
    html.append('</head>')
    html.append('<body>')
    html.append('<div class="container">')
    html.append('<h1>聊天机器人对话报告</h1>')
    
    # 添加统计摘要
    total_conversations = len(all_conversations)
    total_questions = sum(len(conv['qas']) for conv in all_conversations)
    image_text_count = sum(sum(1 for qa in conv['qas'] if qa.get('is_image_text', False)) for conv in all_conversations)
    ai_generated_count = sum(sum(1 for qa in conv['qas'] if qa.get('is_ai_generated', False)) for conv in all_conversations)
    ai_followup_count = sum(sum(1 for qa in conv['qas'] if qa.get('is_ai_followup', False)) for conv in all_conversations)
    text_count = total_questions - image_text_count - ai_generated_count - ai_followup_count
    
    html.append(f'<div class="summary">')
    html.append(f'总计 {total_conversations} 轮对话，{total_questions} 个问题（其中AI生成问题 {ai_generated_count} 个，AI追问对话 {ai_followup_count} 个，图文问题 {image_text_count} 个，普通文本问题 {text_count} 个）')
    html.append('</div>')
    
    for conv in all_conversations:
        html.append(f'<div class="cid">对话 {conv["conversation_id"]}</div>')
        html.append('<table class="conv-table">')
        html.append('<tr><th style="width:40%">问题</th><th style="width:40%">答复</th><th style="width:15%">截图</th><th style="width:5%">回复时长</th></tr>')
        
        for idx, qa in enumerate(conv['qas']):
            question_text = qa['question']
            is_image_text = qa.get('is_image_text', '[图片]' in question_text)
            
            # 检查是否是AI生成问题
            ai_info, pure_question = extract_ai_generated_info(question_text)
            is_ai_generated = ai_info.get('is_ai_generated', False)
            is_ai_followup = ai_info.get('is_ai_followup', False)
            
            
            # 写入问题类型和内容
            if is_ai_followup:
                # AI追问对话：显示徽章和元数据
                question_html = f'''<div class="question-content">
                    <div class="ai-generated-badge">🔄 AI追问对话</div>'''
                
                question_html += f'''<div class="q-ai-generated">{html_module.escape(pure_question)}</div>'''
                
                # 添加AI追问对话的元数据信息
                if ai_info.get('domain') or ai_info.get('scenario') or ai_info.get('round_info') or ai_info.get('reason'):
                    question_html += f'''<div class="ai-metadata">'''
                    if ai_info.get('domain'):
                        question_html += f'''领域: {html_module.escape(ai_info['domain'])} '''
                    if ai_info.get('scenario'):
                        question_html += f'''场景: {html_module.escape(ai_info['scenario'])} '''
                    if ai_info.get('round_info'):
                        question_html += f'''轮次: {html_module.escape(ai_info['round_info'])}'''
                    if ai_info.get('reason'):
                        question_html += f''' | 生成原因: {html_module.escape(ai_info['reason'])}'''
                    question_html += f'''</div>'''
                
                question_html += '''</div>'''
            elif is_ai_generated:
                # AI生成问题：显示徽章和元数据
                question_html = f'''<div class="question-content">
                    <div class="ai-generated-badge">🤖 AI生成问题</div>'''
                
                if ai_info.get('fallback'):
                    question_html += f'''<div class="ai-fallback-badge">⚠️ 备用问题</div>'''
                
                question_html += f'''<div class="q-ai-generated">{html_module.escape(pure_question)}</div>'''
                
                # 添加AI生成的元数据信息
                if ai_info.get('domain') or ai_info.get('intent_type'):
                    question_html += f'''<div class="ai-metadata">'''
                    if ai_info.get('domain'):
                        question_html += f'''领域: {html_module.escape(ai_info['domain'])} '''
                    if ai_info.get('intent_type'):
                        question_html += f'''意图类型: {html_module.escape(ai_info['intent_type'])}'''
                    if ai_info.get('fallback'):
                        question_html += f''' | 备注: AI生成失败，使用示例问题'''
                    question_html += f'''</div>'''
                
                question_html += '''</div>'''
            elif is_image_text:
                _, pure_text = extract_image_from_question(question_text)
                # 获取问题图片URL
                question_image_url = qa.get('question_image_url')
                
                # 图文问题：显示徽章和文本，以及原始问题图片
                question_html = f'''<div class="question-content">
                    <div class="image-text-badge">📷 图文问题</div>
                    <div class="q-image-text">{html_module.escape(pure_text)}</div>'''
                
                # 如果有问题图片URL，则显示图片
                if question_image_url:
                    question_html += f'''<div class="question-image-container">
                        <p class="image-text-note">用户上传的原始问题图片：</p>
                        <img src="{question_image_url}" class="question-image" onclick="showImgModal('{question_image_url}')" alt="问题图片" title="点击查看大图">
                    </div>'''
                else:
                    question_html += '''<div class="image-text-note">此问题包含了用户上传的图片（图片已在对话中发送给AI）</div>
                    <div class="image-text-note">💡 提示：查看对应的截图可以看到完整的图文对话效果</div>'''
                
                question_html += '''</div>'''
            else:
                question_html = f'''<div class="question-content">
                    <div class="text-badge">💬 文本问题</div>
                    <div class="q">{html_module.escape(pure_question)}</div>
                </div>'''
            
            # 处理答复列
            answer_text = html_module.escape(qa['answer'])
            
            # 处理截图列
            img = qa.get('screenshot')
            wait = html_module.escape(qa.get('reply_wait',''))
            img_html = ''
            if img:
                img_html = f'<img src="{img}" class="img-thumb" onclick="showImgModal(\'{img}\')" alt="截图" title="点击查看大图">'
            else:
                img_html = '<span style="color:#ccc;font-size:12px;">无截图</span>'
            
            html.append(f'<tr><td>{question_html}</td><td class="a">{answer_text}</td><td>{img_html}</td><td>{wait}</td></tr>')
        
        html.append('</table>')
    
    # 弹窗大图容器
    html.append('''<div class="img-modal" id="imgModal" onclick="hideImgModal()">
        <span class="img-modal-close" onclick="hideImgModal()">&times;</span>
        <img id="imgModalImg" src="" alt="大图">
    </div>''')
    
    # 脚本
    html.append('''<script>
    function showImgModal(url){
        var modal = document.getElementById('imgModal');
        var img = document.getElementById('imgModalImg');
        img.src = url;
        modal.classList.add('active');
    }
    function hideImgModal(){
        var modal = document.getElementById('imgModal');
        modal.classList.remove('active');
        document.getElementById('imgModalImg').src = '';
    }
    // 防止点击图片本身冒泡关闭弹窗
    document.addEventListener('DOMContentLoaded', function(){
        var img = document.getElementById('imgModalImg');
        img.onclick = function(e){e.stopPropagation();};
    });
    </script>''')
    html.append('</div></body></html>')
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(html))

def generate_conversation_report_excel(all_conversations, output_path):
    """
    生成对话Excel报告，all_conversations结构同HTML报告
    output_path: 保存的xlsx文件路径
    """
    import xlsxwriter
    from PIL import Image
    
    def extract_image_from_question(question_text):
        """从问题文本中提取图片信息"""
        if '[图片]' in question_text:
            text_part = question_text.replace('[图片]', '').strip()
            return True, text_part
        return False, question_text
    
    def extract_ai_generated_info(question_text):
        """从问题文本中提取AI生成信息"""
        lines = question_text.split('\n')
        ai_info = {}
        pure_question = question_text
        
        for line in lines:
            if 'AI生成问题' in line:
                # 解析AI生成信息
                if '领域:' in line and '意图:' in line:
                    parts = line.split(',')
                    for part in parts:
                        if '领域:' in part:
                            ai_info['domain'] = part.split('领域:')[1].strip()
                        elif '意图:' in part:
                            ai_info['intent_type'] = part.split('意图:')[1].strip()
                ai_info['is_ai_generated'] = True
            elif 'AI追问对话' in line:
                # 解析AI追问对话信息
                ai_info['is_ai_followup'] = True
                if '领域:' in line:
                    domain_match = line.split('领域:')[1].split(',')[0].strip() if '领域:' in line else ''
                    if domain_match:
                        ai_info['domain'] = domain_match
                if '场景:' in line:
                    scenario_match = line.split('场景:')[1].split(',')[0].strip() if '场景:' in line else ''
                    if scenario_match:
                        ai_info['scenario'] = scenario_match
                if '轮次:' in line:
                    round_match = line.split('轮次:')[1].strip() if '轮次:' in line else ''
                    if round_match:
                        ai_info['round_info'] = round_match
            elif line.startswith('备注:'):
                ai_info['fallback'] = 'AI生成失败' in line
            elif line.startswith('📝 生成原因:'):
                ai_info['reason'] = line[8:].strip()  # 去掉"📝 生成原因:"前缀
            elif line.startswith('问:'):
                pure_question = line[2:].strip()
        
        return ai_info, pure_question
    
    workbook = xlsxwriter.Workbook(output_path)
    worksheet = workbook.add_worksheet('对话报告')
    
    # 设置表头
    headers = ['对话编号', '问题类型', '问题内容', '答复', '截图', '回复等待时长']
    
    # 定义格式
    header_format = workbook.add_format({
        'bold': True,
        'align': 'center',
        'valign': 'vcenter',
        'bg_color': '#f0f4fa',
        'border': 1,
        'font_size': 11
    })
    
    normal_format = workbook.add_format({
        'align': 'left',
        'valign': 'top',
        'border': 1,
        'text_wrap': True,
        'font_size': 10
    })
    
    image_text_format = workbook.add_format({
        'align': 'left',
        'valign': 'top',
        'border': 1,
        'text_wrap': True,
        'font_color': '#e74c3c',
        'bold': True,
        'font_size': 10
    })
    
    ai_generated_format = workbook.add_format({
        'align': 'left',
        'valign': 'top',
        'border': 1,
        'text_wrap': True,
        'font_color': '#9b59b6',
        'bold': True,
        'font_size': 10
    })
    
    type_image_format = workbook.add_format({
        'align': 'center',
        'valign': 'vcenter',
        'border': 1,
        'bg_color': '#ffe6e6',
        'font_color': '#e74c3c',
        'bold': True,
        'font_size': 10
    })
    
    type_ai_format = workbook.add_format({
        'align': 'center',
        'valign': 'vcenter',
        'border': 1,
        'bg_color': '#f3e6ff',
        'font_color': '#9b59b6',
        'bold': True,
        'font_size': 10
    })
    
    type_ai_fallback_format = workbook.add_format({
        'align': 'center',
        'valign': 'vcenter',
        'border': 1,
        'bg_color': '#fff3e0',
        'font_color': '#f39c12',
        'bold': True,
        'font_size': 10
    })
    
    type_text_format = workbook.add_format({
        'align': 'center',
        'valign': 'vcenter',
        'border': 1,
        'bg_color': '#e6f3ff',
        'font_color': '#3498db',
        'bold': True,
        'font_size': 10
    })
    
    for col, h in enumerate(headers):
        worksheet.write(0, col, h, header_format)
    
    row = 1
    screenshots_dir = os.path.join(os.getcwd(), 'screenshots')
    max_img_width = 160  # px
    max_img_height = 100  # px
    
    # 统计信息
    total_conversations = len(all_conversations)
    total_questions = sum(len(conv['qas']) for conv in all_conversations)
    image_text_count = sum(sum(1 for qa in conv['qas'] if qa.get('is_image_text', False)) for conv in all_conversations)
    ai_generated_count = sum(sum(1 for qa in conv['qas'] if qa.get('is_ai_generated', False)) for conv in all_conversations)
    ai_followup_count = sum(sum(1 for qa in conv['qas'] if qa.get('is_ai_followup', False)) for conv in all_conversations)
    text_count = total_questions - image_text_count - ai_generated_count - ai_followup_count
    
    for conv in all_conversations:
        cid = conv['conversation_id']
        for qa in conv['qas']:
            # 处理问题文本
            question_text = qa['question']
            is_image_text = qa.get('is_image_text', '[图片]' in question_text)
            
            # 检查是否是AI生成问题
            ai_info, pure_question = extract_ai_generated_info(question_text)
            is_ai_generated = ai_info.get('is_ai_generated', False)
            is_ai_followup = ai_info.get('is_ai_followup', False)
            
            # 写入对话编号
            worksheet.write(row, 0, cid, normal_format)
            
            # 写入问题类型和内容
            if is_ai_followup:
                worksheet.write(row, 1, "🔄 AI追问对话", type_ai_format)
                
                # 构建问题内容，包含AI追问对话元数据
                question_content = pure_question
                if ai_info.get('domain') or ai_info.get('scenario') or ai_info.get('round_info') or ai_info.get('reason'):
                    question_content += "\n\n【追问对话信息】"
                    if ai_info.get('domain'):
                        question_content += f"\n领域: {ai_info['domain']}"
                    if ai_info.get('scenario'):
                        question_content += f"\n场景: {ai_info['scenario']}"
                    if ai_info.get('round_info'):
                        question_content += f"\n轮次: {ai_info['round_info']}"
                    if ai_info.get('reason'):
                        question_content += f"\n生成原因: {ai_info['reason']}"
                
                worksheet.write(row, 2, question_content, ai_generated_format)
                
            elif is_ai_generated:
                if ai_info.get('fallback'):
                    worksheet.write(row, 1, "🤖⚠️ AI生成(备用)", type_ai_fallback_format)
                else:
                    worksheet.write(row, 1, "🤖 AI生成问题", type_ai_format)
                
                # 构建问题内容，包含AI元数据
                question_content = pure_question
                if ai_info.get('domain') or ai_info.get('intent_type'):
                    question_content += "\n\n【AI生成信息】"
                    if ai_info.get('domain'):
                        question_content += f"\n领域: {ai_info['domain']}"
                    if ai_info.get('intent_type'):
                        question_content += f"\n意图类型: {ai_info['intent_type']}"
                    if ai_info.get('fallback'):
                        question_content += f"\n备注: AI生成失败，使用示例问题"
                
                worksheet.write(row, 2, question_content, ai_generated_format)
                
            elif is_image_text:
                worksheet.write(row, 1, "📷 图文问题", type_image_format)
                _, pure_text = extract_image_from_question(question_text)
                # 在问题内容中添加图片信息提示
                question_image_url = qa.get('question_image_url')
                if question_image_url:
                    question_with_image_note = f"{pure_text}\n\n[用户上传的原始问题图片: {question_image_url}]\n[查看截图可看到完整图文对话效果]"
                else:
                    question_with_image_note = f"{pure_text}\n\n[此问题包含用户上传的图片]\n[查看截图可看到完整图文对话效果]"
                worksheet.write(row, 2, question_with_image_note, image_text_format)
            else:
                worksheet.write(row, 1, "💬 文本问题", type_text_format)
                worksheet.write(row, 2, pure_question, normal_format)
            
            # 写入答复
            worksheet.write(row, 3, qa['answer'], normal_format)
            
            # 处理截图图片
            img_url = qa.get('screenshot')
            if img_url:
                fname = img_url.split('/')[-1]
                local_path = os.path.join(screenshots_dir, fname)
                if os.path.exists(local_path):
                    # 动态获取图片尺寸，合理缩放
                    try:
                        with Image.open(local_path) as im:
                            w, h = im.size
                        x_scale = min(1, max_img_width / w)
                        y_scale = min(1, max_img_height / h)
                        scale = min(x_scale, y_scale)
                        # 设置行高为缩放后图片高度+边距
                        worksheet.set_row(row, max(25, int(h * scale) + 8))
                        
                        # 插入图片
                        worksheet.insert_image(row, 4, local_path, {
                            'x_scale': scale,
                            'y_scale': scale,
                            'object_position': 2,
                            'x_offset': 2,
                            'y_offset': 2,
                            'tip': f'截图预览 - 来源: {img_url}'
                        })
                    except Exception as e:
                        worksheet.write(row, 4, f'图片加载失败: {e}', normal_format)
                else:
                    worksheet.write(row, 4, '图片文件不存在', normal_format)
            else:
                worksheet.write(row, 4, '无截图', normal_format)
            
            # 写入回复等待时长
            worksheet.write(row, 5, qa.get('reply_wait',''), normal_format)
            
            row += 1
    
    # 在数据下方添加统计信息
    row += 2
    worksheet.write(row, 0, '统计信息', header_format)
    row += 1
    worksheet.write(row, 0, f'总对话轮数: {total_conversations}', normal_format)
    row += 1
    worksheet.write(row, 0, f'总问题数: {total_questions}', normal_format)
    row += 1
    worksheet.write(row, 0, f'AI生成问题数: {ai_generated_count}', normal_format)
    row += 1
    worksheet.write(row, 0, f'AI追问对话数: {ai_followup_count}', normal_format)
    row += 1
    worksheet.write(row, 0, f'图文问题数: {image_text_count}', normal_format)
    row += 1
    worksheet.write(row, 0, f'普通文本问题数: {text_count}', normal_format)
    
    # 设置列宽
    worksheet.set_column(0, 0, 12)  # 对话编号
    worksheet.set_column(1, 1, 15)  # 问题类型
    worksheet.set_column(2, 2, 50)  # 问题内容
    worksheet.set_column(3, 3, 50)  # 答复
    worksheet.set_column(4, 4, 22)  # 截图
    worksheet.set_column(5, 5, 18)  # 回复等待时长
    
    # 冻结首行
    worksheet.freeze_panes(1, 0)
    
    # 添加自动筛选
    data_end_row = row - 7  # 数据结束行（减去统计信息行数）
    worksheet.autofilter(0, 0, data_end_row, len(headers)-1)
    
    workbook.close()

# 图片处理相关函数

def download_image_from_url(url, download_dir="downloaded_images"):
    """从URL下载图片到本地
    
    Args:
        url: 图片URL
        download_dir: 下载目录
        
    Returns:
        str: 本地图片文件路径，失败时返回None
    """
    try:
        # 创建下载目录
        os.makedirs(download_dir, exist_ok=True)
        
        # 生成唯一的文件名
        url_hash = hashlib.md5(url.encode()).hexdigest()[:8]
        
        # 获取文件扩展名
        parsed_url = urllib.parse.urlparse(url)
        path = parsed_url.path
        _, ext = os.path.splitext(path)
        
        # 如果没有扩展名，默认使用.jpg
        if not ext:
            ext = '.jpg'
        
        filename = f"image_{url_hash}{ext}"
        filepath = os.path.join(download_dir, filename)
        
        # 如果文件已存在，直接返回路径
        if os.path.exists(filepath):
            logging.info(f"图片已存在，使用缓存: {filepath}")
            return filepath
        
        # 下载图片
        logging.info(f"正在下载图片: {url}")
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        # 保存图片
        with open(filepath, 'wb') as f:
            f.write(response.content)
        
        logging.info(f"图片下载成功: {filepath}")
        return filepath
        
    except Exception as e:
        logging.error(f"下载图片失败: {url}, 错误: {str(e)}")
        return None

def validate_image_file(filepath):
    """验证图片文件是否有效
    
    Args:
        filepath: 图片文件路径
        
    Returns:
        bool: 图片是否有效
    """
    try:
        if not os.path.exists(filepath):
            logging.error(f"图片文件不存在: {filepath}")
            return False
        
        # 检查文件大小（不超过10MB）
        file_size = os.path.getsize(filepath)
        if file_size > 10 * 1024 * 1024:
            logging.error(f"图片文件过大: {filepath}, 大小: {file_size/1024/1024:.2f}MB")
            return False
        
        # 尝试打开图片验证格式
        with Image.open(filepath) as img:
            img.verify()
        
        logging.info(f"图片文件验证成功: {filepath}")
        return True
        
    except Exception as e:
        logging.error(f"图片文件验证失败: {filepath}, 错误: {str(e)}")
        return False

def upload_image_to_chat(page, image_path, screenshots_dir):
    """在聊天界面上传图片
    
    Args:
        page: Playwright页面对象
        image_path: 图片文件路径
        screenshots_dir: 截图目录
        
    Returns:
        bool: 上传是否成功
    """
    try:
        logging.info(f"正在上传图片: {image_path}")
        
        # 首先验证图片文件
        if not validate_image_file(image_path):
            return False
        
        # 第一步：点击"添加"按钮来唤起文件上传控件
        # add_button_selectors = [
        #     'img[alt="添加"]',  # 用户提供的具体选择器
        #     'button:has(img[alt="添加"])',  # 包含添加图标的按钮
        #     'img[src*="add_icon"]',  # 包含add_icon的img
        #     'button:has(img[src*="add_icon"])',  # 包含add_icon的按钮
        #     '.size-6:has(img[alt="添加"])',  # 带size-6类的添加按钮
        #     'img[class*="size-6"][alt="添加"]'  # 用户提到的具体元素
        # ]
        
        # add_button_clicked = False
        # for selector in add_button_selectors:
        #     try:
        #         element = page.locator(selector).first
        #         if element.is_visible():
        #             # 如果找到的是img元素，点击其父级button
        #             if selector.startswith('img'):
        #                 parent_button = element.locator('..').first  # 获取父元素
        #                 if parent_button.get_attribute('tagName').lower() == 'button':
        #                     parent_button.click()
        #                     logging.info(f"点击添加按钮成功 (通过img父元素): {selector}")
        #                 else:
        #                     # element.click()
        #                     logging.info(f"点击添加图标成功: {selector}")
        #             else:
        #                 # element.click()
        #                 logging.info(f"点击添加按钮成功: {selector}")
                    
        #             add_button_clicked = True
        #             time.sleep(1)  # 等待文件选择器出现
        #             break
        #     except Exception as e:
        #         logging.debug(f"尝试点击 {selector} 失败: {str(e)}")
        #         continue
        
        # if not add_button_clicked:
        #     logging.warning("未找到添加按钮，尝试直接查找文件上传控件")
        # else:
        #     logging.info("成功点击添加按钮，文件选择器应该已打开")
        
        # 第二步：查找并使用文件上传控件
        upload_selectors = [
            'input[type="file"]',  # 标准文件输入控件
            'input[accept*="image"]',  # 接受图片的输入控件
            'input[accept*="*"]'  # 接受所有文件的输入控件
        ]
        
        upload_success = False
        file_input = None
        
        # 查找文件上传input控件
        for selector in upload_selectors:
            try:
                elements = page.locator(selector).all()
                for element in elements:
                    # 检查元素是否可用（可能不一定可见）
                    if element.is_enabled():
                        file_input = element
                        logging.info(f"找到文件上传控件: {selector}")
                        break
                if file_input:
                    break
            except Exception as e:
                logging.debug(f"查找上传控件失败 {selector}: {str(e)}")
                continue
        
        if file_input:
            # 设置文件到输入控件
            try:
                file_input.set_input_files(image_path)
                logging.info(f"成功设置上传文件: {image_path}")
                upload_success = True
                
                # 等待上传完成
                time.sleep(2)
                
                # 新增：关闭文件上传控件
                # try:
                #     logging.info("正在关闭文件上传控件...")
                    
                #     # 调试：保存上传后的初始状态截图
                #     initial_state_path = os.path.join(screenshots_dir, "upload_initial_state.png")
                #     page.screenshot(path=initial_state_path)
                #     logging.info(f"上传后初始状态截图: {initial_state_path}")
                    
                #     # 检查当前页面标题和URL，了解页面状态
                #     try:
                #         current_url = page.url
                #         current_title = page.title()
                #         logging.info(f"当前页面状态 - URL: {current_url}, 标题: {current_title}")
                #     except Exception as page_info_error:
                #         logging.warning(f"获取页面信息失败: {str(page_info_error)}")
                    
                #     # 检查是否有文件输入控件仍然存在且可见
                #     def check_file_dialog_state():
                #         try:
                #             file_inputs = page.locator('input[type="file"]').all()
                #             visible_count = 0
                #             for file_input in file_inputs:
                #                 if file_input.is_visible():
                #                     visible_count += 1
                #             logging.info(f"当前可见的文件输入控件数量: {visible_count}")
                #             return visible_count > 0
                #         except Exception as check_error:
                #             logging.warning(f"检查文件对话框状态失败: {str(check_error)}")
                #             return False
                    
                #     # 检查初始状态
                #     initial_has_dialog = check_file_dialog_state()
                #     logging.info(f"初始文件对话框状态: {'存在' if initial_has_dialog else '不存在'}")
                    
                #     # 首先确保页面获得焦点
                #     page.bring_to_front()
                #     logging.info("已将页面置于前台")
                #     time.sleep(0.5)
                    
                #     # 检查焦点状态
                #     try:
                #         is_focused = page.evaluate("() => document.hasFocus()")
                #         logging.info(f"页面焦点状态: {'有焦点' if is_focused else '无焦点'}")
                #     except Exception as focus_error:
                #         logging.warning(f"检查焦点状态失败: {str(focus_error)}")
                    
                #     # 点击页面确保焦点正确
                #     page.click('body')
                #     logging.info("已点击页面确保焦点")
                #     time.sleep(0.3)
                    
                #     # 再次检查焦点
                #     try:
                #         is_focused_after = page.evaluate("() => document.hasFocus()")
                #         logging.info(f"点击后页面焦点状态: {'有焦点' if is_focused_after else '无焦点'}")
                #     except Exception as focus_error:
                #         logging.warning(f"检查焦点状态失败: {str(focus_error)}")
                    
                #     # 方法1：连续按Escape键（有时需要多次）
                #     logging.info("开始尝试Escape键关闭方法...")
                #     for i in range(5):  # 增加到5次
                #         page.keyboard.press("Escape")
                #         logging.info(f"第{i+1}次按Escape键")
                #         time.sleep(0.4)  # 增加等待时间
                        
                #         # 每次按键后检查状态
                #         has_dialog_after_escape = check_file_dialog_state()
                #         logging.info(f"第{i+1}次Escape后文件对话框状态: {'存在' if has_dialog_after_escape else '不存在'}")
                        
                #         if not has_dialog_after_escape:
                #             logging.info(f"文件对话框已在第{i+1}次Escape后关闭")
                #             break
                    
                #     # 检查Escape后的状态
                #     time.sleep(0.5)
                #     escape_state_path = os.path.join(screenshots_dir, "after_escape_keys.png")
                #     page.screenshot(path=escape_state_path)
                #     logging.info(f"Escape键后状态截图: {escape_state_path}")
                    
                #     # 方法2：尝试使用键盘组合键
                #     logging.info("尝试键盘组合键关闭方法...")
                #     try:
                #         # macOS使用Cmd+W，Windows使用Alt+F4
                #         import platform
                #         system = platform.system()
                #         if system == "Darwin":  # macOS
                #             page.keyboard.press("Meta+w")
                #             logging.info("尝试Cmd+W关闭 (macOS)")
                #         else:  # Windows/Linux
                #             page.keyboard.press("Alt+F4")
                #             logging.info("尝试Alt+F4关闭 (Windows)")
                #         time.sleep(0.3)
                        
                #         has_dialog_after_hotkey = check_file_dialog_state()
                #         logging.info(f"热键后文件对话框状态: {'存在' if has_dialog_after_hotkey else '不存在'}")
                #     except Exception as hotkey_error:
                #         logging.warning(f"热键关闭失败: {str(hotkey_error)}")
                    
                #     # 方法3：点击页面不同区域确保控件失去焦点
                #     logging.info("尝试点击页面区域转移焦点...")
                #     size = page.viewport_size
                #     click_positions = [
                #         {"x": size["width"] // 2, "y": size["height"] // 2},  # 中央
                #         {"x": size["width"] // 4, "y": size["height"] // 4},  # 左上
                #         {"x": size["width"] * 3 // 4, "y": size["height"] * 3 // 4},  # 右下
                #         {"x": 50, "y": 50},  # 左上角
                #         {"x": size["width"] - 50, "y": size["height"] - 50},  # 右下角
                #     ]
                    
                #     for idx, pos in enumerate(click_positions):
                #         try:
                #             page.click('body', position=pos, force=True)
                #             logging.info(f"点击位置{idx+1}: x={pos['x']}, y={pos['y']}")
                #             time.sleep(0.2)
                            
                #             # 每次点击后按Escape
                #             page.keyboard.press("Escape")
                #             logging.info(f"点击位置{idx+1}后按Escape")
                #             time.sleep(0.2)
                            
                #             has_dialog_after_click = check_file_dialog_state()
                #             logging.info(f"点击位置{idx+1}后文件对话框状态: {'存在' if has_dialog_after_click else '不存在'}")
                            
                #             if not has_dialog_after_click:
                #                 logging.info(f"文件对话框已在点击位置{idx+1}后关闭")
                #                 break
                                
                #         except Exception as click_error:
                #             logging.warning(f"点击位置{idx+1}失败: {str(click_error)}")
                    
                #     # 方法4：尝试模拟Tab键切换焦点后再按Escape
                #     logging.info("尝试Tab键切换焦点方法...")
                #     try:
                #         for tab_attempt in range(3):
                #             page.keyboard.press("Tab")
                #             time.sleep(0.2)
                #             page.keyboard.press("Escape")
                #             logging.info(f"第{tab_attempt+1}次Tab+Escape组合")
                #             time.sleep(0.3)
                            
                #             has_dialog_after_tab = check_file_dialog_state()
                #             logging.info(f"第{tab_attempt+1}次Tab+Escape后文件对话框状态: {'存在' if has_dialog_after_tab else '不存在'}")
                            
                #             if not has_dialog_after_tab:
                #                 logging.info(f"文件对话框已在第{tab_attempt+1}次Tab+Escape后关闭")
                #                 break
                #     except Exception as tab_error:
                #         logging.warning(f"Tab+Escape失败: {str(tab_error)}")
                    
                #     # 方法5：尝试直接操作DOM
                #     logging.info("尝试DOM操作关闭文件选择器...")
                #     try:
                #         # 尝试移除或隐藏文件输入控件
                #         page.evaluate("""() => {
                #             const fileInputs = document.querySelectorAll('input[type="file"]');
                #             console.log('找到文件输入控件数量:', fileInputs.length);
                #             fileInputs.forEach((input, index) => {
                #                 console.log('处理文件输入控件', index, input);
                #                 if (input.style) {
                #                     input.style.display = 'none';
                #                     input.style.visibility = 'hidden';
                #                 }
                #                 // 触发blur事件
                #                 input.blur();
                #                 // 清空文件选择
                #                 input.value = '';
                #             });
                #         }""")
                #         logging.info("已执行DOM操作隐藏文件输入控件")
                #         time.sleep(0.3)
                        
                #         has_dialog_after_dom = check_file_dialog_state()
                #         logging.info(f"DOM操作后文件对话框状态: {'存在' if has_dialog_after_dom else '不存在'}")
                #     except Exception as dom_error:
                #         logging.warning(f"DOM操作失败: {str(dom_error)}")
                    
                #     # 最终检查和截图
                #     final_has_dialog = check_file_dialog_state()
                #     logging.info(f"最终文件对话框状态: {'存在' if final_has_dialog else '不存在'}")
                    
                #     # 保存最终状态截图
                #     final_state_path = os.path.join(screenshots_dir, "upload_final_state.png")
                #     page.screenshot(path=final_state_path)
                #     logging.info(f"最终状态截图: {final_state_path}")
                    
                #     if final_has_dialog:
                #         logging.warning("警告：文件上传控件可能仍然打开")
                #     else:
                #         logging.info("文件上传控件已成功关闭")
                    
                #     logging.info("文件上传控件关闭操作完成")
                    
                # except Exception as close_error:
                #     logging.error(f"关闭文件上传控件时出现严重错误: {str(close_error)}")
                #     # 保存错误状态截图
                #     error_state_path = os.path.join(screenshots_dir, "upload_close_error.png")
                #     try:
                #         page.screenshot(path=error_state_path)
                #         logging.info(f"错误状态截图: {error_state_path}")
                #     except:
                #         pass
                #     # 即使关闭失败也继续执行
                
                # 截图确认上传状态
                upload_screenshot_path = os.path.join(screenshots_dir, "after_image_upload.png")
                page.screenshot(path=upload_screenshot_path)
                logging.info(f"图片上传后截图已保存: {upload_screenshot_path}")
                
            except Exception as e:
                logging.error(f"设置上传文件失败: {str(e)}")
        else:
            logging.error("无法找到文件上传控件")
            
            # 保存当前页面状态以供调试
            debug_screenshot_path = os.path.join(screenshots_dir, "upload_debug.png")
            page.screenshot(path=debug_screenshot_path)
            logging.info(f"调试截图已保存: {debug_screenshot_path}")
        
        return upload_success
        
    except Exception as e:
        logging.error(f"上传图片时出错: {str(e)}")
        logging.error(traceback.format_exc())
        return False

def get_next_question_unified(unified_questions, used_indices, mode="sequential"):
    """从统一问题列表中选择下一个问题
    
    Args:
        unified_questions: 统一问题列表
        used_indices: 已使用的问题索引集合
        mode: 访问模式，'random'或'sequential'
    """
    if not unified_questions:
        return None
    
    # 检查是否所有问题都已使用过
    if len(used_indices) >= len(unified_questions):
        return None
    
    if mode == "random":
        # 随机模式
        available_indices = [i for i in range(len(unified_questions)) if i not in used_indices]
        if not available_indices:
            return None
        
        selected_index = random.choice(available_indices)
        used_indices.add(selected_index)
        question_data = unified_questions[selected_index]
        
        return format_question_for_execution(question_data)
    else:
        # 顺序模式
        for i in range(len(unified_questions)):
            if i not in used_indices:
                used_indices.add(i)
                question_data = unified_questions[i]
                return format_question_for_execution(question_data)
        
        return None

def format_question_for_execution(question_data):
    """将统一格式的问题数据转换为执行格式
    
    Args:
        question_data: 统一格式的问题数据，可以是字典或字符串
    
    Returns:
        dict: 适合执行的问题格式
    """
    # 兼容性处理：如果question_data是字符串，转换为single格式
    if isinstance(question_data, str):
        logging.info(f"检测到字符串格式问题，自动转换为single格式: {question_data}")
        return {
            'type': 'text',
            'questions': [question_data]
        }
    
    # 如果不是字典类型，返回None
    if not isinstance(question_data, dict):
        logging.warning(f"不支持的问题数据格式: {type(question_data)}")
        return None
    
    question_type = question_data.get("question_type", "single")
    content = question_data.get("content")
    
    if question_type == "single":
        return {
            'type': 'text',
            'questions': [content]
        }
    elif question_type == "multi":
        return {
            'type': 'text', 
            'questions': content
        }
    elif question_type == "image_text":
        return {
            'type': 'image_text',
            'question_data': content
        }
    elif question_type == "ai_generated":
        # 处理AI生成问题
        return process_ai_generated_question(content)
    elif question_type == "ai_followup":
        # 处理AI追问对话
        return process_ai_followup_question(content)
    else:
        logging.warning(f"未知的问题类型: {question_type}")
        return None

# AI问题生成相关功能
def load_ai_config(config_file="ai_config.json"):
    """加载AI配置文件
    
    Args:
        config_file: 配置文件路径
        
    Returns:
        dict: AI配置信息
    """
    default_config = {
        "ai_generation_config": {
            "default_provider": "ollama",
            "providers": {
                "ollama": {
                    "provider": "ollama",
                    "model": "qwen2.5:7b",
                    "base_url": "http://localhost:11434",
                    "temperature": 0.7,
                    "max_tokens": 1000,
                    "timeout": 120
                }
            }
        },
        "generation_settings": {
            "default_count": 3,
            "max_count": 10,
            "fallback_to_example": True,
            "retry_attempts": 2
        }
    }
    
    try:
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                logging.info(f"已加载AI配置文件: {config_file}")
                return config
        else:
            logging.info(f"AI配置文件不存在，使用默认配置: {config_file}")
            return default_config
    except Exception as e:
        logging.error(f"加载AI配置文件失败: {str(e)}, 使用默认配置")
        return default_config

def get_ai_api_config(provider_name=None):
    """获取API配置
    
    Args:
        provider_name: 提供商名称，None时使用默认提供商
        
    Returns:
        dict: API配置信息
    """
    config = load_ai_config()
    ai_config = config.get("ai_generation_config", {})
    
    if provider_name is None:
        provider_name = ai_config.get("default_provider", "ollama")
    
    providers = ai_config.get("providers", {})
    api_config = providers.get(provider_name)
    
    if not api_config:
        logging.warning(f"未找到提供商配置: {provider_name}, 使用默认配置")
        api_config = providers.get("ollama", {
            "provider": "ollama",
            "model": "qwen2.5:7b", 
            "base_url": "http://localhost:11434"
        })
    
    # 处理环境变量替换
    if api_config.get("api_key") and api_config["api_key"].startswith("${") and api_config["api_key"].endswith("}"):
        env_var = api_config["api_key"][2:-1]
        api_config["api_key"] = os.environ.get(env_var)
    
    return api_config

def generate_ai_questions(domain, intent_type, example_question, count, provider_name=None):
    """使用AI模型生成问题变种
    
    Args:
        domain: 问题领域（如"时尚领域"、"美食推荐"）
        intent_type: 意图类型（如"否定意图"、"特殊需求"）
        example_question: 示例问题
        count: 生成问题数量
        provider_name: API提供商名称，None时使用默认提供商
        
    Returns:
        list: 生成的问题列表，失败时返回None
    """
    import time
    
    start_time = time.time()
    try:
        logging.info(f"开始使用AI生成问题 - 领域: {domain}, 意图类型: {intent_type}, 数量: {count}")
        
        # 获取API配置
        api_config = get_ai_api_config(provider_name)
        
        # 构建prompt
        prompt = build_question_generation_prompt(domain, intent_type, example_question, count)
        
        # 根据API提供商调用相应的接口
        provider = api_config.get("provider", "ollama").lower()
        
        api_start_time = time.time()
        if provider == "ollama":
            generated_questions = call_ollama_api(prompt, api_config)
        elif provider == "openai":
            generated_questions = call_openai_api(prompt, api_config)
        elif provider == "custom":
            generated_questions = call_custom_api(prompt, api_config)
        else:
            logging.error(f"不支持的API提供商: {provider}")
            return None
        
        api_end_time = time.time()
        api_duration = api_end_time - api_start_time
        
        if generated_questions:
            total_duration = time.time() - start_time
            logging.info(f"✅ AI问题生成成功 - API耗时: {api_duration:.2f}秒, 总耗时: {total_duration:.2f}秒")
            logging.info(f"📝 成功生成 {len(generated_questions)} 个问题")
            for i, q in enumerate(generated_questions, 1):
                logging.info(f"  {i}. {q}")
            return generated_questions
        else:
            total_duration = time.time() - start_time
            logging.error(f"❌ AI问题生成失败 - 总耗时: {total_duration:.2f}秒")
            return None
            
    except Exception as e:
        total_duration = time.time() - start_time
        logging.error(f"AI生成问题时出错 - 耗时: {total_duration:.2f}秒, 错误: {str(e)}")
        logging.error(traceback.format_exc())
        return None

def build_question_generation_prompt(domain, intent_type, example_question, count):
    """构建AI问题生成的prompt
    
    Args:
        domain: 问题领域
        intent_type: 意图类型  
        example_question: 示例问题
        count: 生成数量
        
    Returns:
        str: 格式化的prompt
    """
    prompt = f"""你是一个专业的问题生成助手。请根据以下要求生成问题变种：

【任务要求】
- 领域：{domain}
- 意图类型：{intent_type}
- 参考示例：{example_question}
- 生成数量：{count}个

【生成规则】
1. 保持与示例问题相同的意图类型和领域
2. 变换表达方式、具体物品、颜色、场景等细节
3. 保持问题的自然性和实用性
4. 每个问题都应该是完整的、可以直接使用的
5. 问题之间要有一定差异性，避免重复

【输出格式】
请严格按照以下JSON格式输出，不要添加任何其他文字：
{{
    "questions": [
        "生成的问题1",
        "生成的问题2",
        "生成的问题3"
    ]
}}

【示例说明】
如果示例是关于"否定意图"的时尚问题，那么生成的问题也应该包含否定表达（如"不喜欢"、"不要"、"除了...之外"等）
如果示例是关于"特殊需求"的美食问题，那么生成的问题应该包含具体的限制条件或特殊要求

现在请生成{count}个符合要求的问题："""

    return prompt

def call_ollama_api(prompt, api_config):
    """调用Ollama API生成问题
    
    Args:
        prompt: 提示词
        api_config: API配置
        
    Returns:
        list: 生成的问题列表
    """
    try:
        base_url = api_config.get("base_url", "http://localhost:11434")
        model = api_config.get("model", "qwen2.5:7b")
        
        # Ollama API调用
        url = f"{base_url}/api/generate"
        
        payload = {
            "model": model,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": 0.7,
                "top_p": 0.9,
                "num_predict": 1000
            }
        }
        
        logging.info(f"调用Ollama API: {url}, 模型: {model}")
        response = requests.post(url, json=payload, timeout=120)
        response.raise_for_status()
        
        result = response.json()
        generated_text = result.get("response", "")
        
        # 解析生成的问题
        questions = parse_generated_questions(generated_text)
        return questions
        
    except Exception as e:
        logging.error(f"调用Ollama API失败: {str(e)}")
        return None

def call_openai_api(prompt, api_config):
    """调用OpenAI API生成问题
    
    Args:
        prompt: 提示词
        api_config: API配置
        
    Returns:
        list: 生成的问题列表
    """
    try:
        api_key = api_config.get("api_key")
        model = api_config.get("model", "gpt-3.5-turbo")
        base_url = api_config.get("base_url", "https://api.openai.com/v1")
        
        if not api_key:
            logging.error("OpenAI API密钥未配置")
            return None
        
        url = f"{base_url}/chat/completions"
        
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": model,
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.7,
            "max_tokens": 1000
        }
        
        logging.info(f"调用OpenAI API: {model}")
        response = requests.post(url, json=payload, headers=headers, timeout=120)
        response.raise_for_status()
        
        result = response.json()
        generated_text = result["choices"][0]["message"]["content"]
        
        # 解析生成的问题
        questions = parse_generated_questions(generated_text)
        return questions
        
    except Exception as e:
        logging.error(f"调用OpenAI API失败: {str(e)}")
        return None

def call_custom_api(prompt, api_config):
    """调用自定义API生成问题
    
    Args:
        prompt: 提示词
        api_config: API配置
        
    Returns:
        list: 生成的问题列表
    """
    try:
        url = api_config.get("url")
        headers = api_config.get("headers", {})
        
        if not url:
            logging.error("自定义API URL未配置")
            return None
        
        # 根据具体API格式构建请求
        payload = api_config.get("payload_template", {})
        if "prompt" in payload:
            payload["prompt"] = prompt
        else:
            payload = {"prompt": prompt}
        
        logging.info(f"调用自定义API: {url}")
        response = requests.post(url, json=payload, headers=headers, timeout=120)
        response.raise_for_status()
        
        result = response.json()
        # 根据API响应格式解析，这里假设返回字段为"text"
        generated_text = result.get("text", result.get("response", ""))
        
        # 解析生成的问题
        questions = parse_generated_questions(generated_text)
        return questions
        
    except Exception as e:
        logging.error(f"调用自定义API失败: {str(e)}")
        return None

def parse_generated_questions(generated_text):
    """解析AI生成的问题文本
    
    Args:
        generated_text: AI生成的文本
        
    Returns:
        list: 解析出的问题列表
    """
    try:
        logging.info("开始解析AI生成的问题文本")
        logging.debug(f"生成的原始文本: {generated_text}")
        
        # 首先尝试解析JSON格式
        try:
            # 查找JSON部分
            json_start = generated_text.find('{')
            json_end = generated_text.rfind('}') + 1
            
            if json_start != -1 and json_end > json_start:
                json_text = generated_text[json_start:json_end]
                parsed_data = json.loads(json_text)
                
                if "questions" in parsed_data:
                    questions = parsed_data["questions"]
                    logging.info(f"成功解析JSON格式，获得 {len(questions)} 个问题")
                    return [q.strip() for q in questions if q.strip()]
        except json.JSONDecodeError as e:
            logging.warning(f"JSON解析失败: {e}")
        
        # 如果JSON解析失败，尝试使用正则表达式提取
        questions = []
        
        # 方法1: 查找引号包围的问题
        import re
        quoted_patterns = [
            r'"([^"]+)"',  # 双引号
            r"'([^']+)'",  # 单引号
        ]
        
        for pattern in quoted_patterns:
            matches = re.findall(pattern, generated_text)
            if matches:
                questions.extend([q.strip() for q in matches if len(q.strip()) > 10])
        
        # 方法2: 查找以数字开头的行
        lines = generated_text.split('\n')
        for line in lines:
            line = line.strip()
            # 匹配 "1. 问题" 或 "1、问题" 格式
            if re.match(r'^\d+[.、]\s*(.+)', line):
                question = re.sub(r'^\d+[.、]\s*', '', line).strip()
                if len(question) > 5:
                    questions.append(question)
        
        # 方法3: 查找以"-"开头的行  
        for line in lines:
            line = line.strip()
            if line.startswith('- ') and len(line) > 8:
                question = line[2:].strip()
                questions.append(question)
        
        # 去重并过滤
        unique_questions = []
        seen = set()
        for q in questions:
            q_clean = q.strip()
            if q_clean and len(q_clean) > 5 and q_clean not in seen:
                # 过滤掉明显不是问题的文本
                if not any(keyword in q_clean.lower() for keyword in ['json', 'format', '格式', '输出', 'output']):
                    unique_questions.append(q_clean)
                    seen.add(q_clean)
        
        logging.info(f"使用正则表达式解析，获得 {len(unique_questions)} 个问题")
        return unique_questions
        
    except Exception as e:
        logging.error(f"解析生成的问题文本时出错: {str(e)}")
        return []

def process_ai_generated_question(question_data):
    """处理AI生成类型的问题
    
    Args:
        question_data: AI生成问题的配置数据
        
    Returns:
        dict: 格式化的问题数据，包含生成的问题列表
    """
    try:
        domain = question_data.get("domain", "")
        intent_type = question_data.get("intent_type", "")
        example_question = question_data.get("example_question", "")
        count = question_data.get("count", 3)
        
        # 移除对api_config的依赖，使用配置文件中的默认配置
        # provider_name可以从问题配置中获取，如果没有则使用默认
        provider_name = question_data.get("provider", None)  # 可选的提供商指定
        
        # 生成问题
        generated_questions = generate_ai_questions(domain, intent_type, example_question, count, provider_name)
        
        if generated_questions:
            return {
                'type': 'ai_generated',
                'questions': generated_questions,
                'metadata': {
                    'domain': domain,
                    'intent_type': intent_type,
                    'example_question': example_question,
                    'generated_count': len(generated_questions)
                }
            }
        else:
            # 如果AI生成失败，使用示例问题作为后备
            logging.warning("AI生成失败，使用示例问题作为后备")
            return {
                'type': 'text',
                'questions': [example_question],
                'metadata': {
                    'domain': domain,
                    'intent_type': intent_type,
                    'fallback': True
                }
            }
    except Exception as e:
        logging.error(f"处理AI生成问题时出错: {str(e)}")
        return None

def process_ai_followup_question(question_data):
    """处理AI追问对话类型的问题
    
    Args:
        question_data: AI追问对话的配置数据
        
    Returns:
        dict: 格式化的问题数据，包含追问对话信息
    """
    try:
        domain = question_data.get("domain", "")
        scenario = question_data.get("scenario", "")
        initial_question = question_data.get("initial_question", "")
        rounds_config = question_data.get("rounds", 5)
        provider_name = question_data.get("provider", None)
        
        # 检查是否有内部状态（用于恢复多轮对话）
        # 优先使用以_开头的内部状态字段
        current_round = question_data.get("_current_round", 1)
        conversation_history = question_data.get("_conversation_history", [])
        saved_total_rounds = question_data.get("_total_rounds")
        saved_round_type = question_data.get("_round_type")
        
        logging.info(f"处理AI追问问题 - 内部状态: current_round={current_round}, saved_total_rounds={saved_total_rounds}, saved_round_type={saved_round_type}")
        
        # 如果有保存的状态，使用保存的状态
        if saved_total_rounds and saved_round_type:
            total_rounds = saved_total_rounds
            round_type = saved_round_type
            logging.info(f"使用保存的状态: {round_type}, {current_round}/{total_rounds}")
        else:
            # 解析轮次配置（首次处理）
            if isinstance(rounds_config, int):
                # 固定轮次
                total_rounds = rounds_config
                round_type = "fixed"
            elif isinstance(rounds_config, dict):
                round_type = rounds_config.get("type", "fixed")
                if round_type == "random":
                    import random
                    min_rounds = rounds_config.get("min", 3)
                    max_rounds = rounds_config.get("max", 10)
                    total_rounds = random.randint(min_rounds, max_rounds)
                elif round_type == "ai_judge":
                    total_rounds = rounds_config.get("max", 15)
                else:
                    total_rounds = rounds_config.get("value", 5)  # 修复：使用value字段
            else:
                total_rounds = 5
                round_type = "fixed"
            logging.info(f"首次处理，解析轮次配置: {round_type}, {current_round}/{total_rounds}")
        
        return {
            'type': 'ai_followup',
            'initial_question': initial_question,
            'total_rounds': total_rounds,
            'round_type': round_type,
            'current_round': current_round,
            'conversation_history': conversation_history,
            'metadata': {
                'domain': domain,
                'scenario': scenario,
                'provider': provider_name,
                'original_rounds_config': rounds_config
            }
        }
        
    except Exception as e:
        logging.error(f"处理AI追问对话时出错: {str(e)}")
        return None

def generate_followup_question(conversation_history, domain, scenario, current_round, total_rounds, round_type, provider_name=None):
    """生成追问问题
    
    Args:
        conversation_history: 对话历史列表
        domain: 对话领域
        scenario: 对话场景
        current_round: 当前轮次
        total_rounds: 总轮次
        round_type: 轮次类型
        provider_name: API提供商名称
        
    Returns:
        dict: 包含生成的问题和是否结束对话的信息
    """
    import time
    
    start_time = time.time()
    try:
        logging.info(f"开始生成AI追问问题 - 轮次: {current_round}/{total_rounds}, 领域: {domain}")
        
        # 构建追问prompt
        prompt = build_followup_prompt(conversation_history, domain, scenario, current_round, total_rounds, round_type)
        
        # 获取API配置
        api_config = get_ai_api_config(provider_name)
        
        # 根据API提供商调用相应的接口
        provider = api_config.get("provider", "ollama").lower()
        
        api_start_time = time.time()
        if provider == "ollama":
            result = call_ollama_followup_api(prompt, api_config)
        elif provider == "openai":
            result = call_openai_followup_api(prompt, api_config)
        elif provider == "custom":
            result = call_custom_followup_api(prompt, api_config)
        else:
            logging.error(f"不支持的API提供商: {provider}")
            return None
        
        api_end_time = time.time()
        api_duration = api_end_time - api_start_time
        
        if result:
            total_duration = time.time() - start_time
            logging.info(f"✅ AI追问问题生成成功 - API耗时: {api_duration:.2f}秒, 总耗时: {total_duration:.2f}秒")
            
            # 确保result是字典格式
            if isinstance(result, str):
                # 如果返回的是字符串，包装成字典格式
                result = {
                    "question": result,
                    "should_end": False,
                    "reason": "AI生成的问题"
                }
            elif isinstance(result, dict):
                # 如果是字典，验证必要字段
                if 'question' not in result:
                    result['question'] = "生成的追问问题解析失败"
                if 'should_end' not in result:
                    result['should_end'] = False
                if 'reason' not in result:
                    result['reason'] = "问题生成成功"
            else:
                # 其他情况，创建默认字典
                result = {
                    "question": "追问问题生成异常",
                    "should_end": False,
                    "reason": "返回值格式错误"
                }
            
            logging.info(f"📝 生成的问题: {result.get('question', 'N/A')}")
            logging.info(f"🎯 设计原因: {result.get('reason', 'N/A')}")
            
            # 在结果中添加耗时信息
            result['generation_time'] = total_duration
            result['api_time'] = api_duration
        else:
            total_duration = time.time() - start_time
            logging.error(f"❌ AI追问问题生成失败 - 总耗时: {total_duration:.2f}秒")
        
        return result
        
    except Exception as e:
        total_duration = time.time() - start_time
        logging.error(f"生成追问问题时出错 - 耗时: {total_duration:.2f}秒, 错误: {str(e)}")
        logging.error(traceback.format_exc())
        # 返回默认结果而不是None
        return {
            "question": "AI生成追问失败，请继续对话",
            "should_end": False,
            "reason": f"生成失败: {str(e)}"
        }

def build_followup_prompt(conversation_history, domain, scenario, current_round, total_rounds, round_type):
    """构建追问对话的prompt
    
    Args:
        conversation_history: 对话历史
        domain: 对话领域
        scenario: 对话场景
        current_round: 当前轮次
        total_rounds: 总轮次
        round_type: 轮次类型
        
    Returns:
        str: 格式化的prompt
    """
    
    # 构建对话历史文本
    history_text = ""
    for i, item in enumerate(conversation_history, 1):
        if isinstance(item, dict):
            # 支持两种格式：{'question': x, 'answer': y} 或 {'role': x, 'content': y}
            if 'question' in item and 'answer' in item:
                # 原始格式
                history_text += f"第{i}轮:\n问: {item['question']}\n答: {item['answer']}\n\n"
            elif 'role' in item and 'content' in item:
                # OpenAI格式
                role_text = "问" if item['role'] == 'user' else "答"
                history_text += f"{role_text}: {item['content']}\n"
        else:
            # 如果是字符串格式，直接添加
            history_text += f"第{i}轮: {str(item)}\n\n"
    
    # 根据轮次类型设置不同的结束策略
    if round_type == "ai_judge":
        end_strategy = "如果你认为对话已经达到满意的结果或用户的问题已经充分解决，设置 \"should_end\": true"
    elif round_type == "fixed":
        end_strategy = f"这是{total_rounds}轮固定对话，当前第{current_round}轮"
    else:  # random
        end_strategy = f"这是随机{total_rounds}轮对话，当前第{current_round}轮"
    
    prompt = f"""你是一名专业的AI数字人评测专家，正在和对AI数字人对话，进行{domain}领域的对话能力测试和评估。

【测试场景】
- 测试领域：{domain}
- 测试场景：{scenario}
- 当前轮次：{current_round}/{total_rounds}
- 测试策略：{end_strategy}

【对话历史】
{history_text if history_text else "测试刚开始"}

【测试任务】
作为AI数字人评测专家，你需要：
1. **关键**：仔细分析AI数字人在上一轮的回答（即"答："后面的内容）
2. 基于AI数字人的实际回答内容，设计下一个测试问题
3. 测试问题应该延续AI数字人回答的话题方向，深入测试其专业能力
4. 问题要有针对性，能够有效评估AI数字人的知识储备和应答能力
5. 保持测试的连贯性和逻辑性，形成完整的测试链条
6. 如果是最后一轮，可以设计总结性或挑战性问题

【输出格式】
请严格按照以下JSON格式输出，不要添加任何其他文字：
{{
    "question": "你的测试问题",
    "should_end": false,
    "reason": "设计这个测试问题的原因和评测目标"
}}

【测试示例】
- 如果AI数字人回答"我更了解时尚和太古，让我们换个话题聊聊吧！比如，你最近有没有想买的单品？"
  测试追问："那请详细介绍一下太古里最值得推荐的几个时尚品牌及其特色"（测试专业知识深度）
- 如果AI数字人回答"我推荐苹果MacBook Air，性价比很高，适合日常办公"
  测试追问："请具体对比MacBook Air M1和M2芯片的性能差异，包括价格和适用场景"（测试技术细节掌握）
- 如果AI数字人回答"根据您的需求，我觉得这几款都很适合您"
  测试追问："请分别说明这几款产品的核心优势和潜在缺点，以及针对不同用户群体的推荐理由"（测试分析能力）

【评测重点】
- 测试AI数字人的专业知识掌握程度
- 评估其回答的准确性和完整性
- 检验其是否能够提供有价值的信息
- 观察其话题引导和对话管理能力
- 评价其回答的逻辑性和连贯性

【重要提醒】
请务必基于AI数字人的实际回答内容设计测试问题，不要回到用户的原始问题！你的目标是全面评估AI数字人的对话能力。

现在请生成下一个测试问题："""

    return prompt

def call_ollama_followup_api(prompt, api_config):
    """调用Ollama API生成追问问题"""
    try:
        base_url = api_config.get("base_url", "http://localhost:11434")
        model = api_config.get("model", "qwen2.5:7b")
        
        url = f"{base_url}/api/generate"
        
        payload = {
            "model": model,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": 0.7,
                "top_p": 0.9,
                "num_predict": 500
            }
        }
        
        logging.info(f"调用Ollama API生成追问: {url}, 模型: {model}")
        response = requests.post(url, json=payload, timeout=120)
        response.raise_for_status()
        
        result = response.json()
        generated_text = result.get("response", "")
        
        # 解析生成的追问结果
        followup_result = parse_followup_response(generated_text)
        return followup_result
        
    except Exception as e:
        logging.error(f"调用Ollama API生成追问失败: {str(e)}")
        return None

def call_openai_followup_api(prompt, api_config):
    """调用OpenAI API生成追问问题"""
    try:
        api_key = api_config.get("api_key")
        model = api_config.get("model", "gpt-3.5-turbo")
        base_url = api_config.get("base_url", "https://api.openai.com/v1")
        
        if not api_key:
            logging.error("OpenAI API密钥未配置")
            return None
        
        url = f"{base_url}/chat/completions"
        
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": model,
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.7,
            "max_tokens": 500
        }
        
        logging.info(f"调用OpenAI API生成追问: {model}")
        response = requests.post(url, json=payload, headers=headers, timeout=120)
        response.raise_for_status()
        
        result = response.json()
        generated_text = result["choices"][0]["message"]["content"]
        
        # 解析生成的追问结果
        followup_result = parse_followup_response(generated_text)
        return followup_result
        
    except Exception as e:
        logging.error(f"调用OpenAI API生成追问失败: {str(e)}")
        return None

def call_custom_followup_api(prompt, api_config):
    """调用自定义API生成追问问题"""
    try:
        url = api_config.get("url")
        headers = api_config.get("headers", {})
        
        if not url:
            logging.error("自定义API URL未配置")
            return None
        
        payload = api_config.get("payload_template", {})
        if "prompt" in payload:
            payload["prompt"] = prompt
        else:
            payload = {"prompt": prompt}
        
        logging.info(f"调用自定义API生成追问: {url}")
        response = requests.post(url, json=payload, headers=headers, timeout=120)
        response.raise_for_status()
        
        result = response.json()
        generated_text = result.get("text", result.get("response", ""))
        
        # 解析生成的追问结果
        followup_result = parse_followup_response(generated_text)
        return followup_result
        
    except Exception as e:
        logging.error(f"调用自定义API生成追问失败: {str(e)}")
        return None

def parse_followup_response(generated_text):
    """解析AI追问生成的响应
    
    Args:
        generated_text: AI生成的原始文本
        
    Returns:
        str: 解析出的追问问题，失败时返回None
    """
    try:
        logging.info(f"开始解析AI追问响应...")
        logging.debug(f"原始响应文本: {generated_text}")
        
        # 方法1: 尝试解析JSON格式
        import json
        import re
        
        # 提取JSON部分
        json_match = re.search(r'\{.*\}', generated_text, re.DOTALL)
        if json_match:
            try:
                json_str = json_match.group()
                data = json.loads(json_str)
                if 'question' in data:
                    question = data['question'].strip()
                    if question:
                        logging.info(f"✅ JSON格式解析成功: {question}")
                        return question
            except json.JSONDecodeError:
                pass
        
        # 方法2: 提取问号结尾的句子
        lines = generated_text.split('\n')
        for line in lines:
            line = line.strip()
            if line and line.endswith('？') and len(line) > 3:
                logging.info(f"✅ 问句解析成功: {line}")
                return line
        
        # 方法3: 提取"问:"或"追问:"后的内容
        question_patterns = [
            r'问[:：]\s*(.+)',
            r'追问[:：]\s*(.+)',
            r'测试问题[:：]\s*(.+)',
            r'[^\n]*\?$',
            r'[^\n]*？$'
        ]
        
        for pattern in question_patterns:
            matches = re.findall(pattern, generated_text, re.MULTILINE)
            if matches:
                question = matches[0].strip()
                if question and len(question) > 3:
                    logging.info(f"✅ 模式匹配解析成功: {question}")
                    return question
        
        # 方法4: 直接使用第一个非空行作为问题
        for line in lines:
            line = line.strip()
            if line and len(line) > 5 and not line.startswith(('你', '我', '根据', '基于')):
                logging.info(f"✅ 首行解析成功: {line}")
                return line
        
        logging.error("❌ 无法解析出有效的追问问题")
        return None
        
    except Exception as e:
        logging.error(f"解析AI追问响应时出错: {str(e)}")
        return None

# ==================== AI回答评测功能 ====================

def load_evaluation_config(config_file="ai_config.json"):
    """加载AI评测配置
    
    Args:
        config_file: 配置文件路径
        
    Returns:
        dict: 评测配置，如果未启用则返回None
    """
    try:
        ai_config = load_ai_config(config_file)
        eval_config = ai_config.get("ai_evaluation_config", {})
        
        if not eval_config.get("enabled", False):
            return None
            
        return eval_config
    except Exception as e:
        logging.error(f"加载评测配置失败: {str(e)}")
        return None

def get_evaluation_api_config(provider_name=None):
    """获取评测API配置
    
    Args:
        provider_name: 指定的提供商名称，None时使用默认
        
    Returns:
        dict: API配置
    """
    try:
        eval_config = load_evaluation_config()
        if not eval_config:
            return None
            
        providers = eval_config.get("providers", {})
        
        if provider_name and provider_name in providers:
            api_config = providers[provider_name]
        else:
            default_provider = eval_config.get("default_provider", "ollama_eval")
            api_config = providers.get(default_provider, {})
        
        # 处理环境变量替换
        if api_config.get("api_key") and api_config["api_key"].startswith("${") and api_config["api_key"].endswith("}"):
            env_var = api_config["api_key"][2:-1]
            api_config["api_key"] = os.environ.get(env_var)
        
        return api_config
    except Exception as e:
        logging.error(f"获取评测API配置失败: {str(e)}")
        return None

def evaluate_ai_response(question, answer, conversation_history=None, provider_name=None):
    """评测AI回答质量
    
    Args:
        question (str): 用户问题
        answer (str): AI回答
        conversation_history (list): 对话历史
        provider_name (str): API提供商名称
        
    Returns:
        dict: 评测结果，包含各维度评分和总体评价
    """
    try:
        start_time = time.time()
        logging.info("开始评测AI回答质量...")
        
        # 加载评测配置
        config = load_evaluation_config()
        if not config or not config.get('enabled', False):
            logging.info("AI评测功能未启用，跳过评测")
            return None
        
        # 获取API配置
        api_config = get_evaluation_api_config(provider_name)
        if not api_config:
            logging.warning("评测API配置无效，跳过评测")
            return None
        
        # 构建评测prompt
        prompt = build_evaluation_prompt(question, answer, conversation_history)
        
        # 调用API
        provider = api_config.get("provider")
        api_start_time = time.time()
        
        result = None
        try:
            if provider == "ollama":
                result = call_ollama_evaluation_api(prompt, api_config)
            elif provider == "openai":
                result = call_openai_evaluation_api(prompt, api_config)
            elif provider == "custom":
                result = call_custom_evaluation_api(prompt, api_config)
            else:
                logging.error(f"不支持的评测API提供商: {provider}")
                return None
        except Exception as api_error:
            api_duration = time.time() - api_start_time
            logging.error(f"调用评测API失败 - 耗时: {api_duration:.2f}秒, 错误: {str(api_error)}")
            # 返回空结果而不是抛出异常
            return None
        
        api_duration = time.time() - api_start_time
        
        if result:
            total_duration = time.time() - start_time
            logging.info(f"✅ AI回答评测成功 - API耗时: {api_duration:.2f}秒, 总耗时: {total_duration:.2f}秒")
            
            # 在结果中添加耗时信息
            result['evaluation_time'] = total_duration
            result['api_time'] = api_duration
            return result
        else:
            total_duration = time.time() - start_time
            logging.warning(f"⚠️ AI回答评测返回空结果 - 总耗时: {total_duration:.2f}秒")
            return None
        
    except Exception as e:
        total_duration = time.time() - start_time
        logging.error(f"❌ AI回答评测失败 - 总耗时: {total_duration:.2f}秒")
        logging.error(f"评测错误详情: {str(e)}")
        # 返回None而不是抛出异常
        return None

def build_evaluation_prompt(question, answer, conversation_history=None):
    """构建AI回答评测的prompt
    
    Args:
        question: 用户问题
        answer: AI回答
        conversation_history: 对话历史
        
    Returns:
        str: 格式化的评测prompt
    """
    # 获取评测设置
    ai_config = load_ai_config()
    eval_settings = ai_config.get("evaluation_settings", {})
    score_scale = eval_settings.get("score_scale", 5)
    dimensions = eval_settings.get("evaluation_dimensions", [
        "理解力", "记忆力", "专业性", "情商话术", "知识储备", "合规性"
    ])
    domain = eval_settings.get("domain", "时尚")
    
    # 构建对话历史部分
    history_text = ""
    if conversation_history:
        history_text = "\n【对话历史】\n"
        for i, (q, a) in enumerate(conversation_history[-3:], 1):  # 只显示最近3轮
            history_text += f"第{i}轮:\n问: {q}\n答: {a}\n\n"
    
    prompt = f"""你是一名专业的AI助手评测专家，专门评估{domain}领域AI数字人的回答质量。请对以下AI回答进行全面、客观的评测。

{history_text}
【当前对话】
用户问题: {question}
AI回答: {answer}

【评测要求】
请从以下{len(dimensions)}个维度对AI回答进行评测，每个维度使用{score_scale}分制评分：

1. **理解力** (1-{score_scale}分): AI是否准确理解了用户的问题意图、需求和上下文
2. **记忆力** (1-{score_scale}分): AI是否能够记住和引用之前的对话内容，保持对话连贯性
3. **专业性** (1-{score_scale}分): AI在{domain}领域的专业知识是否准确、权威、深入
4. **情商话术** (1-{score_scale}分): AI的表达是否得体、亲和、有感染力，能否恰当处理用户情绪
5. **知识储备** (1-{score_scale}分): AI回答中体现的知识面是否广泛、信息是否丰富实用
6. **合规性** (1-{score_scale}分): AI回答是否合规合法，无有害内容，符合{domain}行业标准

【评分标准】
- {score_scale}分: 优秀，完全满足要求，表现卓越
- {score_scale-1}分: 良好，基本满足要求，有轻微不足
- {score_scale-2}分: 一般，部分满足要求，有明显不足
- {score_scale-3}分: 较差，勉强满足要求，有严重不足
- 1分: 极差，完全不满足要求

【输出格式】
请严格按照以下JSON格式输出，不要添加任何其他文字：
{{
    "detailed_scores": {{
        "理解力": {{"score": 4, "reason": "详细评分理由"}},
        "记忆力": {{"score": 3, "reason": "详细评分理由"}},
        "专业性": {{"score": 5, "reason": "详细评分理由"}},
        "情商话术": {{"score": 4, "reason": "详细评分理由"}},
        "知识储备": {{"score": 4, "reason": "详细评分理由"}},
        "合规性": {{"score": 5, "reason": "详细评分理由"}}
    }},
    "overall_score": 4.2,
    "evaluation_summary": "整体评价：AI回答在专业性和合规性方面表现优秀...",
    "suggestions": "改进建议：1. 可以更好地结合用户的个人偏好... 2. 在某些细节上可以更加深入..."
}}

请现在开始评测："""

    return prompt

def call_ollama_evaluation_api(prompt, api_config):
    """调用Ollama API进行回答评测
    
    Args:
        prompt: 评测提示词
        api_config: API配置
        
    Returns:
        dict: 评测结果
    """
    try:
        base_url = api_config.get("base_url", "http://localhost:11434")
        model = api_config.get("model", "qwen2.5:32b")
        
        url = f"{base_url}/api/generate"
        
        payload = {
            "model": model,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": 0.3,  # 降低温度确保评测的一致性
                "top_p": 0.9,
                "num_predict": 1500
            }
        }
        
        logging.info(f"调用Ollama评测API: {url}, 模型: {model}")
        response = requests.post(url, json=payload, timeout=60)
        response.raise_for_status()
        
        result = response.json()
        generated_text = result.get("response", "")
        
        # 解析评测结果
        evaluation_result = parse_evaluation_response(generated_text)
        return evaluation_result
        
    except Exception as e:
        logging.error(f"调用Ollama评测API失败: {str(e)}")
        return None

def call_openai_evaluation_api(prompt, api_config):
    """调用OpenAI API进行回答评测
    
    Args:
        prompt: 评测提示词
        api_config: API配置
        
    Returns:
        dict: 评测结果
    """
    try:
        api_key = api_config.get("api_key")
        model = api_config.get("model", "gpt-4")
        base_url = api_config.get("base_url", "https://api.openai.com/v1")
        
        if not api_key:
            logging.error("OpenAI API密钥未配置")
            return None
        
        url = f"{base_url}/chat/completions"
        
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": model,
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.3,
            "max_tokens": 1500
        }
        
        logging.info(f"调用OpenAI评测API: {model}")
        response = requests.post(url, json=payload, headers=headers, timeout=60)
        response.raise_for_status()
        
        result = response.json()
        generated_text = result["choices"][0]["message"]["content"]
        
        # 解析评测结果
        evaluation_result = parse_evaluation_response(generated_text)
        return evaluation_result
        
    except Exception as e:
        logging.error(f"调用OpenAI评测API失败: {str(e)}")
        return None

def call_custom_evaluation_api(prompt, api_config):
    """调用自定义API进行回答评测
    
    Args:
        prompt: 评测提示词
        api_config: API配置
        
    Returns:
        dict: 评测结果
    """
    try:
        url = api_config.get("url")
        headers = api_config.get("headers", {})
        payload_template = api_config.get("payload_template", {})
        
        # 构建请求负载
        payload = payload_template.copy()
        payload["prompt"] = prompt
        
        logging.info(f"调用自定义评测API: {url}")
        response = requests.post(url, json=payload, headers=headers, timeout=60)
        response.raise_for_status()
        
        result = response.json()
        generated_text = result.get("response", result.get("text", ""))
        
        # 解析评测结果
        evaluation_result = parse_evaluation_response(generated_text)
        return evaluation_result
        
    except Exception as e:
        logging.error(f"调用自定义评测API失败: {str(e)}")
        return None

def parse_evaluation_response(generated_text):
    """解析AI评测响应
    
    Args:
        generated_text: AI生成的评测文本
        
    Returns:
        dict: 解析出的评测结果
    """
    try:
        import json
        import re
        
        logging.info(f"开始解析AI评测响应...")
        logging.debug(f"原始评测文本: {generated_text}")
        
        # 尝试提取JSON格式
        json_match = re.search(r'\{.*\}', generated_text, re.DOTALL)
        if json_match:
            try:
                json_str = json_match.group()
                data = json.loads(json_str)
                
                # 验证必要字段
                if 'detailed_scores' in data:
                    # 计算综合评分（如果没有提供）
                    if 'overall_score' not in data:
                        scores = []
                        for dimension, info in data['detailed_scores'].items():
                            if isinstance(info, dict) and 'score' in info:
                                scores.append(info['score'])
                        if scores:
                            data['overall_score'] = round(sum(scores) / len(scores), 1)
                    
                    logging.info(f"✅ 评测结果解析成功，综合评分: {data.get('overall_score', 'N/A')}")
                    return data
                    
            except json.JSONDecodeError as e:
                logging.error(f"JSON解析失败: {str(e)}")
        
        # 备用解析：尝试从文本中提取评分信息
        logging.warning("JSON格式解析失败，尝试文本解析...")
        
        # 简单的备用解析逻辑
        scores = {}
        dimensions = ["理解力", "记忆力", "专业性", "情商话术", "知识储备", "合规性"]
        
        for dimension in dimensions:
            # 查找类似 "理解力: 4分" 或 "理解力: 4" 的模式
            pattern = rf'{dimension}[：:]\s*(\d+)'
            match = re.search(pattern, generated_text)
            if match:
                score = int(match.group(1))
                scores[dimension] = {"score": score, "reason": "评分理由解析失败"}
        
        if scores:
            overall_score = round(sum(info['score'] for info in scores.values()) / len(scores), 1)
            result = {
                "detailed_scores": scores,
                "overall_score": overall_score,
                "evaluation_summary": "评测结果解析不完整",
                "suggestions": "建议检查评测模型输出格式"
            }
            logging.info(f"✅ 备用解析成功，综合评分: {overall_score}")
            return result
        
        logging.error("❌ 无法解析出有效的评测结果")
        return None
        
    except Exception as e:
        logging.error(f"解析AI评测响应时出错: {str(e)}")
        return None

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='TikaBot聊天自动化')
    parser.add_argument('--test-mode', action='store_true', help='测试模式，只运行少量对话')
    parser.add_argument('--questions-file', type=str, help='合并的问题文件路径')
    parser.add_argument('--access-mode', type=str, choices=['random', 'sequential'], help='问题访问模式')
    
    args = parser.parse_args()
    
    run_tk_chat(test_mode=args.test_mode, questions_file=args.questions_file, access_mode=args.access_mode)
    
    print("脚本执行完毕.")
    print(f"截图已保存到: {os.path.join(os.getcwd(), 'screenshots')}")
    print(f"对话记录已保存到: {os.path.join(os.getcwd(), 'results')}")
    print(f"日志已保存到: {os.path.join(os.getcwd(), 'logs')}")