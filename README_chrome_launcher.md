# Chrome批量启动器

这个工具可以批量启动Chrome浏览器，为每个URL创建独立的浏览器进程，支持用户操作完成后统一关闭所有进程。

## 功能特点

- ✅ 批量读取URL文件，每行一个URL
- ✅ 为每个URL启动独立的Chrome浏览器进程
- ✅ 每个进程使用独立的用户数据目录，避免冲突
- ✅ 支持实时查看进程状态
- ✅ 支持用户操作完成后统一关闭所有进程
- ✅ 提供Python和Bash两个版本

## 文件说明

- `batch_chrome_launcher.py` - Python版本（推荐）
- `batch_chrome_launcher.sh` - Bash版本
- `test_urls_pre.txt` - URL列表文件
- `README_chrome_launcher.md` - 使用说明

## 使用方法

### Python版本（推荐）

```bash
# 使用默认的test_urls_pre.txt文件
python3 batch_chrome_launcher.py

# 或指定其他URL文件
python3 batch_chrome_launcher.py your_urls.txt
```

### Bash版本

```bash
# 使用默认的test_urls_pre.txt文件
./batch_chrome_launcher.sh

# 或指定其他URL文件
./batch_chrome_launcher.sh your_urls.txt
```

## URL文件格式

URL文件每行一个URL，支持：
- 空行（会被忽略）
- 以`#`开头的注释行（会被忽略）

示例：
```
# 这是注释行
https://example1.com
https://example2.com

https://example3.com
# 另一个注释
https://example4.com
```

## 交互命令

程序启动所有浏览器后，会等待用户输入命令：

- `status` 或 `s` - 查看当前进程状态
- `quit` 或 `q` - 关闭所有浏览器进程并退出
- `help` 或 `h` - 显示帮助信息
- `Ctrl+C` - 强制关闭所有进程并退出

## 系统要求

### Python版本
- Python 3.6+
- 已安装Google Chrome或Chromium浏览器

### Bash版本
- Bash shell
- 已安装Google Chrome或Chromium浏览器

## 支持的操作系统

- ✅ macOS
- ✅ Linux
- ✅ Windows (WSL)

## Chrome浏览器检测

脚本会自动检测以下位置的Chrome浏览器：

### macOS
- `/Applications/Google Chrome.app/Contents/MacOS/Google Chrome`
- `/Applications/Chromium.app/Contents/MacOS/Chromium`

### Linux
- `/usr/bin/google-chrome`
- `/usr/bin/google-chrome-stable`
- `/usr/bin/chromium-browser`
- `/usr/bin/chromium`

### Windows (WSL)
- `/mnt/c/Program Files/Google/Chrome/Application/chrome.exe`
- `/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe`

## 使用示例

1. 准备URL文件（如`test_urls_pre.txt`）
2. 运行脚本：
   ```bash
   python3 batch_chrome_launcher.py
   ```
3. 等待所有浏览器启动完成
4. 在各个浏览器中进行需要的操作
5. 操作完成后，在终端输入`quit`关闭所有浏览器

## 注意事项

1. **资源消耗**：每个Chrome进程会消耗一定的内存和CPU资源，请根据系统配置合理控制URL数量
2. **进程隔离**：每个浏览器进程使用独立的用户数据目录，确保会话隔离
3. **临时文件**：程序会在`/tmp`目录创建临时的用户数据目录，程序结束时会自动清理
4. **网络连接**：确保网络连接正常，避免URL无法访问

## 故障排除

### Chrome未找到
如果提示"未找到Chrome浏览器"，请：
1. 确认已安装Google Chrome或Chromium
2. 检查安装路径是否在脚本的检测列表中
3. 尝试将Chrome添加到系统PATH环境变量

### 进程启动失败
如果某些进程启动失败：
1. 检查URL格式是否正确
2. 确认网络连接正常
3. 检查系统资源是否充足

### 进程无法关闭
如果进程无法正常关闭：
1. 尝试使用`Ctrl+C`强制退出
2. 手动使用系统任务管理器关闭Chrome进程
3. 重启终端会话

## 许可证

此脚本仅供学习和测试使用，请遵守相关网站的使用条款。
