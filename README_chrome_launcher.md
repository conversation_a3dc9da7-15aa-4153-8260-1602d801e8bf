# Chrome批量启动器（支持移动设备模拟）

这个工具可以批量启动Chrome浏览器，为每个URL创建独立的浏览器进程，**支持移动设备模拟**，适用于移动端网页测试和自动化操作。

## 功能特点

- ✅ 批量读取URL文件，每行一个URL
- ✅ 为每个URL启动独立的Chrome浏览器进程
- ✅ **支持移动设备模拟**（iPhone、iPad、Android等）
- ✅ 基于Playwright，提供更稳定的浏览器控制
- ✅ 自动排列浏览器窗口位置，便于操作
- ✅ 支持实时查看浏览器状态
- ✅ 支持用户操作完成后统一关闭所有浏览器
- ✅ 提供Python（Playwright）和Bash两个版本

## 文件说明

- `batch_chrome_simple_mobile.py` - 移动设备模拟版本（**推荐**）
- `batch_chrome_launcher.py` - 标准版本（支持移动设备参数）
- `batch_chrome_launcher.sh` - Bash版本（传统方式）
- `auto_enable_mobile_mode.py` - 自动启用移动设备模拟工具
- `test_urls_pre.txt` - 完整URL列表文件（40个URL）
- `test_urls_small.txt` - 小测试文件（3个URL）
- `install_dependencies.sh` - 依赖检查脚本
- `README_chrome_launcher.md` - 使用说明

## 安装依赖

### Python版本依赖安装

```bash
# 运行安装脚本（可选）
./install_dependencies.sh

# 或者手动安装（Python版本无需额外依赖）
# 只需要Python 3.6+和Chrome浏览器
```

## 使用方法

### Python版本（推荐，支持移动设备模拟）

```bash
# 使用移动设备模拟版本（推荐）
python3 batch_chrome_simple_mobile.py

# 使用小测试文件
python3 batch_chrome_simple_mobile.py test_urls_small.txt

# 指定设备类型
python3 batch_chrome_simple_mobile.py test_urls_small.txt "iPad Pro"

# 使用标准版本
python3 batch_chrome_launcher.py your_urls.txt "iPhone 12"
```

#### 支持的移动设备类型

- `iPhone 13 Pro` （默认）
- `iPhone 13`
- `iPhone 12`
- `iPhone SE`
- `iPad Pro`
- `iPad`
- `Samsung Galaxy S21`
- `Samsung Galaxy Note 20`
- `Pixel 5`
- `Pixel 4`

### Bash版本

```bash
# 使用默认的test_urls_pre.txt文件
./batch_chrome_launcher.sh

# 或指定其他URL文件
./batch_chrome_launcher.sh your_urls.txt
```

## URL文件格式

URL文件每行一个URL，支持：
- 空行（会被忽略）
- 以`#`开头的注释行（会被忽略）

示例：
```
# 这是注释行
https://example1.com
https://example2.com

https://example3.com
# 另一个注释
https://example4.com
```

## 交互命令

程序启动所有浏览器后，会等待用户输入命令：

- `status` 或 `s` - 查看当前进程状态
- `quit` 或 `q` - 关闭所有浏览器进程并退出
- `help` 或 `h` - 显示帮助信息
- `Ctrl+C` - 强制关闭所有进程并退出

## 系统要求

### Python版本（推荐）
- Python 3.6+
- 已安装Google Chrome或Chromium浏览器

### Bash版本
- Bash shell
- 已安装Google Chrome或Chromium浏览器

## 支持的操作系统

- ✅ macOS
- ✅ Linux
- ✅ Windows (WSL)

## Chrome浏览器检测

脚本会自动检测以下位置的Chrome浏览器：

### macOS
- `/Applications/Google Chrome.app/Contents/MacOS/Google Chrome`
- `/Applications/Chromium.app/Contents/MacOS/Chromium`

### Linux
- `/usr/bin/google-chrome`
- `/usr/bin/google-chrome-stable`
- `/usr/bin/chromium-browser`
- `/usr/bin/chromium`

### Windows (WSL)
- `/mnt/c/Program Files/Google/Chrome/Application/chrome.exe`
- `/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe`

## 使用示例

### 基本使用流程

1. **确保已安装Chrome浏览器**

2. **准备URL文件**（如`test_urls_pre.txt`）

3. **运行脚本**：
   ```bash
   # 使用默认设备（iPhone 13 Pro）
   python3 batch_chrome_launcher.py

   # 或使用其他设备
   python3 batch_chrome_launcher.py test_urls_pre.txt "iPad Pro"
   ```

4. **等待所有浏览器启动完成**（会自动排列窗口位置）

5. **在各个浏览器中进行需要的操作**（每个浏览器都模拟移动设备）

6. **操作完成后，在终端输入`quit`关闭所有浏览器**

### 移动设备模拟效果

- ✅ 自动设置移动设备的视口大小
- ✅ 模拟移动设备的User-Agent
- ✅ 支持触摸事件
- ✅ 设置中文语言环境和上海时区

## 注意事项

1. **资源消耗**：每个Chrome进程会消耗一定的内存和CPU资源，请根据系统配置合理控制URL数量
2. **进程隔离**：每个浏览器进程使用独立的用户数据目录，确保会话隔离
3. **临时文件**：程序会在`/tmp`目录创建临时的用户数据目录，程序结束时会自动清理
4. **网络连接**：确保网络连接正常，避免URL无法访问

## 故障排除

### Chrome未找到
如果提示"未找到Chrome浏览器"，请：
1. 确认已安装Google Chrome或Chromium
2. 检查安装路径是否在脚本的检测列表中
3. 尝试将Chrome添加到系统PATH环境变量

### 进程启动失败
如果某些进程启动失败：
1. 检查URL格式是否正确
2. 确认网络连接正常
3. 检查系统资源是否充足

### 进程无法关闭
如果进程无法正常关闭：
1. 尝试使用`Ctrl+C`强制退出
2. 手动使用系统任务管理器关闭Chrome进程
3. 重启终端会话

## 许可证

此脚本仅供学习和测试使用，请遵守相关网站的使用条款。
