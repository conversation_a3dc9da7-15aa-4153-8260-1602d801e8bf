# 图文问题类型功能说明

## 功能概述

本次更新为TikaBot聊天自动化工具新增了图文问题类型支持，允许在对话中发送包含图片的问题。

## 功能特性

- ✅ 支持URL图片和本地图片文件
- ✅ 自动下载URL图片到本地
- ✅ 图片格式验证和大小检查
- ✅ 自动上传图片到聊天界面
- ✅ 发送图片后继续发送文本消息
- ✅ 与现有文本问题类型无缝集成

## 配置格式

在`default_questions.json`文件中添加`image_text_questions`数组：

```json
{
  "version": "1.0",
  "single_questions": [...],
  "multi_questions": [...],
  "image_text_questions": [
    {
      "text": "这件衣服好看吗？",
      "image": "https://example.com/sample1.jpg",
      "imageType": "url"
    },
    {
      "text": "这个包包怎么样？",
      "image": "/path/to/local/image.jpg",
      "imageType": "local"
    }
  ]
}
```

### 字段说明

- `text`: 文本问题内容
- `image`: 图片路径或URL
- `imageType`: 图片类型
  - `"url"`: 网络图片URL，会自动下载
  - `"local"`: 本地图片文件路径

## 使用方式

### 1. 准备图片文件

对于本地图片，建议将图片放在`test_images/`目录下：

```bash
mkdir -p test_images
# 将图片文件复制到此目录
```

### 2. 配置问题文件

在`default_questions.json`中添加图文问题：

```json
{
  "image_text_questions": [
    {
      "text": "这套穿搭怎么样？",
      "image": "test_images/outfit.jpg", 
      "imageType": "local"
    },
    {
      "text": "这个颜色好看吗？",
      "image": "https://picsum.photos/400/300?random=1",
      "imageType": "url"
    }
  ]
}
```

### 3. 运行自动化测试

```bash
# 测试模式运行
python3 tk_chat_automation.py --test-mode

# 正常模式运行
python3 tk_chat_automation.py
```

## 执行流程

1. **问题选择**: 按照配置的模式（sequential/random）选择图文问题
2. **图片处理**: 
   - URL图片：下载到`downloaded_images/`目录
   - 本地图片：验证文件存在性和有效性
3. **图片上传**: 在聊天界面中查找并点击上传按钮，选择图片文件
4. **文本发送**: 上传图片成功后，发送对应的文本消息
5. **等待回复**: 等待AI回复并记录对话内容
6. **截图保存**: 保存对话截图并上传到OSS

## 图片要求

- **支持格式**: JPG, PNG, GIF等常见图片格式
- **文件大小**: 不超过10MB
- **分辨率**: 建议不超过2000x2000像素

## 示例场景

### 时尚穿搭咨询
```json
{
  "text": "这套商务装搭配合适吗？",
  "image": "test_images/business_outfit.jpg",
  "imageType": "local"
}
```

### 产品推荐
```json
{
  "text": "这款包包有什么推荐搭配？",
  "image": "https://example.com/handbag.jpg", 
  "imageType": "url"
}
```

### 颜色咨询
```json
{
  "text": "这个颜色适合我的肤色吗？",
  "image": "test_images/color_sample.jpg",
  "imageType": "local"
}
```

## 日志记录

图文问题的执行会在日志中记录：

```
处理图文问题: 这件衣服好看吗？
图片: https://example.com/sample1.jpg (类型: url)
正在下载图片: https://example.com/sample1.jpg
图片下载成功: downloaded_images/image_a1b2c3d4.jpg
正在上传图片到聊天界面...
图片上传成功，现在发送文本消息
已在输入框中填入: '这件衣服好看吗？'
```

## 故障排除

### 常见问题

1. **图片下载失败**
   - 检查网络连接
   - 确认图片URL有效
   - 检查图片服务器是否允许访问

2. **图片上传失败**
   - 确认聊天界面已正确加载
   - 检查图片文件是否有效
   - 查看浏览器是否支持文件上传

3. **本地图片不存在**
   - 检查文件路径是否正确
   - 确认文件权限允许读取

### 调试模式

运行测试脚本检查功能：

```bash
python3 test_image_text.py
```

此脚本会验证：
- 问题文件格式正确性
- 图片下载功能
- 图片验证逻辑
- 问题选择机制

## 技术实现

- **图片下载**: 使用`requests`库下载网络图片
- **图片验证**: 使用`PIL`库验证图片格式和大小
- **文件上传**: 使用Playwright的`set_input_files`方法
- **界面交互**: 自动查找上传按钮和输入框

## 注意事项

1. 图文问题会按照问题选择模式（sequential/random）与其他问题类型混合执行
2. 图片上传可能需要额外的等待时间，请确保网络环境良好
3. 不同的聊天界面可能有不同的上传控件，代码已包含多种查找策略
4. 建议在测试模式下验证图文功能正常后再进行大规模测试 