@echo off
setlocal enabledelayedexpansion

:: 需要管理员权限
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 请以管理员身份运行此脚本!
    echo 右键点击此脚本，选择"以管理员身份运行"
    echo.
    echo 按任意键退出...
    pause > nul
    exit /b 1
)

:: 设置脚本路径
set SCRIPT_PATH=%~dp0
set SERVICE_NAME=TKChatAutoService
set PYTHON_SCRIPT=tk_chat_automation.py

:: 设置日志路径
set LOG_PATH=%SCRIPT_PATH%logs
if not exist "%LOG_PATH%" mkdir "%LOG_PATH%"
set LOG_FILE=%LOG_PATH%\service_install_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%.log
set LOG_FILE=%LOG_FILE: =0%

:: 标题
title 太古数字人聊天自动化测试 - 服务安装

echo [%date% %time%] 开始安装Windows服务... >> "%LOG_FILE%"
echo 正在准备安装Windows服务...

:: 检查NSSM是否存在
set NSSM_PATH=%SCRIPT_PATH%tools\nssm.exe
if not exist "%NSSM_PATH%" (
    echo [%date% %time%] NSSM工具不存在，正在下载... >> "%LOG_FILE%"
    echo NSSM工具不存在，正在下载...
    
    :: 创建tools目录
    if not exist "%SCRIPT_PATH%tools" mkdir "%SCRIPT_PATH%tools"
    
    :: 使用PowerShell下载NSSM
    powershell -Command "& {Invoke-WebRequest -Uri 'https://nssm.cc/release/nssm-2.24.zip' -OutFile '%SCRIPT_PATH%tools\nssm.zip'}"
    
    :: 解压NSSM
    powershell -Command "& {Expand-Archive -Path '%SCRIPT_PATH%tools\nssm.zip' -DestinationPath '%SCRIPT_PATH%tools\temp' -Force}"
    
    :: 复制正确的NSSM版本 (根据系统架构)
    if exist "%PROGRAMFILES(X86)%" (
        copy "%SCRIPT_PATH%tools\temp\nssm-2.24\win64\nssm.exe" "%SCRIPT_PATH%tools" /Y
    ) else (
        copy "%SCRIPT_PATH%tools\temp\nssm-2.24\win32\nssm.exe" "%SCRIPT_PATH%tools" /Y
    )
    
    :: 清理临时文件
    rmdir /S /Q "%SCRIPT_PATH%tools\temp"
    del "%SCRIPT_PATH%tools\nssm.zip"
)

:: 检查Python是否存在
where python >nul 2>&1
if %errorlevel% neq 0 (
    echo [%date% %time%] 错误: 未找到Python! 请安装Python 3.8或更高版本 >> "%LOG_FILE%"
    echo 错误: 未找到Python! 请安装Python 3.8或更高版本
    echo 按任意键退出...
    pause > nul
    exit /b 1
)

:: 获取Python完整路径
for /f "tokens=*" %%i in ('where python') do set PYTHON_PATH=%%i

echo [%date% %time%] 使用Python路径: %PYTHON_PATH% >> "%LOG_FILE%"
echo 使用Python路径: %PYTHON_PATH%

:: 检查服务是否已存在
"%NSSM_PATH%" status "%SERVICE_NAME%" >nul 2>&1
if %errorlevel% equ 0 (
    echo [%date% %time%] 服务已存在，正在卸载... >> "%LOG_FILE%"
    echo 服务已存在，正在卸载...
    "%NSSM_PATH%" stop "%SERVICE_NAME%" >nul 2>&1
    "%NSSM_PATH%" remove "%SERVICE_NAME%" confirm >nul 2>&1
)

:: 安装服务
echo [%date% %time%] 正在安装服务... >> "%LOG_FILE%"
echo 正在安装服务...

"%NSSM_PATH%" install "%SERVICE_NAME%" "%PYTHON_PATH%"
"%NSSM_PATH%" set "%SERVICE_NAME%" AppParameters "%SCRIPT_PATH%%PYTHON_SCRIPT%"
"%NSSM_PATH%" set "%SERVICE_NAME%" AppDirectory "%SCRIPT_PATH%"
"%NSSM_PATH%" set "%SERVICE_NAME%" DisplayName "太古数字人聊天自动化服务"
"%NSSM_PATH%" set "%SERVICE_NAME%" Description "自动运行太古数字人聊天测试，并在崩溃时自动重启"
"%NSSM_PATH%" set "%SERVICE_NAME%" Start SERVICE_AUTO_START
"%NSSM_PATH%" set "%SERVICE_NAME%" AppStdout "%LOG_PATH%\service_output.log"
"%NSSM_PATH%" set "%SERVICE_NAME%" AppStderr "%LOG_PATH%\service_error.log"
"%NSSM_PATH%" set "%SERVICE_NAME%" AppRotateFiles 1
"%NSSM_PATH%" set "%SERVICE_NAME%" AppRotateOnline 1
"%NSSM_PATH%" set "%SERVICE_NAME%" AppRotateSeconds 86400
"%NSSM_PATH%" set "%SERVICE_NAME%" AppRotateBytes 10485760

:: 启动服务
echo [%date% %time%] 正在启动服务... >> "%LOG_FILE%"
echo 正在启动服务...
"%NSSM_PATH%" start "%SERVICE_NAME%"

:: 检查服务是否启动成功
timeout /t 3 /nobreak > nul
"%NSSM_PATH%" status "%SERVICE_NAME%" >nul 2>&1
if %errorlevel% equ 0 (
    echo [%date% %time%] 服务安装并启动成功! >> "%LOG_FILE%"
    echo 服务安装并启动成功!
    echo.
    echo 服务名称: %SERVICE_NAME%
    echo 服务描述: 太古数字人聊天自动化服务
    echo 服务状态: 已启动
    echo.
    echo 您可以在Windows服务管理器中管理此服务:
    echo 1. 运行services.msc
    echo 2. 找到"太古数字人聊天自动化服务"
    echo.
) else (
    echo [%date% %time%] 错误: 服务安装成功但启动失败! >> "%LOG_FILE%"
    echo 错误: 服务安装成功但启动失败!
    echo 请检查日志文件了解更多信息。
)

echo 按任意键退出...
pause > nul 