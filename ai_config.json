{"ai_generation_config": {"default_provider": "ollama", "providers": {"ollama": {"provider": "ollama", "model": "deepseek-r1:14b", "base_url": "http://localhost:11434", "temperature": 0.7, "max_tokens": 1000, "timeout": 120}, "openai": {"provider": "openai", "model": "gpt-3.5-turbo", "api_key": "${OPENAI_API_KEY}", "base_url": "https://api.openai.com/v1", "temperature": 0.7, "max_tokens": 1000, "timeout": 120}, "custom": {"provider": "custom", "url": "http://your-custom-api.com/generate", "headers": {"Content-Type": "application/json", "Authorization": "Bearer your-token"}, "payload_template": {"prompt": "", "max_tokens": 1000, "temperature": 0.7}, "timeout": 120}}}, "ai_evaluation_config": {"enabled": false, "default_provider": "ollama_eval", "providers": {"ollama_eval": {"provider": "ollama", "model": "deepseek-r1:14b", "base_url": "http://localhost:11434", "temperature": 0.3, "max_tokens": 1500, "timeout": 180}, "openai_eval": {"provider": "openai", "model": "gpt-4", "api_key": "${OPENAI_API_KEY}", "base_url": "https://api.openai.com/v1", "temperature": 0.3, "max_tokens": 1500, "timeout": 60}, "custom_eval": {"provider": "custom", "url": "http://your-custom-api.com/evaluate", "headers": {"Content-Type": "application/json", "Authorization": "Bearer your-token"}, "payload_template": {"prompt": "", "max_tokens": 1500, "temperature": 0.3}, "timeout": 60}}}, "evaluation_settings": {"score_scale": 5, "evaluation_dimensions": ["理解力", "记忆力", "专业性", "情商话术", "知识储备", "合规性"], "domain": "时尚", "retry_attempts": 2}, "generation_settings": {"default_count": 3, "max_count": 10, "fallback_to_example": true, "retry_attempts": 2}}