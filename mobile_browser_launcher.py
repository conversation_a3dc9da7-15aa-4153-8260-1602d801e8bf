#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手机浏览器模式启动器
使用手机浏览器模式启动多个URL，参考tk_chat_automation.py的启动方式
"""

import os
import sys
import time
import logging
import argparse
import subprocess
import threading
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor
from playwright.sync_api import sync_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page

def setup_logging():
    """设置日志"""
    log_dir = "logs"
    os.makedirs(log_dir, exist_ok=True)
    
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"mobile_launcher_{timestamp}.log")
    
    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    return log_file

def calculate_window_position(process_id, total_count):
    """计算窗口位置，避免重叠"""
    screen_width = 2800
    screen_height = 1800
    window_width = 400
    window_height = 600
    margin = 30
    gap = 80
    
    # 计算每行可以放置的窗口数量
    windows_per_row = (screen_width - margin * 2) // (window_width + gap)
    
    if windows_per_row <= 0:
        windows_per_row = 1
    
    row = process_id // windows_per_row
    col = process_id % windows_per_row
    
    x = margin + col * (window_width + gap)
    y = margin + row * (window_height + gap)
    
    # 确保不超出屏幕边界
    if x + window_width > screen_width:
        x = screen_width - window_width - margin
    if y + window_height > screen_height:
        y = screen_height - window_height - margin
    
    return x, y

def launch_mobile_browser(url, process_id, total_count):
    """启动手机浏览器模式访问指定URL"""
    log_file = setup_logging()
    
    try:
        # 计算窗口位置
        window_x, window_y = calculate_window_position(process_id, total_count)
        
        logging.info(f"进程 {process_id}: 启动手机浏览器模式，URL: {url}")
        logging.info(f"进程 {process_id}: 窗口位置 X={window_x}, Y={window_y}")
        
        with sync_playwright() as p:
            # 启动浏览器，使用更稳定的参数
            browser = p.chromium.launch(
                headless=False,
                args=[
                    f'--window-position={window_x},{window_y}',
                    '--window-size=400,600',
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-gpu',
                    '--disable-software-rasterizer',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding',
                    '--disable-features=TranslateUI',
                    '--disable-ipc-flooding-protection',
                    '--disable-hang-monitor',
                    '--disable-prompt-on-repost',
                    '--disable-popup-blocking',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--force-color-profile=srgb',
                    '--metrics-recording-only',
                    '--no-first-run',
                    '--password-store=basic',
                    '--use-mock-keychain',
                    '--no-service-autorun',
                    '--disable-search-engine-choice-screen',
                    '--enable-use-zoom-for-dsf=false'
                ]
            )
            
            # 创建手机上下文
            context = browser.new_context(
                viewport={'width': 375, 'height': 667},  # iPhone 6/7/8 尺寸
                user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
                device_scale_factor=2,
                is_mobile=True,
                has_touch=True,
                # 添加更多稳定性配置
                ignore_https_errors=True,
                bypass_csp=True
            )
            
            # 创建页面
            page = context.new_page()
            
            # 设置页面超时
            page.set_default_timeout(60000)  # 60秒超时
            
            # 导航到URL
            logging.info(f"进程 {process_id}: 正在导航到 {url}")
            page.goto(url, wait_until='domcontentloaded')  # 改为domcontentloaded，更稳定
            
            logging.info(f"进程 {process_id}: 成功加载页面，开始等待...")
            
            # 等待用户手动操作或脚本终止
            try:
                while True:
                    time.sleep(1)
                    # 检查页面是否还活着
                    if page.is_closed():
                        logging.info(f"进程 {process_id}: 页面已关闭")
                        break
            except KeyboardInterrupt:
                logging.info(f"进程 {process_id}: 收到中断信号，正在关闭...")
            except Exception as e:
                logging.error(f"进程 {process_id}: 等待过程中出错 - {str(e)}")
            finally:
                # 关闭浏览器
                try:
                    context.close()
                    browser.close()
                    logging.info(f"进程 {process_id}: 浏览器已关闭")
                except Exception as e:
                    logging.error(f"进程 {process_id}: 关闭浏览器时出错 - {str(e)}")
                    
    except Exception as e:
        logging.error(f"进程 {process_id}: 启动失败 - {str(e)}")
        return False
    
    return True

def load_urls_from_file(file_path):
    """从文件加载URL列表"""
    urls = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    urls.append(line)
        logging.info(f"从文件 {file_path} 加载了 {len(urls)} 个URL")
    except Exception as e:
        logging.error(f"加载URL文件失败: {str(e)}")
        return []
    
    return urls

def run_single_browser_process(url, process_id, total_count):
    """运行单个浏览器进程"""
    try:
        success = launch_mobile_browser(url, process_id, total_count)
        if success:
            logging.info(f"进程 {process_id}: 完成")
        else:
            logging.error(f"进程 {process_id}: 失败")
        return success
    except Exception as e:
        logging.error(f"进程 {process_id}: 异常 - {str(e)}")
        return False

def main():
    parser = argparse.ArgumentParser(description='手机浏览器模式启动器')
    parser.add_argument('--urls-file', default='test_urls.txt', help='URL文件路径')
    parser.add_argument('--concurrent', type=int, default=5, help='并发数量')
    parser.add_argument('--startup-interval', type=int, default=2, help='启动间隔（秒）')
    parser.add_argument('--test-window-positions', action='store_true', help='测试窗口位置计算')
    
    args = parser.parse_args()
    
    # 设置日志
    log_file = setup_logging()
    logging.info("=== 手机浏览器模式启动器开始 ===")
    logging.info(f"URL文件: {args.urls_file}")
    logging.info(f"并发数量: {args.concurrent}")
    logging.info(f"启动间隔: {args.startup_interval}秒")
    
    # 测试窗口位置
    if args.test_window_positions:
        logging.info("=== 测试窗口位置计算 ===")
        for i in range(args.concurrent):
            x, y = calculate_window_position(i, args.concurrent)
            logging.info(f"进程 {i}: X={x}, Y={y}")
        return
    
    # 加载URL
    urls = load_urls_from_file(args.urls_file)
    if not urls:
        logging.error("没有找到有效的URL，退出")
        return
    
    logging.info(f"总共加载了 {len(urls)} 个URL")
    
    # 使用线程池启动浏览器
    with ThreadPoolExecutor(max_workers=args.concurrent) as executor:
        futures = []
        
        for i, url in enumerate(urls):
            # 提交任务
            future = executor.submit(run_single_browser_process, url, i, len(urls))
            futures.append(future)
            
            # 启动间隔
            if i < len(urls) - 1:  # 不是最后一个
                time.sleep(args.startup_interval)
        
        # 等待所有任务完成
        logging.info("等待所有浏览器进程完成...")
        for i, future in enumerate(futures):
            try:
                result = future.result()
                if result:
                    logging.info(f"进程 {i}: 成功完成")
                else:
                    logging.error(f"进程 {i}: 失败")
            except Exception as e:
                logging.error(f"进程 {i}: 异常 - {str(e)}")
    
    logging.info("=== 手机浏览器模式启动器结束 ===")

if __name__ == "__main__":
    main() 