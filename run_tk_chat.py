#!/usr/bin/env python3
"""
TK Chat Automation 启动脚本
解决conda环境和依赖问题
"""

import os
import sys
import subprocess
from pathlib import Path

def check_conda_env():
    """检查conda环境是否存在"""
    conda_env_path = Path.home() / "miniforge3" / "envs" / "tk_chat"
    if not conda_env_path.exists():
        print("❌ 错误: tk_chat conda环境不存在")
        print("请先创建环境: conda create -n tk_chat python=3.9")
        return False
    return True

def get_conda_python():
    """获取conda环境中的Python路径"""
    python_path = Path.home() / "miniforge3" / "envs" / "tk_chat" / "bin" / "python3"
    if python_path.exists():
        return str(python_path)
    return None

def check_playwright():
    """检查playwright是否已安装"""
    python_path = get_conda_python()
    if not python_path:
        return False
    
    try:
        result = subprocess.run([python_path, "-c", "import playwright"], 
                              capture_output=True, text=True)
        return result.returncode == 0
    except Exception:
        return False

def install_playwright():
    """安装playwright"""
    print("正在安装playwright...")
    try:
        # 使用conda安装
        conda_sh = Path.home() / "miniforge3" / "etc" / "profile.d" / "conda.sh"
        cmd = f"source {conda_sh} && conda activate tk_chat && conda install -c conda-forge playwright -y && playwright install"
        
        result = subprocess.run(cmd, shell=True, executable="/bin/bash")
        return result.returncode == 0
    except Exception as e:
        print(f"安装失败: {e}")
        return False

def run_tk_chat():
    """运行tk_chat_automation.py"""
    python_path = get_conda_python()
    script_path = "tk_chat_automation.py"
    
    if not os.path.exists(script_path):
        print("❌ 错误: tk_chat_automation.py文件不存在")
        print("请确保在正确的目录中运行此脚本")
        return False
    
    print("🚀 启动TK Chat Automation...")
    print("")
    
    try:
        result = subprocess.run([python_path, script_path])
        return result.returncode == 0
    except Exception as e:
        print(f"运行失败: {e}")
        return False

def main():
    print("=== TK Chat Automation 启动脚本 ===")
    
    # 检查conda环境
    if not check_conda_env():
        sys.exit(1)
    
    # 检查playwright
    if not check_playwright():
        print("❌ playwright未安装在tk_chat环境中")
        if not install_playwright():
            print("❌ playwright安装失败")
            sys.exit(1)
        print("✅ playwright安装成功")
    
    print("✅ 环境检查完成")
    
    # 运行脚本
    if run_tk_chat():
        print("")
        print("📋 脚本执行完成")
        print("📁 检查以下目录获取结果:")
        print("   - 截图: ./screenshots")
        print("   - 对话记录: ./results")  
        print("   - 日志: ./logs")
    else:
        print("❌ 脚本执行失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
