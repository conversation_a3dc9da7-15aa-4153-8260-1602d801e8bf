#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证浏览器重启逻辑和AI NPC按钮模式检测功能
测试两个关键修复：
1. 浏览器crash检测和自动重启
2. 基于AI NPC-黑按钮的精确模式检测
"""

import json
import subprocess
import sys
import os

def create_comprehensive_test_questions():
    """创建全面的测试问题集"""
    questions = {
        "questions": [
            {"text": "你好，这里是太古汇吗？"},
            {"text": "请问现在几点了？"},
            {"text": "有什么特色餐厅推荐？"},
            {"text": "停车方便吗？"},
            {"text": "购物有什么优惠活动？"}
        ]
    }
    
    filename = "浏览器重启和模式检测_测试问题.json"
    with open(filename, "w", encoding="utf-8") as f:
        json.dump(questions, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 已创建测试问题文件: {filename}")
    return filename

def print_comprehensive_fixes():
    """打印所有修复内容的详细说明"""
    print("🛠️  浏览器重启逻辑和模式检测修复总结")
    print("=" * 70)
    
    print("\n📋 修复1: 浏览器crash检测和自动重启")
    print("   ✅ 创建 is_browser_alive() 函数")
    print("      - 检查浏览器、上下文、页面对象状态")
    print("      - 尝试获取页面URL验证连接")
    print("      - 捕获并识别浏览器crash异常")
    print()
    print("   ✅ 创建 restart_browser_if_needed() 函数")
    print("      - 强制清理旧的浏览器进程")
    print("      - 启动新的Playwright浏览器实例")
    print("      - 返回新的browser、context、page对象")
    print()
    print("   ✅ 修改 restart_chat_session() 函数")
    print("      - 在重启前检查浏览器状态")
    print("      - 返回浏览器实例以支持重启")
    print("      - 区分会话重启失败和浏览器crash")
    print()
    print("   ✅ 更新主流程中的重启逻辑")
    print("      - 处理浏览器重启的返回值")
    print("      - 自动更新浏览器实例引用")
    print("      - 重启后重新执行聊天流程")
    
    print("\n📋 修复2: 基于AI NPC按钮的精确模式检测")
    print("   ✅ 重写 detect_current_mode() 函数")
    print("      - 基于AI NPC-黑按钮的唯一准确特征")
    print("      - 简化检测逻辑，提高准确性")
    print("      - 添加备用检测机制")
    
    print("\n🎯 文本模式唯一特征（AI NPC-黑按钮）:")
    text_features = [
        'img[alt="AI NPC-黑"][src*="ai_npc_model_black.8466226c.png"]',
        'img[alt="AI NPC-黑"]',
        'img[src*="ai_npc_model_black"]'
    ]
    for i, feature in enumerate(text_features, 1):
        print(f"   {i}. {feature}")
    
    print("\n📊 检测逻辑:")
    print("   🔍 核心原则: 只有显示AI NPC-黑按钮时才是文本模式")
    print("   🔍 如果存在AI NPC-黑按钮 → 文本模式")
    print("   🔍 如果不存在AI NPC-黑按钮 → 数字人模式")
    print("   🔍 备用方案: textarea元素检测（异常情况）")
    
    print("\n🚀 浏览器重启流程:")
    restart_flow = [
        "检测浏览器状态异常",
        "清理旧的浏览器进程",
        "启动新的浏览器实例",
        "重新导航到目标页面",
        "恢复聊天会话",
        "继续执行测试流程"
    ]
    for i, step in enumerate(restart_flow, 1):
        print(f"   {i}. {step}")

def main():
    """主函数"""
    print("🧪 浏览器重启逻辑和AI NPC按钮模式检测综合验证")
    print("=" * 60)
    
    # 打印修复总结
    print_comprehensive_fixes()
    
    print("\n" + "=" * 70)
    print("\n🚀 测试配置")
    
    # 创建测试文件
    questions_file = create_comprehensive_test_questions()
    
    print("\n📝 推荐测试场景:")
    
    print("\n1. 基础功能测试:")
    print("   - 验证模式检测准确性")
    print("   - 验证强制文本模式切换")
    print("   - 验证重启后模式保持")
    cmd1 = f"python3 tk_chat_automation.py --test-mode --force-text-mode --questions-file {questions_file}"
    print(f"   命令: {cmd1}")
    
    print("\n2. 浏览器重启压力测试:")
    print("   - 多进程并发测试")
    print("   - 模拟浏览器异常情况")
    print("   - 验证自动恢复机制")
    cmd2 = f"python3 stress_test.py --concurrent 3 --test-mode --force-text-mode --questions-file {questions_file}"
    print(f"   命令: {cmd2}")
    
    print("\n3. 长时间稳定性测试:")
    print("   - 验证长期运行稳定性")
    print("   - 测试浏览器内存泄漏处理")
    print("   - 验证重启逻辑的可靠性")
    cmd3 = f"python3 tk_chat_automation.py --force-text-mode --max-questions 15 --questions-file {questions_file}"
    print(f"   命令: {cmd3}")
    
    print("\n🎯 验证重点:")
    verification_points = [
        "浏览器crash后能够自动检测",
        "新浏览器实例启动成功",
        "聊天会话能够正确恢复",
        "模式检测基于AI NPC-黑按钮的唯一特征",
        "文本模式通过AI NPC-黑按钮准确识别",
        "强制文本模式在重启后仍然生效",
        "发送按钮不会误点语音按钮",
        "整个流程无明显卡顿或异常"
    ]
    
    for i, point in enumerate(verification_points, 1):
        print(f"   {i}. {point}")
    
    print("\n📊 关键日志监控:")
    key_logs = [
        "'⚠️ 检测到浏览器已crash，需要重启浏览器'",
        "'🔧 需要重启浏览器，正在重新启动...'",
        "'✅ 浏览器重启成功'",
        "'✅ 浏览器重启后聊天会话恢复成功'",
        "'✅ 检测到当前为文本模式（存在AI NPC-黑按钮）'",
        "'✅ 检测到当前为数字人模式（未找到AI NPC-黑按钮）'",
        "'🎯 重启后执行强制文本模式切换...'",
        "'跳过语音按钮: 语音'"
    ]
    
    for i, log in enumerate(key_logs, 1):
        print(f"   {i}. {log}")
    
    print("\n" + "=" * 70)
    
    # 询问用户测试选择
    print("\n🧪 选择测试类型:")
    print("1. 基础功能测试 - 验证模式检测和强制文本模式")
    print("2. 浏览器重启压力测试 - 多进程并发测试")
    print("3. 长时间稳定性测试 - 验证长期运行稳定性")
    print("4. 自定义测试 - 输入自定义参数")
    print("q. 退出")
    
    user_choice = input("\n请选择测试类型 (1-4/q): ").strip()
    
    if user_choice == 'q':
        print("⏸️ 退出测试")
        return
    
    # 构建测试命令
    if user_choice == '1':
        cmd = [
            sys.executable, 
            'tk_chat_automation.py',
            '--test-mode',
            '--force-text-mode',
            '--questions-file', questions_file
        ]
        test_type = "基础功能测试"
    elif user_choice == '2':
        cmd = [
            sys.executable, 
            'stress_test.py',
            '--concurrent', '3',
            '--test-mode',
            '--force-text-mode',
            '--questions-file', questions_file
        ]
        test_type = "浏览器重启压力测试"
    elif user_choice == '3':
        cmd = [
            sys.executable, 
            'tk_chat_automation.py',
            '--force-text-mode',
            '--max-questions', '15',
            '--questions-file', questions_file
        ]
        test_type = "长时间稳定性测试"
    elif user_choice == '4':
        print("\n📝 自定义测试参数:")
        script = input("脚本名称 (tk_chat_automation.py/stress_test.py): ").strip() or "tk_chat_automation.py"
        custom_args = input("额外参数 (如: --max-questions 10): ").strip()
        
        cmd = [sys.executable, script, '--force-text-mode', '--questions-file', questions_file]
        if custom_args:
            cmd.extend(custom_args.split())
        test_type = "自定义测试"
    else:
        print("❌ 无效选择")
        return
    
    print(f"\n⏰ 开始执行{test_type}...")
    print(f"🔧 命令: {' '.join(cmd)}")
    print("=" * 60)
    print("📋 重要提示:")
    print("   - 观察日志中的浏览器重启相关信息")
    print("   - 注意模式检测的准确性")
    print("   - 留意强制文本模式的工作情况")
    print("   - 关注异常恢复的效果")
    print("=" * 60)
    
    try:
        # 运行测试
        result = subprocess.run(cmd, capture_output=False, text=True)
        
        if result.returncode == 0:
            print(f"\n✅ {test_type}完成！")
            print("🎯 请检查日志确认以下功能是否正常:")
            print("   1. 模式检测准确性")
            print("   2. 浏览器重启机制")
            print("   3. 强制文本模式保持")
            print("   4. 发送按钮正确识别")
        else:
            print(f"\n⚠️ {test_type}退出码: {result.returncode}")
            
    except KeyboardInterrupt:
        print(f"\n⏹️ {test_type}被用户中断")
    except Exception as e:
        print(f"\n❌ {test_type}执行出错: {e}")
    finally:
        # 清理测试文件
        try:
            if os.path.exists(questions_file):
                os.remove(questions_file)
                print(f"🧹 已清理测试文件: {questions_file}")
        except:
            pass

if __name__ == "__main__":
    main() 