#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os
import re

def convert_to_json_format(input_file, output_file):
    """将文本格式的问题转换为JSON格式，适合多轮次追问对话"""
    
    print("开始处理文件: %s" % input_file)
    
    # 读取输入文件
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 分割成段落
    paragraphs = content.strip().split('\n\n')
    
    # 存储最终的多问题组
    multi_questions = []
    
    # 处理每个段落
    for paragraph in paragraphs:
        lines = paragraph.strip().split('\n')
        
        # 如果段落为空，跳过
        if not lines:
            continue
        
        # 提取问题组
        question_group = []
        
        # 第一行是主问题
        main_question = lines[0].strip()
        if main_question:
            question_group.append(main_question)
        
        # 处理后续问题，不再添加前缀
        for line in lines[1:]:
            # 移除"问题："前缀
            follow_up_question = line.strip()
            if follow_up_question.startswith("问题："):
                follow_up_question = follow_up_question[3:].strip()
            
            if follow_up_question:
                question_group.append(follow_up_question)
        
        # 如果问题组不为空，添加到结果中
        if question_group:
            multi_questions.append(question_group)
    
    # 将结果转换为JSON格式并写入输出文件
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(multi_questions, f, ensure_ascii=False, indent=2)
    
    print("处理完成，转换了 %s 组问题" % len(multi_questions))
    print("结果已保存到: %s" % output_file)
    
    # 返回示例数据，用于验证
    return multi_questions[:3] if multi_questions else []

def convert_to_text_format(input_file, output_file):
    """将JSON格式转换回文本格式（用于需要反向转换的情况）"""
    
    print("开始读取JSON文件: %s" % input_file)
    
    # 读取JSON文件
    with open(input_file, 'r', encoding='utf-8') as f:
        multi_questions = json.load(f)
    
    # 转换为文本格式
    text_content = []
    
    for question_group in multi_questions:
        if not question_group:
            continue
            
        # 第一行是主问题
        group_text = [question_group[0]]
        
        # 后续问题添加"问题："前缀，以兼容原格式
        for question in question_group[1:]:
            group_text.append("问题：%s" % question)
        
        # 添加到整体内容中
        text_content.append("\n".join(group_text))
    
    # 写入到输出文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("\n\n".join(text_content))
    
    print("处理完成，转换了 %s 组问题" % len(multi_questions))
    print("结果已保存到: %s" % output_file)

def create_sample_json_file(output_file):
    """创建示例JSON文件，演示多轮对话的正确格式"""
    
    # 示例多轮对话数据
    sample_data = [
        # 示例1: 咖啡店推荐对话
        [
            "推荐几家咖啡店",
            "哪家最好喝",
            "有什么特色饮品"
        ],
        # 示例2: 自我介绍对话
        [
            "你好",
            "介绍一下自己",
            "你能做什么"
        ],
        # 示例3: 时尚咨询对话
        [
            "今年流行什么颜色",
            "这个颜色适合什么场合",
            "有没有搭配建议"
        ]
    ]
    
    # 写入JSON文件
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(sample_data, f, ensure_ascii=False, indent=2)
    
    print("已创建示例JSON文件: %s" % output_file)
    print("示例数据格式如下:")
    for i, group in enumerate(sample_data):
        print("对话 %s: %s" % (i+1, group))
    
    return sample_data

def main():
    # 定义输入和输出文件
    base_dir = os.path.dirname(os.path.abspath(__file__))
    input_file = os.path.join(base_dir, 'multi_questions.txt')
    output_json_file = os.path.join(base_dir, 'multi_questions.json')
    
    # 直接执行文本转JSON操作
    print("执行操作: 文本转JSON (multi_questions.txt -> multi_questions.json)")
    
    # 执行转换
    sample_data = convert_to_json_format(input_file, output_json_file)
    
    # 显示示例数据
    print("\n转换后的JSON格式示例 (前3组):")
    for i, group in enumerate(sample_data):
        print("对话 %s: %s" % (i+1, group))
        
    # 提示更新脚本中的文件路径
    print("\n转换完成。如需在自动化脚本中使用，请确保修改tk_chat_automation.py中的文件路径。")

if __name__ == "__main__":
    main() 