#!/bin/bash

# 确保在脚本所在目录运行
cd "$(dirname "$0")"

# 检查是否指定了测试模式
TEST_MODE=""
if [ "$1" == "--test-mode" ]; then
    TEST_MODE="--test-mode"
    echo "将以测试模式启动"
fi

# 获取当前时间作为日志文件名
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="logs/keepalive_${TIMESTAMP}.log"

# 确保日志目录存在
mkdir -p logs

# 启动保活脚本并在后台运行
echo "启动保活脚本，日志将保存到: $LOG_FILE"
nohup python3 keepalive.py $TEST_MODE > "$LOG_FILE" 2>&1 &

# 保存进程ID
PID=$!
echo "保活脚本已启动，进程ID: $PID"
echo $PID > keepalive.pid

echo "可以使用以下命令查看日志:"
echo "tail -f $LOG_FILE" 