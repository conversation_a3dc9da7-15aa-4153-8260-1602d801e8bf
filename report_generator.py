"""
对话报告生成模块
负责生成HTML和Excel格式的对话报告
"""

import os
import html as html_module
import logging


def extract_image_from_question(question_text):
    """从问题文本中提取图片信息"""
    if '[图片]' in question_text:
        text_part = question_text.replace('[图片]', '').strip()
        return True, text_part
    return False, question_text


def extract_ai_generated_info(question_text):
    """从问题文本中提取AI生成信息"""
    lines = question_text.split('\n')
    ai_info = {}
    pure_question = question_text
    
    for line in lines:
        if 'AI生成问题' in line:
            if '领域:' in line and '意图:' in line:
                parts = line.split(',')
                for part in parts:
                    if '领域:' in part:
                        ai_info['domain'] = part.split('领域:')[1].strip()
                    elif '意图:' in part:
                        ai_info['intent_type'] = part.split('意图:')[1].strip()
            ai_info['is_ai_generated'] = True
        elif 'AI追问对话' in line:
            ai_info['is_ai_followup'] = True
            if '领域:' in line:
                domain_match = line.split('领域:')[1].split(',')[0].strip() if '领域:' in line else ''
                if domain_match:
                    ai_info['domain'] = domain_match
        elif line.startswith('问:'):
            pure_question = line[2:].strip()
    
    return ai_info, pure_question


def generate_conversation_report_html(all_conversations, output_path):
    """生成对话HTML报告"""
    
    html = []
    html.append('<!DOCTYPE html>')
    html.append('<html lang="zh-CN">')
    html.append('<head>')
    html.append('<meta charset="UTF-8">')
    html.append('<title>对话报告</title>')
    html.append('<style>')
    html.append('body{font-family:Arial,sans-serif;background:#f7f7f7;margin:0;padding:0;}')
    html.append('.container{max-width:1200px;margin:30px auto;background:#fff;padding:30px 40px;border-radius:8px;box-shadow:0 2px 8px rgba(0,0,0,0.08);}')
    html.append('h1{text-align:center;color:#222;margin-bottom:10px;}')
    html.append('.summary{text-align:center;color:#666;margin-bottom:30px;padding:15px;background:#f8f9fa;border-radius:5px;}')
    html.append('table{width:100%;border-collapse:collapse;margin-top:30px;}')
    html.append('th,td{border:1px solid #e0e0e0;padding:12px 8px;text-align:left;vertical-align:top;}')
    html.append('th{background:#f0f4fa;color:#333;font-weight:bold;}')
    html.append('.q{color:#2c3e50;font-weight:bold;}')
    html.append('.a{color:#16a085;}')
    html.append('</style>')
    html.append('</head>')
    html.append('<body>')
    html.append('<div class="container">')
    html.append('<h1>聊天机器人对话报告</h1>')
    
    # 添加统计摘要
    total_conversations = len(all_conversations)
    total_questions = sum(len(conv['qas']) for conv in all_conversations)
    
    html.append(f'<div class="summary">')
    html.append(f'总计 {total_conversations} 轮对话，{total_questions} 个问题')
    html.append('</div>')
    
    for conv in all_conversations:
        html.append(f'<div class="cid">对话 {conv["conversation_id"]}</div>')
        html.append('<table>')
        html.append('<tr><th>问题</th><th>答复</th><th>截图</th><th>回复时长</th></tr>')
        
        for qa in conv['qas']:
            question_text = qa['question']
            ai_info, pure_question = extract_ai_generated_info(question_text)
            
            question_html = f'<div class="q">{html_module.escape(pure_question)}</div>'
            answer_text = html_module.escape(qa['answer'])
            
            img = qa.get('screenshot', '')
            wait = html_module.escape(qa.get('reply_wait',''))
            img_html = f'<img src="{img}" style="max-width:180px;" alt="截图">' if img else '无截图'
            
            html.append(f'<tr><td>{question_html}</td><td class="a">{answer_text}</td><td>{img_html}</td><td>{wait}</td></tr>')
        
        html.append('</table>')
    
    html.append('</div></body></html>')
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(html))


def generate_conversation_report_excel(all_conversations, output_path):
    """生成对话Excel报告"""
    try:
        import xlsxwriter
        
        workbook = xlsxwriter.Workbook(output_path)
        worksheet = workbook.add_worksheet('对话报告')
        
        # 设置表头
        headers = ['对话编号', '问题类型', '问题内容', '答复', '截图', '回复等待时长']
        
        # 设置格式
        header_format = workbook.add_format({
            'bold': True,
            'align': 'center',
            'border': 1,
            'bg_color': '#f0f4fa'
        })
        
        normal_format = workbook.add_format({
            'align': 'left',
            'valign': 'top',
            'border': 1,
            'text_wrap': True
        })
        
        # 写入表头
        for col, header in enumerate(headers):
            worksheet.write(0, col, header, header_format)
        
        # 写入数据
        row = 1
        for conv in all_conversations:
            for qa in conv['qas']:
                question_text = qa['question']
                ai_info, pure_question = extract_ai_generated_info(question_text)
                
                worksheet.write(row, 0, conv['conversation_id'], normal_format)
                worksheet.write(row, 1, '文本问题', normal_format)
                worksheet.write(row, 2, pure_question, normal_format)
                worksheet.write(row, 3, qa['answer'], normal_format)
                worksheet.write(row, 4, qa.get('screenshot', ''), normal_format)
                worksheet.write(row, 5, qa.get('reply_wait',''), normal_format)
                
                row += 1
        
        workbook.close()
        
    except ImportError:
        logging.warning("未安装xlsxwriter库，无法生成Excel报告")
    except Exception as e:
        logging.error(f"生成Excel报告失败: {str(e)}")


def convert_txt_to_html(txt_path):
    """将文本文件转换为HTML格式"""
    html_path = txt_path.replace('.txt', '.html')
    
    try:
        with open(txt_path, 'r', encoding='utf-8') as txt_file:
            content = txt_file.read()
        
        html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>对话日志</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }}
        .log-content {{ white-space: pre-wrap; font-family: monospace; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>TikaBot 对话日志</h1>
        <div class="log-content">{html_module.escape(content)}</div>
    </div>
</body>
</html>"""
        
        with open(html_path, 'w', encoding='utf-8') as html_file:
            html_file.write(html_content)
        
        logging.info(f"已转换文本文件为HTML: {html_path}")
        return html_path
        
    except Exception as e:
        logging.error(f"转换文本文件为HTML失败: {str(e)}")
        return None 