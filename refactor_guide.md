# TK Chat Automation 重构指南

## 重构概述

原来的 `tk_chat_automation.py` 文件过于臃肿（5000多行代码），包含了多个不同功能领域的代码。为了提高代码的可维护性和可读性，我们将其拆分为以下几个独立的模块：

## 模块划分

### 1. `ai_service.py` - AI服务模块
**功能**: AI问题生成、追问对话、回答评测
**主要类/函数**:
- `load_ai_config()` - 加载AI配置
- `generate_ai_questions()` - 生成AI问题
- `generate_followup_question()` - 生成追问问题
- `evaluate_ai_response()` - 评测AI回答
- 支持多种API提供商（Ollama、OpenAI、自定义）

### 2. `report_generator.py` - 报告生成模块
**功能**: HTML和Excel报告生成
**主要类/函数**:
- `generate_conversation_report_html()` - 生成HTML报告
- `generate_conversation_report_excel()` - 生成Excel报告
- `extract_ai_generated_info()` - 提取AI生成信息
- `convert_txt_to_html()` - 文本转HTML

### 3. `image_handler.py` - 图片处理模块
**功能**: 图片上传、下载、验证、处理
**主要类/函数**:
- `download_image_from_url()` - 从URL下载图片
- `validate_image_file()` - 验证图片文件
- `upload_image_to_chat()` - 上传图片到聊天界面
- `resize_image()` - 调整图片大小
- `compress_image()` - 压缩图片

### 4. `storage_service.py` - 存储服务模块
**功能**: OSS文件上传、目录管理
**主要类/函数**:
- `upload_image_to_oss()` - 上传图片到OSS
- `upload_text_to_oss()` - 上传文本文件到OSS
- `download_from_oss()` - 从OSS下载文件
- `cleanup_old_oss_objects()` - 清理旧OSS文件

### 5. `notification_service.py` - 通知服务模块
**功能**: 钉钉、邮件、Slack等通知
**主要类/函数**:
- `send_dingtalk_notification()` - 发送钉钉通知
- `send_email_notification()` - 发送邮件通知
- `NotificationManager` - 通知管理器类

### 6. `question_manager.py` - 问题管理模块
**功能**: 问题加载、调度、过滤
**主要类/函数**:
- `load_questions()` - 加载问题文件
- `get_next_questions()` - 获取下一批问题
- `QuestionManager` - 问题管理器类
- 支持JSON、CSV、TXT格式

### 7. `logging_utils.py` - 日志工具模块
**功能**: 日志设置、网络请求记录、调试信息收集
**主要类/函数**:
- `setup_logging()` - 设置日志配置
- `log_network_request()` - 记录网络请求
- `collect_debug_information()` - 收集调试信息
- `PerformanceLogger` - 性能日志记录器

### 8. `stats_manager.py` - 统计管理模块
**功能**: 对话统计、性能统计、数据分析
**主要类/函数**:
- `DialogueStats` - 对话统计类
- `PerformanceMonitor` - 性能监控器
- `DataAnalyzer` - 数据分析器

## 如何迁移

### 1. 更新导入语句

原来的代码：
```python
# 所有功能都在同一个文件中
def some_function():
    generate_ai_questions(...)
    send_dingtalk_notification(...)
```

重构后的代码：
```python
# 按功能模块导入
from ai_service import generate_ai_questions, evaluate_ai_response
from notification_service import send_dingtalk_notification
from report_generator import generate_conversation_report_html
from question_manager import QuestionManager
from logging_utils import setup_logging
from stats_manager import DialogueStats

def some_function():
    generate_ai_questions(...)
    send_dingtalk_notification(...)
```

### 2. 主程序结构示例

```python
# main.py 或 tk_chat_automation_refactored.py
import logging
from logging_utils import setup_logging
from question_manager import QuestionManager
from ai_service import generate_ai_questions, evaluate_ai_response
from report_generator import generate_conversation_report_html
from notification_service import NotificationManager
from stats_manager import DialogueStats
from image_handler import download_image_from_url
from storage_service import upload_image_to_oss

def main():
    # 初始化日志
    setup_logging(log_level=logging.INFO, log_file="logs/automation.log")
    
    # 初始化组件
    question_mgr = QuestionManager("questions.json")
    stats = DialogueStats()
    notifier = NotificationManager(config)
    
    # 主要逻辑
    conversations = []
    
    for i in range(conversation_count):
        # 获取问题
        questions = question_mgr.get_multiple_questions(5)
        
        # 执行对话逻辑...
        qas = []
        for question in questions:
            # 执行问题并获取答案
            answer = execute_question(question)
            qas.append({"question": question, "answer": answer})
        
        # 添加统计
        conversation_id = f"conversation_{i+1}"
        conversations.append({
            "conversation_id": conversation_id,
            "qas": qas
        })
        stats.add_conversation(conversation_id, qas)
    
    # 生成报告
    generate_conversation_report_html(conversations, "report.html")
    
    # 发送通知
    stats_summary = stats.get_stats()
    notifier.send_success_notification(stats_summary)

if __name__ == "__main__":
    main()
```

### 3. 配置文件更新

可能需要更新配置文件结构，例如：

```json
{
  "ai_config": {
    "enabled": true,
    "provider": "ollama",
    "model": "qwen2.5:7b"
  },
  "notification_config": {
    "enabled_services": ["dingtalk", "email"],
    "dingtalk": {
      "webhook_url": "your_webhook_url"
    }
  },
  "question_config": {
    "file": "questions.json",
    "selection_method": "random"
  }
}
```

## 优势

1. **模块化**: 每个模块专注于特定功能，职责清晰
2. **可维护性**: 更容易定位和修复问题
3. **可扩展性**: 可以独立扩展某个模块的功能
4. **可测试性**: 可以针对每个模块编写单元测试
5. **可重用性**: 模块可以在其他项目中重用

## 注意事项

1. **依赖管理**: 确保所有必要的依赖包都已安装
2. **配置迁移**: 原有的配置可能需要适配新的模块结构
3. **逐步迁移**: 建议逐个模块进行迁移，确保功能正常
4. **测试验证**: 迁移后要充分测试所有功能

## 下一步工作

1. 将原 `tk_chat_automation.py` 中的主程序逻辑重构为使用新模块
2. 更新相关的配置文件和文档
3. 编写单元测试
4. 优化各模块间的接口设计 