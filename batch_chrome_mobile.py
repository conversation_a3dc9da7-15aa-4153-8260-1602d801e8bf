#!/usr/bin/env python3
"""
Chrome批量启动器 - 移动设备模拟版本
使用Chrome DevTools Protocol来实现真正的移动设备模拟
"""

import subprocess
import sys
import os
import time
import signal
import json
import requests
from typing import List, Dict, Optional

class ChromeMobileLauncher:
    def __init__(self, url_file: str = "test_urls_pre.txt", device_name: str = "iPhone 13 Pro"):
        self.url_file = url_file
        self.device_name = device_name
        self.chrome_processes: List[subprocess.Popen] = []
        self.process_info: Dict[int, Dict] = {}
        
        # 移动设备配置
        self.device_configs = {
            "iPhone 13 Pro": {
                "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                "width": 390,
                "height": 844,
                "device_scale_factor": 3,
                "mobile": True,
                "touch": True
            },
            "iPhone 12": {
                "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
                "width": 390,
                "height": 844,
                "device_scale_factor": 3,
                "mobile": True,
                "touch": True
            },
            "iPad Pro": {
                "user_agent": "Mozilla/5.0 (iPad; CPU OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                "width": 1024,
                "height": 1366,
                "device_scale_factor": 2,
                "mobile": True,
                "touch": True
            },
            "Samsung Galaxy S21": {
                "user_agent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36",
                "width": 360,
                "height": 800,
                "device_scale_factor": 3,
                "mobile": True,
                "touch": True
            }
        }
    
    def find_chrome_executable(self) -> str:
        """查找Chrome可执行文件路径"""
        possible_paths = [
            # macOS
            "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
            "/Applications/Chromium.app/Contents/MacOS/Chromium",
            # Linux
            "/usr/bin/google-chrome",
            "/usr/bin/google-chrome-stable",
            "/usr/bin/chromium-browser",
            "/usr/bin/chromium",
            # Windows (如果在WSL中运行)
            "/mnt/c/Program Files/Google/Chrome/Application/chrome.exe",
            "/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe",
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        
        # 尝试使用系统PATH中的chrome
        try:
            result = subprocess.run(["which", "google-chrome"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                return result.stdout.strip()
        except:
            pass
            
        try:
            result = subprocess.run(["which", "chrome"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                return result.stdout.strip()
        except:
            pass
        
        raise FileNotFoundError("未找到Chrome浏览器，请确保已安装Chrome或Chromium")
    
    def read_urls(self) -> List[str]:
        """读取URL文件"""
        if not os.path.exists(self.url_file):
            raise FileNotFoundError(f"URL文件不存在: {self.url_file}")
        
        urls = []
        with open(self.url_file, 'r', encoding='utf-8') as f:
            for line in f:
                url = line.strip()
                if url and not url.startswith('#'):  # 忽略空行和注释行
                    urls.append(url)
        
        return urls
    
    def launch_chrome_with_devtools(self, url: str, index: int) -> subprocess.Popen:
        """启动Chrome进程，启用DevTools协议"""
        chrome_path = self.find_chrome_executable()
        
        # 获取设备配置
        device_config = self.device_configs.get(self.device_name, self.device_configs["iPhone 13 Pro"])
        
        # 计算调试端口
        debug_port = 9222 + index
        
        # Chrome启动参数
        chrome_args = [
            chrome_path,
            f"--remote-debugging-port={debug_port}",  # 启用远程调试
            "--new-window",
            "--no-first-run",
            "--no-default-browser-check",
            f"--user-data-dir=/tmp/chrome_profile_{index}_{int(time.time())}",
            "--disable-web-security",
            "--disable-features=VizDisplayCompositor",
            "--disable-extensions",
            "--disable-plugins",
            "--disable-default-apps",
            "--disable-background-timer-throttling",
            "--disable-backgrounding-occluded-windows",
            "--disable-renderer-backgrounding",
            
            # 基本的移动设备参数
            f"--user-agent={device_config['user_agent']}",
            f"--window-size={device_config['width'] + 20},{device_config['height'] + 120}",
            f"--window-position={50 + (index % 4) * (device_config['width'] + 40)},{50 + (index // 4) * (device_config['height'] + 140)}",
            
            url,
        ]
        
        try:
            process = subprocess.Popen(
                chrome_args,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL,
                preexec_fn=os.setsid if os.name != 'nt' else None
            )
            
            # 保存进程信息
            self.process_info[process.pid] = {
                "debug_port": debug_port,
                "device_config": device_config,
                "url": url,
                "index": index
            }
            
            return process
        except Exception as e:
            print(f"启动Chrome进程失败 (URL {index+1}): {e}")
            return None
    
    def setup_mobile_emulation(self, debug_port: int, device_config: Dict):
        """通过DevTools协议设置移动设备模拟"""
        try:
            # 等待Chrome启动
            time.sleep(2)
            
            # 获取标签页列表
            tabs_response = requests.get(f"http://localhost:{debug_port}/json", timeout=5)
            if tabs_response.status_code != 200:
                print(f"无法连接到Chrome DevTools (端口 {debug_port})")
                return False
            
            tabs = tabs_response.json()
            if not tabs:
                print(f"未找到标签页 (端口 {debug_port})")
                return False
            
            # 使用第一个标签页
            tab = tabs[0]
            ws_url = tab.get('webSocketDebuggerUrl')
            if not ws_url:
                print(f"未找到WebSocket调试URL (端口 {debug_port})")
                return False
            
            # 发送设备模拟命令
            import websocket
            import json
            
            def on_open(ws):
                # 启用设备模拟
                emulation_params = {
                    "id": 1,
                    "method": "Emulation.setDeviceMetricsOverride",
                    "params": {
                        "width": device_config["width"],
                        "height": device_config["height"],
                        "deviceScaleFactor": device_config["device_scale_factor"],
                        "mobile": device_config["mobile"],
                        "fitWindow": False
                    }
                }
                ws.send(json.dumps(emulation_params))
                
                # 设置触摸事件
                if device_config.get("touch"):
                    touch_params = {
                        "id": 2,
                        "method": "Emulation.setTouchEmulationEnabled",
                        "params": {
                            "enabled": True
                        }
                    }
                    ws.send(json.dumps(touch_params))
                
                # 设置User-Agent
                ua_params = {
                    "id": 3,
                    "method": "Emulation.setUserAgentOverride",
                    "params": {
                        "userAgent": device_config["user_agent"]
                    }
                }
                ws.send(json.dumps(ua_params))
                
                ws.close()
            
            # 创建WebSocket连接
            ws = websocket.WebSocketApp(ws_url, on_open=on_open)
            ws.run_forever()
            
            return True
            
        except Exception as e:
            print(f"设置移动设备模拟失败 (端口 {debug_port}): {e}")
            return False
    
    def launch_all_browsers(self):
        """启动所有浏览器进程"""
        urls = self.read_urls()
        device_config = self.device_configs.get(self.device_name, self.device_configs["iPhone 13 Pro"])
        print(f"找到 {len(urls)} 个URL，开始启动Chrome浏览器...")
        print(f"模拟设备: {self.device_name}")
        print(f"设备配置: {device_config['width']}x{device_config['height']} @ {device_config['device_scale_factor']}x")
        
        for i, url in enumerate(urls):
            print(f"启动浏览器 {i+1}/{len(urls)}: {url}")
            process = self.launch_chrome_with_devtools(url, i)
            
            if process:
                self.chrome_processes.append(process)
                
                # 设置移动设备模拟
                debug_port = self.process_info[process.pid]["debug_port"]
                device_config = self.process_info[process.pid]["device_config"]
                
                print(f"  设置移动设备模拟 (端口 {debug_port})...")
                if self.setup_mobile_emulation(debug_port, device_config):
                    print(f"  ✓ 移动设备模拟设置成功")
                else:
                    print(f"  ✗ 移动设备模拟设置失败")
                
                time.sleep(1)  # 稍微延迟，避免同时启动太多进程
            else:
                print(f"跳过URL {i+1}（启动失败）")
        
        print(f"\n成功启动了 {len(self.chrome_processes)} 个Chrome浏览器进程")
        self.show_process_info()
    
    def show_process_info(self):
        """显示进程信息"""
        print("\n当前运行的Chrome进程:")
        print("-" * 80)
        for process in self.chrome_processes:
            if process.poll() is None:  # 进程仍在运行
                info = self.process_info.get(process.pid, {})
                debug_port = info.get("debug_port", "N/A")
                url = info.get("url", "N/A")
                index = info.get("index", 0)
                print(f"PID {process.pid}: URL {index+1} (调试端口: {debug_port})")
                print(f"  {url}")
        print("-" * 80)
    
    def wait_for_user_input(self):
        """等待用户输入"""
        print("\n所有浏览器已启动完成！")
        print("请在各个浏览器中完成您的操作...")
        print("\n可用命令:")
        print("  'status' 或 's' - 查看进程状态")
        print("  'quit' 或 'q' - 关闭所有浏览器进程")
        print("  'help' 或 'h' - 显示帮助信息")
        
        while True:
            try:
                user_input = input("\n请输入命令 (输入 'quit' 关闭所有进程): ").strip().lower()
                
                if user_input in ['quit', 'q', 'exit']:
                    break
                elif user_input in ['status', 's']:
                    self.check_process_status()
                elif user_input in ['help', 'h']:
                    self.show_help()
                else:
                    print("未知命令，请输入 'help' 查看可用命令")
                    
            except KeyboardInterrupt:
                print("\n\n检测到 Ctrl+C，准备关闭所有进程...")
                break
            except EOFError:
                print("\n\n检测到输入结束，准备关闭所有进程...")
                break
    
    def show_help(self):
        """显示帮助信息"""
        print("\n=== 帮助信息 ===")
        print("命令列表:")
        print("  status, s  - 查看当前运行的Chrome进程状态")
        print("  quit, q    - 关闭所有Chrome进程并退出程序")
        print("  help, h    - 显示此帮助信息")
        print("  Ctrl+C     - 强制关闭所有进程并退出")
    
    def check_process_status(self):
        """检查进程状态"""
        running_count = 0
        print("\n进程状态检查:")
        print("-" * 80)
        
        for process in self.chrome_processes:
            info = self.process_info.get(process.pid, {})
            debug_port = info.get("debug_port", "N/A")
            url = info.get("url", "N/A")
            index = info.get("index", 0)
            
            if process.poll() is None:
                print(f"✓ 运行中 - PID {process.pid}: URL {index+1} (端口: {debug_port})")
                running_count += 1
            else:
                print(f"✗ 已结束 - PID {process.pid}: URL {index+1} (端口: {debug_port})")
        
        print("-" * 80)
        print(f"总计: {len(self.chrome_processes)} 个进程，{running_count} 个正在运行")
    
    def cleanup_processes(self):
        """清理所有Chrome进程"""
        if not self.chrome_processes:
            print("没有需要关闭的进程")
            return
        
        print(f"\n开始关闭 {len(self.chrome_processes)} 个Chrome进程...")
        
        terminated_count = 0
        for process in self.chrome_processes:
            try:
                if process.poll() is None:  # 进程仍在运行
                    print(f"关闭进程 PID {process.pid}...")
                    
                    # 尝试优雅关闭
                    if os.name != 'nt':
                        os.killpg(os.getpgid(process.pid), signal.SIGTERM)
                    else:
                        process.terminate()
                    
                    # 等待进程结束
                    try:
                        process.wait(timeout=5)
                        terminated_count += 1
                    except subprocess.TimeoutExpired:
                        # 强制关闭
                        print(f"强制关闭进程 PID {process.pid}")
                        if os.name != 'nt':
                            os.killpg(os.getpgid(process.pid), signal.SIGKILL)
                        else:
                            process.kill()
                        terminated_count += 1
                else:
                    terminated_count += 1
                    
            except Exception as e:
                print(f"关闭进程 PID {process.pid} 时出错: {e}")
        
        print(f"成功关闭了 {terminated_count} 个进程")
        self.chrome_processes.clear()
        self.process_info.clear()
    
    def run(self):
        """主运行方法"""
        try:
            print("=== Chrome批量启动器 (移动设备模拟版) ===")
            print(f"URL文件: {self.url_file}")
            
            # 启动所有浏览器
            self.launch_all_browsers()
            
            # 等待用户操作
            self.wait_for_user_input()
            
        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
        except Exception as e:
            print(f"\n程序运行出错: {e}")
        finally:
            # 清理所有进程
            self.cleanup_processes()
            print("\n程序结束")

def main():
    """主函数"""
    url_file = "test_urls_pre.txt"
    device_name = "iPhone 13 Pro"
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        url_file = sys.argv[1]
    if len(sys.argv) > 2:
        device_name = sys.argv[2]
    
    print(f"使用设备模拟: {device_name}")
    print("可用设备列表: iPhone 13 Pro, iPhone 12, iPad Pro, Samsung Galaxy S21")
    
    launcher = ChromeMobileLauncher(url_file, device_name)
    launcher.run()

if __name__ == "__main__":
    main()
