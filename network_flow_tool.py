#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络流量监控工具 (基于进程ID)
基于Java NetworkFlowTool的Python实现

功能:
- 支持通过进程ID监控应用流量
- 根据Android API版本选择不同的监控方式
- 计算增量流量数据
- 提供详细的流量统计信息

使用方法: python3 network_flow_tool.py
"""

import subprocess
import time
import re
import logging
from datetime import datetime
from typing import Optional, Dict, List
from dataclasses import dataclass

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('network_flow_tool.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

@dataclass
class NetworkFlowBean:
    """网络流量数据Bean"""
    index: int = 0
    timestamp: str = ""
    original_receive_flow: float = 0.0  # 原始接收流量(KB)
    original_send_flow: float = 0.0     # 原始发送流量(KB)
    receive_flow: float = 0.0           # 接收流量(KB)
    send_flow: float = 0.0              # 发送流量(KB)
    start_time: Optional[int] = None    # 开始时间戳(毫秒)
    end_time: Optional[int] = None      # 结束时间戳(毫秒)
    cost: int = 0                       # 耗时(毫秒)
    output: str = ""                    # 原始输出
    
    @property
    def total_flow(self) -> float:
        """总流量(KB)"""
        return self.receive_flow + self.send_flow
    
    @property
    def receive_flow_mb(self) -> float:
        """接收流量(MB)"""
        return self.receive_flow / 1024.0
    
    @property
    def send_flow_mb(self) -> float:
        """发送流量(MB)"""
        return self.send_flow / 1024.0
    
    @property
    def total_flow_mb(self) -> float:
        """总流量(MB)"""
        return self.total_flow / 1024.0
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            'index': self.index,
            'timestamp': self.timestamp,
            'receive_flow_kb': self.receive_flow,
            'send_flow_kb': self.send_flow,
            'total_flow_kb': self.total_flow,
            'receive_flow_mb': self.receive_flow_mb,
            'send_flow_mb': self.send_flow_mb,
            'total_flow_mb': self.total_flow_mb,
            'cost_ms': self.cost
        }


class NetworkFlowTool:
    """网络流量监控工具"""
    
    def __init__(self, udid: str):
        self.logger = logging.getLogger("PERFORMANCE")
        self.udid = udid
        self.uid = 0
        self.index = 0
        self.find_app_flow = False
        self.init_flow = False
        self.init_receive_flow = 0.0
        self.init_send_flow = 0.0
        self.api_level = None
        
    def get_api_level(self) -> Optional[int]:
        """获取Android API Level"""
        try:
            cmd = ["adb", "-s", self.udid, "shell", "getprop", "ro.build.version.sdk"]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            if result.returncode == 0 and result.stdout.strip():
                api_level = int(result.stdout.strip())
                self.logger.info(f"📱 Android API Level: {api_level}")
                return api_level
            return None
        except Exception as e:
            self.logger.error(f"获取API Level失败: {str(e)}")
            return None
    
    def adb_shell(self, command: str, timeout: int = 6) -> Optional[str]:
        """执行ADB Shell命令"""
        try:
            cmd = ["adb", "-s", self.udid, "shell", command]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=timeout)
            if result.returncode == 0:
                return result.stdout
            else:
                self.logger.error(f"ADB命令执行失败: {result.stderr}")
                return None
        except subprocess.TimeoutExpired:
            self.logger.error(f"ADB命令超时: {command}")
            return None
        except Exception as e:
            self.logger.error(f"ADB命令异常: {str(e)}")
            return None
    
    def get_uid_by_process_id(self, process_id: int) -> int:
        """通过进程ID获取UID"""
        try:
            output = self.adb_shell(f"cat /proc/{process_id}/status", 10)
            if not output:
                return 0
            
            lines = output.split('\n')
            for line in lines:
                if 'Uid' in line:
                    self.logger.info(f"Uid line: {line}")
                    args = line.split()
                    if args:
                        # 通常Uid行格式为: Uid: real_uid effective_uid saved_uid filesystem_uid
                        # 我们取最后一个值
                        uid = int(args[-1]) if args[-1].isdigit() else 0
                        self.logger.info(f"✅ 获取到UID: {uid}")
                        return uid
            return 0
        except Exception as e:
            self.logger.error(f"获取UID失败: {str(e)}")
            return 0
    
    def get_network_flow_api29_plus(self, process_id: int) -> tuple:
        """Android API 29+ 流量获取方式 (使用 /proc/{pid}/net/dev)"""
        send_flow = 0.0
        receive_flow = 0.0
        
        try:
            start_time = int(time.time() * 1000)
            output = self.adb_shell(f"cat /proc/{process_id}/net/dev|grep wlan0")
            end_time = int(time.time() * 1000)
            cost = end_time - start_time
            
            if not output:
                return send_flow, receive_flow, output, cost, start_time, end_time
            
            # 解析wlan0数据
            # 格式: wlan0: bytes packets errs drop fifo frame compressed multicast ...
            if 'wlan0:' in output:
                format_str = output.split('wlan0:')[1].strip()
                stats = re.split(r'\s+', format_str)
                
                if len(stats) >= 9:
                    recv_str = stats[0]  # 接收字节数
                    send_str = stats[8]  # 发送字节数
                    
                    self.find_app_flow = True
                    r_flow = float(recv_str) if recv_str.isdigit() else 0.0
                    self.logger.debug(f"rFlow: {r_flow}")
                    receive_flow += r_flow / 1024.0  # 转换为KB
                    
                    s_flow = float(send_str) if send_str.isdigit() else 0.0
                    self.logger.debug(f"sFlow: {s_flow}")
                    send_flow += s_flow / 1024.0  # 转换为KB
                    
                    if self.find_app_flow and not self.init_flow:
                        self.init_flow = True
                        self.init_receive_flow = receive_flow
                        self.init_send_flow = send_flow
            
            return send_flow, receive_flow, output, cost, start_time, end_time
            
        except Exception as e:
            self.logger.error(f"API29+流量获取异常: {str(e)}")
            return 0.0, 0.0, "", 0, 0, 0
    
    def get_network_flow_legacy(self, process_id: int) -> tuple:
        """Android API < 29 流量获取方式 (使用 /proc/net/xt_qtaguid/stats)"""
        send_flow = 0.0
        receive_flow = 0.0
        
        try:
            start_time = int(time.time() * 1000)
            output = self.adb_shell("cat /proc/net/xt_qtaguid/stats")
            end_time = int(time.time() * 1000)
            cost = end_time - start_time
            
            if not output:
                return send_flow, receive_flow, output, cost, start_time, end_time
            
            lines = output.split('\n')
            
            for line in lines:
                if not line.strip():
                    continue
                
                if self.uid == 0:
                    self.uid = self.get_uid_by_process_id(process_id)
                
                args = line.split()
                if len(args) >= 8:
                    try:
                        line_uid = int(args[3])
                        if line_uid == self.uid:
                            self.find_app_flow = True
                            self.logger.debug(f"line: {line}")
                            
                            r_flow = float(args[5]) if args[5].replace('.', '').isdigit() else 0.0
                            self.logger.debug(f"rFlow: {r_flow}")
                            receive_flow += r_flow / 1024.0  # 转换为KB
                            
                            s_flow = float(args[7]) if args[7].replace('.', '').isdigit() else 0.0
                            self.logger.debug(f"sFlow: {s_flow}")
                            send_flow += s_flow / 1024.0  # 转换为KB
                    except (ValueError, IndexError):
                        continue
            
            if self.find_app_flow and not self.init_flow:
                self.init_flow = True
                self.init_receive_flow = receive_flow
                self.init_send_flow = send_flow
            
            return send_flow, receive_flow, output, cost, start_time, end_time
            
        except Exception as e:
            self.logger.error(f"Legacy流量获取异常: {str(e)}")
            if "Resource temporarily unavailable" in str(e):
                time.sleep(10)
            return 0.0, 0.0, "", 0, 0, 0
    
    def get_network_flow(self, process_id: int) -> NetworkFlowBean:
        """获取网络流量数据"""
        send_flow = 0.0
        receive_flow = 0.0
        output = ""
        cost = 0
        start_time = None
        end_time = None
        
        try:
            # 获取API Level
            if self.api_level is None:
                self.api_level = self.get_api_level()
            
            if self.api_level is not None:
                if self.api_level >= 29:
                    # Android 10+ 使用 /proc/{pid}/net/dev
                    send_flow, receive_flow, output, cost, start_time, end_time = \
                        self.get_network_flow_api29_plus(process_id)
                else:
                    # Android 9- 使用 /proc/net/xt_qtaguid/stats
                    send_flow, receive_flow, output, cost, start_time, end_time = \
                        self.get_network_flow_legacy(process_id)
            
        except Exception as e:
            self.logger.error(f"获取网络流量时发生错误: {str(e)}")
            if "Resource temporarily unavailable" in str(e):
                time.sleep(10)
        
        # 构建返回对象
        network_flow_bean = self.build_network_flow_bean(
            receive_flow - self.init_receive_flow,
            send_flow - self.init_send_flow
        )
        
        network_flow_bean.output = output
        network_flow_bean.start_time = start_time
        network_flow_bean.end_time = end_time
        network_flow_bean.cost = cost
        
        return network_flow_bean
    
    def build_network_flow_bean(self, receive_flow: float, send_flow: float) -> NetworkFlowBean:
        """构建网络流量Bean对象"""
        network_flow_bean = NetworkFlowBean()
        network_flow_bean.index = self.index
        self.index += 1
        network_flow_bean.timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        network_flow_bean.original_receive_flow = receive_flow
        network_flow_bean.original_send_flow = send_flow
        network_flow_bean.receive_flow = abs(receive_flow)
        network_flow_bean.send_flow = abs(send_flow)
        
        return network_flow_bean
    
    def get_process_id_by_package(self, package_name: str) -> Optional[int]:
        """通过包名获取进程ID"""
        try:
            # 使用pidof命令获取进程ID
            output = self.adb_shell(f"pidof {package_name}")
            if output and output.strip():
                pids = output.strip().split()
                if pids:
                    pid = int(pids[0])  # 取第一个PID
                    self.logger.info(f"✅ 找到进程ID: {pid} (包名: {package_name})")
                    return pid
            
            # 备用方法：使用ps命令
            output = self.adb_shell("ps")
            if output:
                lines = output.split('\n')
                for line in lines:
                    if package_name in line:
                        parts = line.split()
                        if len(parts) >= 2:
                            try:
                                pid = int(parts[1])
                                self.logger.info(f"✅ 通过ps找到进程ID: {pid} (包名: {package_name})")
                                return pid
                            except ValueError:
                                continue
            
            self.logger.warning(f"⚠️  未找到包名 {package_name} 对应的进程")
            return None
            
        except Exception as e:
            self.logger.error(f"获取进程ID失败: {str(e)}")
            return None
    
    def reset_init_flow(self):
        """重置初始流量数据"""
        self.init_flow = False
        self.init_receive_flow = 0.0
        self.init_send_flow = 0.0
        self.find_app_flow = False
        self.logger.info("🔄 已重置初始流量数据")


def main():
    """主函数 - 交互式流量监控"""
    print("📊 网络流量监控工具 (基于进程ID)")
    print("=" * 50)
    
    # 检查ADB连接
    try:
        result = subprocess.run(["adb", "devices"], capture_output=True, text=True, timeout=10)
        if result.returncode != 0:
            print("❌ ADB不可用，请确保已安装Android SDK")
            return
        
        devices = [line.split('\t')[0] for line in result.stdout.strip().split('\n')[1:] if '\tdevice' in line]
        if not devices:
            print("❌ 没有连接的设备")
            return
        
        device_id = devices[0]
        print(f"✅ 设备已连接: {device_id}")
        
    except Exception as e:
        print(f"❌ ADB连接检查失败: {str(e)}")
        return
    
    # 创建流量监控工具
    flow_tool = NetworkFlowTool(device_id)
    
    # 获取可监控的应用
    try:
        result = subprocess.run(["adb", "-s", device_id, "shell", "pm", "list", "packages", "-3"], 
                              capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            packages = []
            lines = result.stdout.strip().split('\n')
            for line in lines:
                if line.startswith('package:'):
                    package_name = line.replace('package:', '').strip()
                    packages.append(package_name)
            
            if packages:
                print(f"\n📋 找到 {len(packages)} 个第三方应用:")
                for i, pkg in enumerate(packages[:10], 1):  # 只显示前10个
                    print(f"{i:2d}. {pkg}")
                
                if len(packages) > 10:
                    print(f"    ... 还有 {len(packages) - 10} 个应用")
                
                # 用户选择或输入包名
                choice = input(f"\n请选择要监控的应用 (1-{min(10, len(packages))}) 或输入包名: ").strip()
                
                if choice.isdigit():
                    index = int(choice) - 1
                    if 0 <= index < min(10, len(packages)):
                        selected_package = packages[index]
                    else:
                        print("❌ 无效选择")
                        return
                else:
                    selected_package = choice
                
                print(f"\n🎯 选择的应用: {selected_package}")
                
                # 获取进程ID
                process_id = flow_tool.get_process_id_by_package(selected_package)
                if process_id is None:
                    print("❌ 无法获取进程ID，应用可能未运行")
                    return
                
                print(f"🔍 进程ID: {process_id}")
                
                # 开始监控
                print(f"\n📊 开始监控流量... (Ctrl+C 停止)")
                print("-" * 60)
                print(f"{'序号':<6} {'时间':<20} {'下载(KB)':<12} {'上传(KB)':<12} {'总计(KB)':<12} {'耗时(ms)':<10}")
                print("-" * 60)
                
                try:
                    while True:
                        flow_bean = flow_tool.get_network_flow(process_id)
                        
                        print(f"{flow_bean.index:<6} {flow_bean.timestamp:<20} "
                              f"{flow_bean.receive_flow:<12.2f} {flow_bean.send_flow:<12.2f} "
                              f"{flow_bean.total_flow:<12.2f} {flow_bean.cost:<10}")
                        
                        time.sleep(5)  # 5秒间隔
                        
                except KeyboardInterrupt:
                    print(f"\n👋 监控已停止")
                    
            else:
                print("❌ 没有找到第三方应用")
        else:
            print(f"❌ 获取应用列表失败: {result.stderr}")
            
    except Exception as e:
        print(f"❌ 程序异常: {str(e)}")


if __name__ == "__main__":
    main() 