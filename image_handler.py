"""
图片处理模块
负责图片上传、下载、验证、OSS存储等功能
"""

import os
import logging
import hashlib
import urllib.parse
import requests
import time
import traceback
from PIL import Image


def download_image_from_url(url, download_dir="downloaded_images"):
    """从URL下载图片到本地
    
    Args:
        url: 图片URL
        download_dir: 下载目录
        
    Returns:
        str: 本地图片文件路径，失败时返回None
    """
    try:
        # 创建下载目录
        os.makedirs(download_dir, exist_ok=True)
        
        # 生成唯一的文件名
        url_hash = hashlib.md5(url.encode()).hexdigest()[:8]
        
        # 获取文件扩展名
        parsed_url = urllib.parse.urlparse(url)
        path = parsed_url.path
        _, ext = os.path.splitext(path)
        
        # 如果没有扩展名，默认使用.jpg
        if not ext:
            ext = '.jpg'
        
        filename = f"image_{url_hash}{ext}"
        filepath = os.path.join(download_dir, filename)
        
        # 如果文件已存在，直接返回路径
        if os.path.exists(filepath):
            logging.info(f"图片已存在，使用缓存: {filepath}")
            return filepath
        
        # 下载图片
        logging.info(f"正在下载图片: {url}")
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        # 保存图片
        with open(filepath, 'wb') as f:
            f.write(response.content)
        
        logging.info(f"图片下载成功: {filepath}")
        return filepath
        
    except Exception as e:
        logging.error(f"下载图片失败: {url}, 错误: {str(e)}")
        return None


def validate_image_file(filepath):
    """验证图片文件是否有效
    
    Args:
        filepath: 图片文件路径
        
    Returns:
        bool: 图片是否有效
    """
    try:
        if not os.path.exists(filepath):
            logging.error(f"图片文件不存在: {filepath}")
            return False
        
        # 检查文件大小（不超过10MB）
        file_size = os.path.getsize(filepath)
        if file_size > 10 * 1024 * 1024:
            logging.error(f"图片文件过大: {filepath}, 大小: {file_size/1024/1024:.2f}MB")
            return False
        
        # 尝试打开图片验证格式
        with Image.open(filepath) as img:
            img.verify()
        
        logging.info(f"图片文件验证成功: {filepath}")
        return True
        
    except Exception as e:
        logging.error(f"图片文件验证失败: {filepath}, 错误: {str(e)}")
        return False


def upload_image_to_chat(page, image_path, screenshots_dir):
    """在聊天界面上传图片
    
    Args:
        page: Playwright页面对象
        image_path: 图片文件路径
        screenshots_dir: 截图目录
        
    Returns:
        bool: 上传是否成功
    """
    try:
        logging.info(f"正在上传图片: {image_path}")
        
        # 首先验证图片文件
        if not validate_image_file(image_path):
            return False
        
        # 查找并使用文件上传控件
        upload_selectors = [
            'input[type="file"]',  # 标准文件输入控件
            'input[accept*="image"]',  # 接受图片的输入控件
            'input[accept*="*"]'  # 接受所有文件的输入控件
        ]
        
        upload_success = False
        file_input = None
        
        # 查找文件上传input控件
        for selector in upload_selectors:
            try:
                elements = page.locator(selector).all()
                for element in elements:
                    # 检查元素是否可用（可能不一定可见）
                    if element.is_enabled():
                        file_input = element
                        logging.info(f"找到文件上传控件: {selector}")
                        break
                if file_input:
                    break
            except Exception as e:
                logging.debug(f"查找上传控件失败 {selector}: {str(e)}")
                continue
        
        if file_input:
            # 设置文件到输入控件
            try:
                file_input.set_input_files(image_path)
                logging.info(f"成功设置上传文件: {image_path}")
                upload_success = True
                
                # 等待上传完成
                time.sleep(2)
                
                # 截图确认上传状态
                upload_screenshot_path = os.path.join(screenshots_dir, "after_image_upload.png")
                page.screenshot(path=upload_screenshot_path)
                logging.info(f"图片上传后截图已保存: {upload_screenshot_path}")
                
            except Exception as e:
                logging.error(f"设置上传文件失败: {str(e)}")
        else:
            logging.error("无法找到文件上传控件")
            
            # 保存当前页面状态以供调试
            debug_screenshot_path = os.path.join(screenshots_dir, "upload_debug.png")
            page.screenshot(path=debug_screenshot_path)
            logging.info(f"调试截图已保存: {debug_screenshot_path}")
        
        return upload_success
        
    except Exception as e:
        logging.error(f"上传图片时出错: {str(e)}")
        logging.error(traceback.format_exc())
        return False


def get_image_dimensions(image_path):
    """获取图片尺寸
    
    Args:
        image_path: 图片文件路径
        
    Returns:
        tuple: (width, height)，失败时返回(0, 0)
    """
    try:
        with Image.open(image_path) as img:
            return img.size
    except Exception as e:
        logging.error(f"获取图片尺寸失败: {image_path}, 错误: {str(e)}")
        return (0, 0)


def resize_image(image_path, max_width=800, max_height=600, quality=85):
    """调整图片大小
    
    Args:
        image_path: 图片文件路径
        max_width: 最大宽度
        max_height: 最大高度
        quality: JPEG质量（1-100）
        
    Returns:
        str: 调整后的图片路径，失败时返回原路径
    """
    try:
        with Image.open(image_path) as img:
            # 计算新尺寸
            original_width, original_height = img.size
            ratio = min(max_width / original_width, max_height / original_height)
            
            # 如果图片已经足够小，不需要调整
            if ratio >= 1:
                return image_path
            
            new_width = int(original_width * ratio)
            new_height = int(original_height * ratio)
            
            # 调整图片大小
            resized_img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # 生成新文件名
            base_name, ext = os.path.splitext(image_path)
            resized_path = f"{base_name}_resized{ext}"
            
            # 保存调整后的图片
            if ext.lower() in ['.jpg', '.jpeg']:
                resized_img.save(resized_path, 'JPEG', quality=quality, optimize=True)
            else:
                resized_img.save(resized_path, optimize=True)
            
            logging.info(f"图片已调整大小: {image_path} -> {resized_path}")
            logging.info(f"尺寸变化: {original_width}x{original_height} -> {new_width}x{new_height}")
            
            return resized_path
            
    except Exception as e:
        logging.error(f"调整图片大小失败: {image_path}, 错误: {str(e)}")
        return image_path


def compress_image(image_path, quality=75, max_file_size_mb=2):
    """压缩图片文件大小
    
    Args:
        image_path: 图片文件路径
        quality: JPEG质量（1-100）
        max_file_size_mb: 最大文件大小（MB）
        
    Returns:
        str: 压缩后的图片路径，失败时返回原路径
    """
    try:
        # 检查原文件大小
        original_size = os.path.getsize(image_path)
        max_size_bytes = max_file_size_mb * 1024 * 1024
        
        if original_size <= max_size_bytes:
            logging.info(f"图片文件大小已满足要求: {original_size / 1024 / 1024:.2f}MB")
            return image_path
        
        with Image.open(image_path) as img:
            # 生成压缩后的文件名
            base_name, ext = os.path.splitext(image_path)
            compressed_path = f"{base_name}_compressed.jpg"  # 统一使用JPEG格式
            
            # 尝试不同的质量级别
            for q in range(quality, 10, -10):
                img.save(compressed_path, 'JPEG', quality=q, optimize=True)
                
                # 检查压缩后的文件大小
                compressed_size = os.path.getsize(compressed_path)
                if compressed_size <= max_size_bytes:
                    logging.info(f"图片压缩成功: {image_path} -> {compressed_path}")
                    logging.info(f"文件大小: {original_size / 1024 / 1024:.2f}MB -> {compressed_size / 1024 / 1024:.2f}MB")
                    logging.info(f"压缩质量: {q}")
                    return compressed_path
            
            # 如果仍然太大，尝试进一步调整尺寸
            logging.warning(f"图片仍然过大，尝试调整尺寸: {compressed_path}")
            resized_path = resize_image(compressed_path, max_width=600, max_height=400)
            
            final_size = os.path.getsize(resized_path)
            logging.info(f"最终压缩结果: {original_size / 1024 / 1024:.2f}MB -> {final_size / 1024 / 1024:.2f}MB")
            
            return resized_path
            
    except Exception as e:
        logging.error(f"压缩图片失败: {image_path}, 错误: {str(e)}")
        return image_path


def convert_image_format(image_path, target_format='JPEG'):
    """转换图片格式
    
    Args:
        image_path: 原图片路径
        target_format: 目标格式（JPEG, PNG, WEBP等）
        
    Returns:
        str: 转换后的图片路径，失败时返回原路径
    """
    try:
        with Image.open(image_path) as img:
            # 如果是RGBA模式且目标格式是JPEG，需要转换为RGB
            if img.mode == 'RGBA' and target_format.upper() == 'JPEG':
                # 创建白色背景
                background = Image.new('RGB', img.size, (255, 255, 255))
                background.paste(img, mask=img.split()[-1])  # 使用alpha通道作为mask
                img = background
            
            # 生成新文件名
            base_name, _ = os.path.splitext(image_path)
            ext_mapping = {
                'JPEG': '.jpg',
                'PNG': '.png',
                'WEBP': '.webp'
            }
            new_ext = ext_mapping.get(target_format.upper(), '.jpg')
            converted_path = f"{base_name}_converted{new_ext}"
            
            # 保存转换后的图片
            img.save(converted_path, target_format.upper(), quality=85, optimize=True)
            
            logging.info(f"图片格式转换成功: {image_path} -> {converted_path}")
            return converted_path
            
    except Exception as e:
        logging.error(f"转换图片格式失败: {image_path}, 错误: {str(e)}")
        return image_path


def cleanup_temp_images(directory=".", pattern="temp_*", max_age_hours=24):
    """清理临时图片文件
    
    Args:
        directory: 目录路径
        pattern: 文件名模式
        max_age_hours: 最大保留时间（小时）
    """
    try:
        import glob
        import time
        
        # 查找匹配的文件
        pattern_path = os.path.join(directory, pattern)
        files = glob.glob(pattern_path)
        
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        
        cleaned_count = 0
        for file_path in files:
            try:
                # 检查文件年龄
                file_age = current_time - os.path.getmtime(file_path)
                if file_age > max_age_seconds:
                    os.remove(file_path)
                    cleaned_count += 1
                    logging.debug(f"已删除临时文件: {file_path}")
            except Exception as e:
                logging.warning(f"删除临时文件失败: {file_path}, 错误: {str(e)}")
        
        if cleaned_count > 0:
            logging.info(f"已清理 {cleaned_count} 个临时图片文件")
        
    except Exception as e:
        logging.error(f"清理临时图片文件时出错: {str(e)}")


def extract_image_info(image_path):
    """提取图片信息
    
    Args:
        image_path: 图片文件路径
        
    Returns:
        dict: 图片信息字典
    """
    try:
        with Image.open(image_path) as img:
            info = {
                'path': image_path,
                'format': img.format,
                'mode': img.mode,
                'size': img.size,
                'width': img.size[0],
                'height': img.size[1],
                'file_size': os.path.getsize(image_path),
                'has_transparency': img.mode in ('RGBA', 'LA') or 'transparency' in img.info
            }
            
            # 获取EXIF信息（如果存在）
            if hasattr(img, '_getexif'):
                exif = img._getexif()
                if exif:
                    info['exif'] = exif
            
            return info
            
    except Exception as e:
        logging.error(f"提取图片信息失败: {image_path}, 错误: {str(e)}")
        return {
            'path': image_path,
            'error': str(e)
        } 