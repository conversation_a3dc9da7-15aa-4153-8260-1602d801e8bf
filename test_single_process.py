import os
import sys
import subprocess
import time

print("=== 测试单进程聊天自动化 ===")
print("目标：验证修复后的日志和回复获取功能")

# 设置环境变量
env = os.environ.copy()
env['STRESS_TEST_MODE'] = 'true'
env['STRESS_PROCESS_ID'] = '100'
env['BROWSER_WINDOW_X'] = '50'
env['BROWSER_WINDOW_Y'] = '50'
env['TARGET_URL'] = 'https://tkmates.swireproperties.com.cn?ticket=test'

# 创建测试工作目录
test_dir = 'test_single_100'
os.makedirs(test_dir, exist_ok=True)
os.makedirs(f'{test_dir}/logs', exist_ok=True)

# 复制必要文件
import shutil
if os.path.exists('min_questions.json'):
    shutil.copy2('min_questions.json', f'{test_dir}/min_questions.json')

print(f"测试目录: {test_dir}")

# 启动测试进程
cmd = [sys.executable, '../tk_chat_automation.py', '--test-mode', '--force-text-mode', '--questions-file', 'min_questions.json']

print(f"执行命令: {' '.join(cmd)}")
print("启动单进程测试...")

start_time = time.time()
process = subprocess.Popen(
    cmd,
    cwd=test_dir,
    env=env,
    stdout=subprocess.PIPE,
    stderr=subprocess.PIPE,
    text=True
)

# 等待进程完成或10秒超时
try:
    stdout, stderr = process.communicate(timeout=10)
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"进程完成，用时: {duration:.2f}秒")
    print(f"返回码: {process.returncode}")
    if stdout:
        print(f"标准输出: {stdout[-500:]}")
    if stderr:
        print(f"错误输出: {stderr[-500:]}")
    
except subprocess.TimeoutExpired:
    print("进程运行超过10秒，正在终止...")
    process.terminate()
    try:
        process.wait(timeout=5)
    except subprocess.TimeoutExpired:
        process.kill()

# 检查日志文件
log_dir = f'{test_dir}/logs'
if os.path.exists(log_dir):
    log_files = [f for f in os.listdir(log_dir) if f.endswith('.log')]
    print(f"找到日志文件: {log_files}")
    
    for log_file in log_files:
        log_path = os.path.join(log_dir, log_file)
        size = os.path.getsize(log_path)
        print(f"日志文件 {log_file}: {size} 字节")
        
        if size > 0:
            print("✅ 日志文件有内容！")
            with open(log_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找关键的调试信息
            if "AI容器完整HTML" in content:
                print("✅ 找到完整HTML输出")
            if "找到候选回复" in content:
                print("✅ 找到候选回复列表")
            if "使用选择器获取最新回复" in content:
                print("✅ 找到最新回复获取日志")
                
            # 输出最后1000字符的日志内容
            print("\n=== 最后1000字符的日志内容 ===")
            print(content[-1000:])
        else:
            print("❌ 日志文件为空")
else:
    print("❌ logs目录不存在")

# 保留测试目录以便查看
print(f"保留测试目录供检查: {test_dir}")
