#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os
import sys
import time
import json
import logging
import datetime
import requests
import traceback
import threading
import subprocess
import signal
import psutil
import platform
from typing import Dict, Any, List, Optional
import argparse
import logging
from dingtalk_stream import AckMessage
import dingtalk_stream



# 标记文件定义
RESTART_FLAG_FILE = "restart_flag.txt"  # 重启标记文件
START_FLAG_FILE = "start_flag.txt"      # 启动标记文件
STOP_FLAG_FILE = "stop_flag.txt"        # 停止标记文件
PAUSE_FLAG_FILE = "pause_flag.txt"      # 暂停保活脚本标记文件

# 钉钉Stream配置
DINGTALK_CLIENT_ID = "dinglpyzbgobiv8taugr"  # 更改为你的钉钉应用的ClientID
DINGTALK_CLIENT_SECRET = "ckVQg3jtZtOvQI19ALIGksjjbgWlaRKBbFU01p3q7xWRYiPv5uLKChFbpKGPd6L6"  # 更改为你的钉钉应用的ClientSecret
# 管理员用户ID列表
ADMIN_USER_IDS = [
    "$:LWCP_v1:$xW0BzQERP/+vj+XSXYaTt97ZJJINYPgB",  # 管理员用户ID，例如"manager1234" 
]

def setup_logger():
    logger = logging.getLogger()
    handler = logging.StreamHandler()
    handler.setFormatter(
        logging.Formatter('%(asctime)s %(name)-8s %(levelname)-8s %(message)s [%(filename)s:%(lineno)d]'))
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)
    return logger


def define_options():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--client_id', dest='client_id', required=True,
        help='app_key or suite_key from https://open-dev.digntalk.com'
    )
    parser.add_argument(
        '--client_secret', dest='client_secret', required=True,
        help='app_secret or suite_secret from https://open-dev.digntalk.com'
    )
    options = parser.parse_args()
    return options


# 命令处理类
class CommandHandler(dingtalk_stream.ChatbotHandler):
    def __init__(self, logger: logging.Logger = None):
        super(dingtalk_stream.ChatbotHandler, self).__init__()
        if logger:
            self.logger = logger
        self.commands = {
            "重启自动化": self.restart_automation,
            "启动自动化": self.start_automation,
            "停止自动化": self.stop_automation,
            "查看状态": self.check_status,
            "帮助": self.show_help,
            "更换数据": self.update_questions_data,
        }

    async def process(self, callback: dingtalk_stream.CallbackMessage):
        incoming_message = dingtalk_stream.ChatbotMessage.from_dict(callback.data)
        text = incoming_message.text.content.strip()
        sender_id = incoming_message.sender_id
        conversation_id = incoming_message.conversation_id

        # 处理命令
        response = await self.handle_command(text, sender_id, conversation_id)

        self.logger.info(f"发送回复: {response}")
        
        # 根据返回的内容选择回复类型
        # 对于包含标题(###)的内容使用markdown格式，其他使用普通文本
        if isinstance(response, str):
            if "###" in response or "**" in response:
                # 看起来是markdown格式
                self.reply_markdown("机器人回复", response, incoming_message)
            else:
                # 普通文本
                self.reply_text(response, incoming_message)
        else:
            # 兼容处理，防止老代码返回字典格式
            self.logger.warning(f"收到非字符串响应: {type(response)}")
            self.reply_text(str(response), incoming_message)

        return AckMessage.STATUS_OK, 'OK'
    
    async def handle_command(self, text: str, sender_id: str, conversation_id: str) -> str:
        """处理接收到的命令"""
        text = text.strip()
        
        # 检查是否是已知命令
        for cmd, handler in self.commands.items():
            if text == cmd or text.startswith(cmd + " "):
                self.logger.info(f"收到命令: {cmd}, 发送者: {sender_id}")
                
                # 定义需要管理员权限的命令
                admin_commands = ["重启自动化", "启动自动化", "停止自动化", "更换数据"]
                
                # 检查是否有权限执行此命令
                if cmd in admin_commands and sender_id not in ADMIN_USER_IDS:
                    return f"⚠️ 您没有权限执行此命令: {cmd}\n只有管理员可以执行此命令。"
                
                try:
                    return await handler(text, sender_id, conversation_id)
                except Exception as e:
                    error_msg = f"❌ 执行命令 '{cmd}' 时出错: {str(e)}"
                    self.logger.error(error_msg)
                    self.logger.error(traceback.format_exc())
                    return error_msg
        
        # 未知命令
        return f"❓ 未知命令: {text}\n请输入 '帮助' 查看可用命令列表。"
        
    async def restart_automation(self, text: str, sender_id: str, conversation_id: str) -> str:
        """重启自动化脚本"""
        try:
            # 创建重启标志文件
            with open(RESTART_FLAG_FILE, 'w') as f:
                current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                f.write(f"重启请求时间: {current_time}\n")
                f.write(f"请求者: {sender_id}")
            
            # 如果存在暂停标记文件，则删除它以恢复保活脚本的自动重启功能
            if os.path.exists(PAUSE_FLAG_FILE):
                os.remove(PAUSE_FLAG_FILE)
                self.logger.info("已删除暂停标记文件，保活脚本将恢复自动重启功能")
            
            self.logger.info(f"已创建重启标志文件，请求者: {sender_id}")
            
            return f"✅ 重启命令已发送\n\n保活脚本将检测到重启标记并重启自动化脚本。\n\n请求时间: {current_time}\n请求者: {sender_id}"
            
        except Exception as e:
            error_msg = f"❌ 发送重启命令失败\n错误: {str(e)}"
            self.logger.error(error_msg)
            self.logger.error(traceback.format_exc())
            return error_msg
    
    async def check_status(self, text: str, sender_id: str, conversation_id: str) -> str:
        """检查脚本状态"""
        try:
            # 检查各脚本进程状态
            processes = {
                "tk_chat_automation.py": False,
                "keepalive.py": False,
                "dingtalk_stream_server.py": True  # 当前脚本肯定在运行
            }
            
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['cmdline']:
                        cmd_line = ' '.join(proc.info['cmdline'])
                        for script in processes.keys():
                            if 'python' in cmd_line and script in cmd_line:
                                processes[script] = True
                except:
                    pass
            
            # 构建状态消息
            status_text = "### 📊 系统状态\n\n"
            for script, running in processes.items():
                status_icon = "✅ 运行中" if running else "❌ 未运行"
                status_text += f"**{script}**: {status_icon}\n"
            
            # 添加运行时间信息
            uptime = time.time() - psutil.boot_time()
            days, remainder = divmod(uptime, 86400)
            hours, remainder = divmod(remainder, 3600)
            minutes, seconds = divmod(remainder, 60)
            
            status_text += f"\n**系统运行时间**: {int(days)}天 {int(hours)}小时 {int(minutes)}分钟\n"
            
            # 添加内存使用情况
            memory = psutil.virtual_memory()
            status_text += f"**内存使用率**: {memory.percent}% (总计 {round(memory.total/1024/1024/1024, 2)} GB)\n"
            
            # 添加CPU使用情况
            status_text += f"**CPU使用率**: {psutil.cpu_percent()}%\n"
            
            # 添加磁盘使用情况
            disk = psutil.disk_usage('/')
            status_text += f"**磁盘使用率**: {disk.percent}% (总计 {round(disk.total/1024/1024/1024, 2)} GB)\n"
            
            return status_text
            
        except Exception as e:
            error_msg = f"❌ 获取状态信息失败\n错误: {str(e)}"
            self.logger.error(error_msg)
            self.logger.error(traceback.format_exc())
            return error_msg
    
    async def show_help(self, text: str, sender_id: str, conversation_id: str) -> str:
        """显示帮助信息"""
        help_text = (
            "### 🤖 机器人命令帮助\n\n"
            "可用命令列表：\n\n"
            "- `重启自动化` - 重启自动化脚本\n"
            "- `启动自动化` - 启动自动化脚本\n"
            "- `停止自动化` - 停止自动化脚本\n"
            "- `查看状态` - 检查系统状态\n"
            "- `更换数据:http://example.com/questions.json` - 更新问题数据文件\n"
            "- `帮助` - 显示此帮助信息\n\n"
            "注意：管理员命令需要足够的权限才能执行。"
        )
        
        return help_text
    
    async def start_automation(self, text: str, sender_id: str, conversation_id: str) -> str:
        """启动自动化脚本"""
        try:
            # 检查自动化脚本是否已经在运行
            is_running = False
            tk_script_pid = None
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['cmdline']:
                        cmd_line = ' '.join(proc.info['cmdline'])
                        if 'python' in cmd_line and 'tk_chat_automation.py' in cmd_line:
                            is_running = True
                            tk_script_pid = proc.info['pid']
                            break
                except Exception as e:
                    self.logger.error(f"检查进程时出错: {str(e)}")
            
            if is_running:
                self.logger.info(f"自动化脚本已经在运行中，PID: {tk_script_pid}")
                return f"⚠️ 自动化脚本已经在运行中 (PID: {tk_script_pid})\n\n如需重启，请使用 `重启自动化` 命令。"
            
            # 创建启动标记文件
            with open(START_FLAG_FILE, 'w') as f:
                current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                f.write(f"启动请求时间: {current_time}\n")
                f.write(f"请求者: {sender_id}\n")
            
            # 如果存在暂停标记文件，则删除它以恢复保活脚本的自动重启功能
            if os.path.exists(PAUSE_FLAG_FILE):
                os.remove(PAUSE_FLAG_FILE)
                self.logger.info("已删除暂停标记文件，保活脚本将恢复自动重启功能")
            
            self.logger.info(f"已创建启动标记文件，请求者: {sender_id}")
            
            return f"✅ 启动命令已发送\n\n保活脚本将检测到启动标记并启动自动化脚本。\n\n请求时间: {current_time}\n请求者: {sender_id}"
            
        except Exception as e:
            error_msg = f"❌ 发送启动命令失败\n错误: {str(e)}"
            self.logger.error(error_msg)
            self.logger.error(traceback.format_exc())
            return error_msg
    
    async def stop_automation(self, text: str, sender_id: str, conversation_id: str) -> str:
        """停止自动化脚本"""
        try:
            # 检查自动化脚本是否在运行
            is_running = False
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['cmdline']:
                        cmd_line = ' '.join(proc.info['cmdline'])
                        if 'python' in cmd_line and 'tk_chat_automation.py' in cmd_line:
                            is_running = True
                            break
                except:
                    pass
            
            if not is_running:
                self.logger.info("没有找到运行中的自动化脚本进程")
                return "⚠️ 当前没有运行中的自动化脚本"
            
            # 创建停止标记文件
            with open(STOP_FLAG_FILE, 'w') as f:
                current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                f.write(f"停止请求时间: {current_time}\n")
                f.write(f"请求者: {sender_id}\n")
            
            # 创建暂停标记文件，指示保活脚本暂停自动重启功能
            with open(PAUSE_FLAG_FILE, 'w') as f:
                f.write(f"暂停请求时间: {current_time}\n")
                f.write(f"请求者: {sender_id}\n")
                f.write("此文件存在时，保活脚本将不会自动重启自动化脚本\n")
            
            self.logger.info(f"已创建停止标记文件和暂停标记文件，请求者: {sender_id}")
            
            return f"✅ 停止命令已发送\n\n保活脚本将检测到停止标记并停止自动化脚本。同时，保活脚本将暂停自动重启功能。\n\n请求时间: {current_time}\n请求者: {sender_id}"
                
        except Exception as e:
            error_msg = f"❌ 发送停止命令失败\n错误: {str(e)}"
            self.logger.error(error_msg)
            self.logger.error(traceback.format_exc())
            return error_msg

    async def update_questions_data(self, text: str, sender_id: str, conversation_id: str) -> str:
        """更新问题数据文件"""
        try:
            # 解析URL
            parts = text.split(':', 1)
            if len(parts) != 2:
                return "❌ 命令格式错误\n\n正确格式：`更换数据:http://example.com/questions.json`"
            
            url = parts[1].strip()
            if not url.startswith('http'):
                return "❌ URL格式错误\n\n请提供有效的HTTP或HTTPS URL"
            
            self.logger.info(f"开始下载问题数据文件: {url}")
            
            # 下载文件
            response = requests.get(url, timeout=30)
            if response.status_code != 200:
                return f"❌ 下载失败\n\n服务器返回状态码: {response.status_code}"
            
            # 验证JSON格式
            try:
                data = response.json()
                # 验证必要的字段
                if not all(key in data for key in ['single_questions', 'multi_questions', 'mode']):
                    return "❌ 数据格式错误\n\n缺少必要的字段：single_questions, multi_questions, mode"
            except json.JSONDecodeError:
                return "❌ 下载的文件不是有效的JSON格式"
            
            # 备份当前文件（如果存在）
            backup_path = None
            if os.path.exists("default_questions.json"):
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = f"default_questions.backup.{timestamp}.json"
                try:
                    with open("default_questions.json", 'r', encoding='utf-8') as src, \
                         open(backup_path, 'w', encoding='utf-8') as dst:
                        dst.write(src.read())
                    self.logger.info(f"已备份当前数据文件到: {backup_path}")
                except Exception as e:
                    self.logger.error(f"备份数据文件失败: {str(e)}")
                    return f"❌ 备份当前数据文件失败: {str(e)}"
            
            # 更新数据中的时间戳
            data['timestamp'] = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 保存新文件
            with open("default_questions.json", 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"已成功更新问题数据文件，单问题数量: {len(data['single_questions'])}, 多问题组数量: {len(data['multi_questions'])}")
            
            # 创建标志文件，通知需要在下次启动时重新加载数据
            with open("reload_questions_flag.txt", 'w') as f:
                current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                f.write(f"更新请求时间: {current_time}\n")
                f.write(f"请求者: {sender_id}\n")
                f.write(f"数据源: {url}")
            
            # 构建成功消息
            success_msg = (
                f"### ✅ 问题数据已更新\n\n"
                f"**时间**: {current_time}\n"
                f"**数据源**: {url}\n"
                f"**单问题**: {len(data['single_questions'])} 个\n"
                f"**多问题组**: {len(data['multi_questions'])} 组\n"
                f"**模式**: {data.get('mode', 'sequential')}\n"
            )
            
            if backup_path:
                success_msg += f"**备份**: {backup_path}\n"
            
            success_msg += "\n数据将在脚本下次启动时生效。要立即使用新数据，请使用`重启自动化`命令。"
            
            return success_msg
                
        except Exception as e:
            error_msg = f"❌ 更新问题数据失败\n错误: {str(e)}"
            self.logger.error(error_msg)
            self.logger.error(traceback.format_exc())
            return error_msg

def main():
    logger = setup_logger()
    credential = dingtalk_stream.Credential(DINGTALK_CLIENT_ID, DINGTALK_CLIENT_SECRET)
    client = dingtalk_stream.DingTalkStreamClient(credential)
    client.register_callback_handler(dingtalk_stream.chatbot.ChatbotMessage.TOPIC, CommandHandler(logger))
    client.start_forever()


if __name__ == "__main__":
    main() 