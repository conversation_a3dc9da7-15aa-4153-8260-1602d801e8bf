#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os
import sys
import subprocess
import time
import logging
import datetime
import platform
import psutil
import signal

# 检测操作系统
IS_WINDOWS = platform.system() == "Windows"

# 配置日志
def setup_logging():
    log_dir = os.path.join(os.getcwd(), 'logs')
    os.makedirs(log_dir, exist_ok=True)
    log_file = os.path.join(log_dir, f'stop_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )
    return log_file

# 要停止的脚本列表
SCRIPTS = [
    "tk_chat_automation.py",
    "keepalive.py",
    "dingtalk_stream_server.py"
]

# 创建停止标记文件
def create_stop_flag():
    try:
        with open("stop_flag.txt", 'w') as f:
            current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            f.write(f"停止请求时间: {current_time}\n")
        logging.info("已创建停止标记文件")
        return True
    except Exception as e:
        logging.error(f"创建停止标记文件失败: {str(e)}")
        return False

# 终止进程及其子进程
def terminate_process_tree(process_id):
    try:
        if IS_WINDOWS:
            subprocess.call(['taskkill', '/F', '/T', '/PID', str(process_id)])
            return True
        else:
            try:
                parent = psutil.Process(process_id)
                for child in parent.children(recursive=True):
                    try:
                        child.terminate()
                    except:
                        os.kill(child.pid, signal.SIGKILL)
                parent.terminate()
                return True
            except:
                os.kill(process_id, signal.SIGKILL)
                return True
    except Exception as e:
        logging.error(f"终止进程 {process_id} 失败: {str(e)}")
        return False

# 查找并终止指定脚本的所有进程
def find_and_terminate_processes(script_name):
    terminated = 0
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
            if 'python' in cmdline and script_name in cmdline:
                pid = proc.info['pid']
                logging.info(f"发现 {script_name} 进程，PID: {pid}，正在终止...")
                if terminate_process_tree(pid):
                    terminated += 1
                    logging.info(f"已终止 {script_name} 进程，PID: {pid}")
                else:
                    logging.warning(f"无法终止 {script_name} 进程，PID: {pid}")
        except Exception as e:
            continue
    return terminated

# Unix系统使用kill命令终止所有相关进程
def kill_all_processes_unix():
    try:
        cmd = "pkill -f 'python.*tk_chat_automation.py|python.*keepalive.py|python.*dingtalk_stream_server.py'"
        result = subprocess.run(cmd, shell=True, text=True, capture_output=True)
        if result.returncode != 0 and result.returncode != 1:  # 1可能表示没有找到进程
            logging.warning(f"终止所有进程的命令返回错误: {result.stderr}")
            return False
        return True
    except Exception as e:
        logging.error(f"执行kill命令失败: {str(e)}")
        return False

# 主函数
def main():
    log_file = setup_logging()
    logging.info("停止脚本开始执行...")
    logging.info(f"操作系统: {platform.system()}")
    
    # 创建停止标记文件，通知keepalive.py停止重启tk_chat_automation.py
    create_stop_flag()
    
    # 等待一段时间让所有脚本有机会响应停止标记
    logging.info("等待5秒让所有脚本有机会响应停止标记...")
    time.sleep(5)
    
    # 在Unix系统上，使用pkill命令终止所有相关进程
    if not IS_WINDOWS:
        logging.info("在Unix系统上使用pkill命令终止所有相关进程...")
        if kill_all_processes_unix():
            logging.info("已使用pkill命令终止所有相关进程")
        else:
            logging.warning("使用pkill命令终止进程可能不完全，将继续使用Python方法终止进程")
    
    # 逐个脚本终止进程
    total_terminated = 0
    for script in SCRIPTS:
        logging.info(f"正在终止 {script} 的所有实例...")
        terminated = find_and_terminate_processes(script)
        total_terminated += terminated
        logging.info(f"已终止 {script} 的 {terminated} 个实例")
    
    # 检查是否有进程遗漏
    for script in SCRIPTS:
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                if 'python' in cmdline and script in cmdline:
                    pid = proc.info['pid']
                    logging.warning(f"发现未终止的 {script} 进程，PID: {pid}，尝试强制终止...")
                    try:
                        if IS_WINDOWS:
                            subprocess.call(['taskkill', '/F', '/PID', str(pid)])
                        else:
                            os.kill(pid, signal.SIGKILL)
                        logging.info(f"已强制终止 {script} 进程，PID: {pid}")
                        total_terminated += 1
                    except:
                        logging.error(f"无法强制终止 {script} 进程，PID: {pid}")
            except:
                continue
    
    logging.info(f"停止脚本执行完成，共终止 {total_terminated} 个进程")
    
    # 检查进程是否都已终止
    any_running = False
    for script in SCRIPTS:
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                if 'python' in cmdline and script in cmdline:
                    any_running = True
                    pid = proc.info['pid']
                    logging.warning(f"仍有 {script} 进程在运行，PID: {pid}")
            except:
                continue
    
    if any_running:
        logging.warning("某些进程可能未能成功终止")
        print("警告：某些进程可能未能成功终止，请查看日志了解详情。")
    else:
        logging.info("所有进程已终止")
        print("所有进程已成功终止！")
    
    # 提示
    if not IS_WINDOWS:
        print("\n如果仍有进程未终止，请手动运行以下命令进行终止：")
        print("pkill -9 -f 'python.*tk_chat_automation.py|python.*keepalive.py|python.*dingtalk_stream_server.py'")

if __name__ == "__main__":
    main() 