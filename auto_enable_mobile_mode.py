#!/usr/bin/env python3
"""
自动启用Chrome移动设备模拟模式
通过模拟键盘快捷键来自动打开开发者工具并切换到移动设备模拟模式
"""

import time
import subprocess
import sys

def enable_mobile_mode_macos():
    """在macOS上自动启用移动设备模拟模式"""
    try:
        # 使用AppleScript来模拟键盘操作
        applescript = '''
        tell application "System Events"
            -- 等待Chrome窗口激活
            delay 2
            
            -- 遍历所有Chrome窗口
            tell application "Google Chrome" to activate
            delay 1
            
            -- 打开开发者工具 (Cmd+Option+I)
            key code 34 using {command down, option down}
            delay 2
            
            -- 切换到设备模拟模式 (Cmd+Shift+M)
            key code 46 using {command down, shift down}
            delay 1
            
        end tell
        '''
        
        # 执行AppleScript
        subprocess.run(['osascript', '-e', applescript], check=True)
        print("✓ 已自动启用移动设备模拟模式")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"✗ 自动启用失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 发生错误: {e}")
        return False

def enable_mobile_mode_linux():
    """在Linux上自动启用移动设备模拟模式"""
    try:
        # 使用xdotool来模拟键盘操作
        # 首先检查是否安装了xdotool
        subprocess.run(['which', 'xdotool'], check=True, capture_output=True)
        
        # 等待窗口激活
        time.sleep(2)
        
        # 打开开发者工具 (Ctrl+Shift+I)
        subprocess.run(['xdotool', 'key', 'ctrl+shift+i'], check=True)
        time.sleep(2)
        
        # 切换到设备模拟模式 (Ctrl+Shift+M)
        subprocess.run(['xdotool', 'key', 'ctrl+shift+m'], check=True)
        time.sleep(1)
        
        print("✓ 已自动启用移动设备模拟模式")
        return True
        
    except subprocess.CalledProcessError:
        print("✗ 需要安装xdotool: sudo apt-get install xdotool")
        return False
    except Exception as e:
        print(f"✗ 发生错误: {e}")
        return False

def main():
    """主函数"""
    print("=== Chrome移动设备模拟自动启用工具 ===")
    
    # 检测操作系统
    import platform
    system = platform.system().lower()
    
    if system == 'darwin':  # macOS
        print("检测到macOS系统，使用AppleScript自动化...")
        success = enable_mobile_mode_macos()
    elif system == 'linux':  # Linux
        print("检测到Linux系统，使用xdotool自动化...")
        success = enable_mobile_mode_linux()
    else:
        print(f"✗ 不支持的操作系统: {system}")
        print("请手动启用移动设备模拟模式:")
        print("1. 按 F12 或 Ctrl+Shift+I 打开开发者工具")
        print("2. 按 Ctrl+Shift+M 切换到设备模拟模式")
        success = False
    
    if success:
        print("\n🎉 移动设备模拟模式已启用！")
        print("现在可以在开发者工具中选择具体的设备类型")
    else:
        print("\n❌ 自动启用失败，请手动操作:")
        print("1. 在Chrome中按 F12 打开开发者工具")
        print("2. 点击设备图标或按 Ctrl+Shift+M (macOS: Cmd+Shift+M)")
        print("3. 选择对应的移动设备类型")

if __name__ == "__main__":
    main()
