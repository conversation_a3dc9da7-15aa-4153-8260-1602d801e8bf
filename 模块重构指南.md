# TK聊天自动化模块重构指南

## 重构概述

原始的 `tk_chat_automation.py` 文件（5460行）已成功重构为模块化架构，拆分成多个功能明确的模块，大大降低了单个文件的代码量，提高了代码的可维护性和可读性。

## 模块结构

### 重构后的目录结构

```
modules/
├── __init__.py                 # 包初始化文件
├── ai_service.py              # AI服务模块（问题生成）
├── ai_evaluation.py           # AI评估模块（回答评估）
├── dingtalk_notifier.py       # 钉钉通知模块
├── logging_utils.py           # 日志工具模块
├── oss_uploader.py            # OSS文件上传模块
└── question_manager.py        # 问题管理模块
```

## 核心模块说明

### 1. AI服务模块 (ai_service.py)

**功能**: AI配置管理和问题生成
- `load_ai_config()` - 加载AI配置文件
- `get_ai_api_config()` - 获取API配置信息
- `generate_ai_questions()` - 使用AI生成问题变种
- `call_ollama_api()` - 调用Ollama API
- `call_openai_api()` - 调用OpenAI API
- `parse_generated_questions()` - 解析生成的问题

### 2. AI评估模块 (ai_evaluation.py)

**功能**: AI回答质量评估
- `evaluate_ai_response()` - 评测AI回答质量
- `build_evaluation_prompt()` - 构建评测提示词
- `parse_evaluation_response()` - 解析评测结果
- 支持多维度评分（理解力、专业性、情商话术等）

### 3. 钉钉通知模块 (dingtalk_notifier.py)

**功能**: 钉钉消息推送
- `send_dingtalk_notification()` - 发送钉钉通知
- 支持文本、图片、@用户等多种消息类型
- 支持禁用通知功能

### 4. OSS上传模块 (oss_uploader.py)

**功能**: 阿里云OSS文件上传
- `upload_image_to_oss()` - 上传图片或文本文件
- `upload_text_to_oss()` - 上传文本文件
- `upload_binary_to_oss()` - 上传二进制内容
- `convert_txt_to_html()` - 文本转HTML格式
- `ensure_oss_dir()` - 确保OSS目录存在

### 5. 问题管理模块 (question_manager.py)

**功能**: 问题加载和管理
- `load_questions()` - 加载问题语料（支持新旧格式）
- `get_next_questions()` - 选择下一组问题
- `get_next_question_unified()` - 从统一格式中选择问题
- `process_ai_generated_question()` - 处理AI生成问题
- `process_ai_followup_question()` - 处理AI追问对话

### 6. 日志工具模块 (logging_utils.py)

**功能**: 日志系统管理
- `setup_logging()` - 配置日志系统
- `find_latest_log_file()` - 查找最新日志文件

## 使用方式

### 方式一：直接导入模块

```python
from modules import ai_service, oss_uploader, dingtalk_notifier

# 使用AI服务
config = ai_service.load_ai_config()
questions = ai_service.generate_ai_questions("时尚", "否定意图", "我不喜欢红色", 3)

# 上传文件
url = oss_uploader.upload_image_to_oss("screenshot.png")

# 发送通知
dingtalk_notifier.send_dingtalk_notification(webhook_url, "测试消息")
```

### 方式二：从包导入函数

```python
from modules import (
    load_ai_config,
    generate_ai_questions,
    upload_image_to_oss,
    send_dingtalk_notification,
    setup_logging
)

# 设置日志
setup_logging()

# 直接使用函数
config = load_ai_config()
questions = generate_ai_questions("时尚", "否定意图", "我不喜欢红色", 3)
```

### 方式三：整体导入（兼容原有代码）

```python
import modules

# 使用模块函数
config = modules.load_ai_config()
url = modules.upload_image_to_oss("test.png")
```

## 重构优势

1. **代码组织**: 将5460行单一文件拆分为多个专门模块，每个模块职责明确
2. **可维护性**: 模块化结构便于独立开发、测试和维护
3. **可重用性**: 各模块可独立使用，便于在其他项目中复用
4. **可扩展性**: 新功能可以独立成模块，不影响现有代码
5. **团队协作**: 不同开发者可以专注于不同模块的开发

## 注意事项

1. **导入依赖**: 确保相对导入路径正确（如ai_evaluation.py中的`from .ai_service import load_ai_config`）
2. **配置文件**: 各模块共享同一个配置文件（ai_config.json等）
3. **日志系统**: 所有模块使用统一的日志系统
4. **错误处理**: 保持原有的错误处理逻辑不变

## 未来扩展

基于模块化架构，可以轻松添加新功能模块：
- 统计分析模块
- 报告生成模块  
- 浏览器自动化模块
- 图像处理模块
- 数据库操作模块

## 迁移建议

1. **逐步迁移**: 可以逐个模块进行迁移测试，确保功能正常
2. **保留备份**: 保留原始文件作为参考
3. **测试验证**: 重点测试模块间的交互和依赖关系
4. **性能监控**: 关注模块化后的性能表现 