# 太古聊天机器人自动化服务已启动

以下服务正在运行：

- 钉钉Stream服务器 (dingtalk_stream_server.py)
- 保活脚本 (keepalive.py)

要查看各服务的日志，请在终端中运行以下命令：

- 钉钉Stream服务日志: tail -f logs/dingtalk_stream_*.log
- 保活脚本日志: tail -f logs/keepalive_*.log
- 自动化脚本日志: tail -f logs/chat_log_*.log

如需重启服务，请运行：

1. 停止所有服务: pkill -f 'python.*tk_chat_automation.py|python.*keepalive.py|python.*dingtalk_stream_server.py'
2. 重新启动: python3 start.py

最后更新时间: 2025-05-20 17:55:06