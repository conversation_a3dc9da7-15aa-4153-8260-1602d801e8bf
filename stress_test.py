# -*- coding: utf-8 -*-
"""
TikaBot聊天自动化压力测试脚本
基于tk_chat_automation.py，支持多个浏览器实例并发测试
"""
import time
import sys
import os
import json
import logging
import datetime
import requests
import traceback
import subprocess
import threading
import argparse
import multiprocessing
from concurrent.futures import ThreadPoolExecutor, as_completed
import psutil
import signal
import shutil
from pathlib import Path
import random
import urllib.parse

def calculate_window_position(process_id, concurrent_count):
    """
    为进程计算浏览器窗口位置，避免窗口重叠
    
    Args:
        process_id: 进程ID (1开始)
        concurrent_count: 总并发数
        
    Returns:
        tuple: (x, y) 窗口位置坐标
    """
    # 尝试检测实际屏幕尺寸，如果失败则使用保守的默认值
    try:
        import subprocess
        import platform
        
        if platform.system() == 'Darwin':  # macOS
            result = subprocess.run(['system_profiler', 'SPDisplaysDataType'], 
                                  capture_output=True, text=True, timeout=5)
            # 这里可以解析输出获取屏幕尺寸，但为了简单使用默认值
            pass
    except:
        pass
    
    # 使用用户实际屏幕尺寸的保守估计
    screen_width = 2800   # 基于用户的3024宽度，留些边距
    screen_height = 1800  # 基于用户的1964高度，留些边距
    
    # 使用适中的浏览器窗口尺寸，留足够间距
    window_width = 450    # 稍微增加窗口宽度适应实际需要
    window_height = 700   # 增加窗口高度
    
    # 添加边距
    margin = 30  # 增加边距确保安全距离
    gap = 100    # 窗口之间的额外间距
    
    # 计算每行能放多少个窗口，使用更大的间距 
    available_width = screen_width - 2 * margin
    window_spacing = window_width + gap  # 窗口宽度 + 间距
    windows_per_row = max(1, available_width // window_spacing)
    
    # 计算当前进程的行和列
    row = (process_id - 1) // windows_per_row
    col = (process_id - 1) % windows_per_row
    
    # 计算实际位置
    x = margin + col * window_spacing
    y = margin + row * (window_height + gap + 80)  # 垂直间距
    
    # 如果并发数较多，使用更紧凑的布局
    if concurrent_count > 8:  # 提高阈值
        # 使用更小的窗口和更少的边距
        window_width = 380
        window_height = 550
        margin = 20
        gap = 60  # 紧凑模式下的间距
        
        # 重新计算
        available_width = screen_width - 2 * margin
        window_spacing = window_width + gap
        windows_per_row = max(1, available_width // window_spacing)
        
        row = (process_id - 1) // windows_per_row
        col = (process_id - 1) % windows_per_row
        
        x = margin + col * window_spacing
        y = margin + row * (window_height + gap + 50)
    
    # 确保不会超出屏幕边界
    max_x = screen_width - window_width - margin
    max_y = screen_height - window_height - margin
    
    x = min(x, max_x)
    y = min(y, max_y)
    
    # 如果计算出的位置仍然可能重叠，添加偏移
    if concurrent_count > 10:
        offset_x = (process_id - 1) * 25 % 100
        offset_y = (process_id - 1) * 20 % 80
        x = (x + offset_x) % max(max_x, 100)
        y = (y + offset_y) % max(max_y, 100)
    
    # 记录调试信息
    logging.info(f"进程 {process_id}: 计算窗口位置 x={x}, y={y} (并发数={concurrent_count}, 每行={windows_per_row}个窗口, 行={row}, 列={col})")
    
    return int(x), int(y)

def test_window_positions(concurrent_count):
    """测试窗口位置计算，显示各个进程的窗口位置"""
    print(f"\n=== 窗口位置计算测试 (并发数: {concurrent_count}) ===")
    print(f"{'进程ID':<8} {'X坐标':<8} {'Y坐标':<8} {'说明'}")
    print("-" * 50)
    
    for process_id in range(1, concurrent_count + 1):
        x, y = calculate_window_position(process_id, concurrent_count)
        print(f"{process_id:<8} {x:<8} {y:<8} 浏览器窗口位置")
    
    print(f"\n注意: 以上坐标基于屏幕尺寸 1440x900 的假设")
    print("如果您的屏幕尺寸不同，请检查浏览器窗口是否重叠")
    print("建议在实际压测前使用此命令验证窗口位置")

# 全局变量
stress_test_stats = {
    'total_processes': 0,
    'running_processes': 0,
    'completed_processes': 0,
    'failed_processes': 0,
    'start_time': None,
    'lock': threading.Lock()
}

# 循环压测全局配置
loop_test_config = {
    'enabled': False,           # 是否启用循环压测
    'restart_delay': 10,        # 重启间隔（秒）
    'max_retries': -1,          # 最大重试次数（-1表示无限）
    'running': False,           # 是否正在运行
    'processes': {},            # 进程状态跟踪
    'stop_event': threading.Event(),  # 停止事件
    'supervisor_thread': None,   # 监督线程
    'lock': threading.Lock()
}

def load_tickets_config(config_file='tickets_config.json'):
    """加载ticket配置文件"""
    try:
        if not os.path.exists(config_file):
            logging.warning(f"票证配置文件不存在: {config_file}")
            return None
            
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
            
        logging.info(f"加载了 {len(config.get('tickets', []))} 个ticket")
        return config
        
    except Exception as e:
        logging.error(f"加载ticket配置文件失败: {str(e)}")
        return None

def get_ticket_for_process(process_id, tickets_config):
    """为指定进程获取ticket"""
    if not tickets_config or 'tickets' not in tickets_config:
        logging.warning(f"进程 {process_id}: 无可用ticket配置")
        return None
        
    tickets = tickets_config['tickets']
    if not tickets:
        logging.warning(f"进程 {process_id}: ticket列表为空")
        return None
        
    # 循环分配ticket，确保每个进程获得不同的ticket
    ticket_index = (process_id - 1) % len(tickets)
    selected_ticket = tickets[ticket_index]
    
    logging.info(f"进程 {process_id}: 分配ticket {selected_ticket} (索引: {ticket_index})")
    return selected_ticket

def build_url_with_ticket(tickets_config, ticket):
    """构建包含ticket的完整URL"""
    if not tickets_config:
        return "https://taikoo-li.gamenow.club/?ticket=61653113-3c5f-4bdb-9f73-3be15272d1as&mallCode=TKH&devtool=false"
        
    base_url = tickets_config.get('base_url', 'https://taikoo-li.gamenow.club/')
    mall_code = tickets_config.get('mall_code', 'TKH')
    
    # 构建URL参数
    params = {
        'ticket': ticket,
        'mallCode': mall_code,
        'devtool': 'false'
    }
    
    # 构建完整URL
    url = base_url + '?' + urllib.parse.urlencode(params)
    return url

class ProcessManager:
    """进程管理器，负责进程的启动、监控和重启"""
    
    def __init__(self, process_id, cmd, process_dir, env, webhook_url=None, assigned_ticket=None):
        self.process_id = process_id
        self.cmd = cmd
        self.process_dir = process_dir
        self.env = env
        self.webhook_url = webhook_url
        self.assigned_ticket = assigned_ticket
        self.process = None
        self.start_time = None
        self.end_time = None
        self.retry_count = 0
        self.total_runs = 0
        self.is_running = False
        
    def start_process(self):
        """启动进程"""
        try:
            self.start_time = datetime.datetime.now()
            self.is_running = True
            
            logging.info(f"进程 {self.process_id}: 第{self.total_runs + 1}次启动 (重试次数: {self.retry_count})")
            
            self.process = subprocess.Popen(
                self.cmd,
                cwd=self.process_dir,
                env=self.env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.total_runs += 1
            
            # 发送启动通知
            if self.webhook_url:
                message = (
                    f"🚀 **进程 {self.process_id} 启动**\n"
                    f"**第**: {self.total_runs} 次运行\n"
                    f"**重试次数**: {self.retry_count}\n"
                    f"**启动时间**: {self.start_time.strftime('%H:%M:%S')}\n"
                    f"**分配ticket**: `{self.assigned_ticket or '无'}`"
                )
                send_stress_test_notification(self.webhook_url, message, f"进程{self.process_id}")
            
            return True
        except Exception as e:
            logging.error(f"进程 {self.process_id}: 启动失败 - {str(e)}")
            self.is_running = False
            return False
    
    def check_status(self):
        """检查进程状态"""
        if not self.process:
            return False, None
            
        poll_result = self.process.poll()
        if poll_result is not None:
            # 进程已结束
            self.end_time = datetime.datetime.now()
            self.is_running = False
            
            # 获取输出
            try:
                stdout, stderr = self.process.communicate(timeout=5)
            except subprocess.TimeoutExpired:
                stdout, stderr = "", "进程输出获取超时"
            
            duration = (self.end_time - self.start_time).total_seconds()
            
            if poll_result == 0:
                logging.info(f"进程 {self.process_id}: 正常完成, 耗时 {duration:.2f}秒")
                # 发送完成通知
                if self.webhook_url:
                    message = (
                        f"✅ **进程 {self.process_id} 完成**\n"
                        f"**第**: {self.total_runs} 次运行\n"
                        f"**耗时**: {duration:.2f}秒\n"
                        f"**开始**: {self.start_time.strftime('%H:%M:%S')}\n"
                        f"**结束**: {self.end_time.strftime('%H:%M:%S')}"
                    )
                    send_stress_test_notification(self.webhook_url, message, f"进程{self.process_id}")
            else:
                logging.error(f"进程 {self.process_id}: 异常退出, 返回码 {poll_result}, 耗时 {duration:.2f}秒")
                logging.error(f"进程 {self.process_id}: 错误输出: {stderr[:500]}")
                self.retry_count += 1
                
                # 发送失败通知
                if self.webhook_url:
                    message = (
                        f"❌ **进程 {self.process_id} 异常退出**\n"
                        f"**返回码**: {poll_result}\n"
                        f"**第**: {self.total_runs} 次运行\n"
                        f"**重试次数**: {self.retry_count}\n"
                        f"**耗时**: {duration:.2f}秒\n"
                        f"**错误**: ```{stderr[:200]}```" if stderr else "**错误**: 无详细信息"
                    )
                    send_stress_test_notification(self.webhook_url, message, f"进程{self.process_id}")
            
            return False, poll_result
        
        return True, None  # 进程仍在运行
    
    def stop_process(self):
        """停止进程"""
        if self.process and self.process.poll() is None:
            try:
                self.process.terminate()
                # 等待5秒让进程优雅退出
                try:
                    self.process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    # 强制杀死进程
                    self.process.kill()
                    self.process.wait()
                logging.info(f"进程 {self.process_id}: 已停止")
            except Exception as e:
                logging.error(f"进程 {self.process_id}: 停止失败 - {str(e)}")
        
        self.is_running = False

def process_supervisor(concurrent_count, questions_file, access_mode, test_mode, webhook_url, tickets_config, force_text_mode=False, startup_interval=30):
    """进程监督器，负责监控和重启进程"""
    
    logging.info(f"启动进程监督器，管理 {concurrent_count} 个进程")
    
    # 初始化进程管理器
    process_managers = {}
    
    for process_id in range(1, concurrent_count + 1):
        # 创建进程工作空间
        process_dir = create_process_workspace(process_id, base_dir=None, tickets_config=tickets_config, custom_questions_file=questions_file)
        
        # 获取分配的ticket
        assigned_ticket = None
        if tickets_config:
            assigned_ticket = get_ticket_for_process(process_id, tickets_config)
        
        # 构建运行命令
        script_path = os.path.join(os.getcwd(), 'tk_chat_automation.py')
        cmd = [sys.executable, script_path]
        
        if test_mode:
            cmd.append('--test-mode')
        if force_text_mode:
            cmd.append('--force-text-mode')
        if questions_file:
            local_questions_file = os.path.join(process_dir, os.path.basename(questions_file))
            if os.path.exists(local_questions_file):
                cmd.extend(['--questions-file', local_questions_file])
        if access_mode:
            cmd.extend(['--access-mode', access_mode])
        
        # 设置环境变量
        env = os.environ.copy()
        env['STRESS_TEST_MODE'] = 'true'
        env['STRESS_PROCESS_ID'] = str(process_id)
        
        # 计算并设置窗口位置
        window_x, window_y = calculate_window_position(process_id, concurrent_count)
        env['BROWSER_WINDOW_X'] = str(window_x)
        env['BROWSER_WINDOW_Y'] = str(window_y)
        
        if assigned_ticket:
            env['ASSIGNED_TICKET'] = assigned_ticket
            if tickets_config:
                env['TARGET_URL'] = build_url_with_ticket(tickets_config, assigned_ticket)
        
        # 创建进程管理器
        process_managers[process_id] = ProcessManager(
            process_id, cmd, process_dir, env, webhook_url, assigned_ticket
        )
    
    # 逐个启动进程，按照启动间隔
    logging.info(f"开始逐个启动进程，启动间隔: {startup_interval}秒")
    
    started_processes = []
    
    def startup_processes():
        """启动进程的线程函数"""
        for process_id in range(1, concurrent_count + 1):
            if loop_test_config['stop_event'].is_set():
                break
            
            manager = process_managers[process_id]
            logging.info(f"启动进程 {process_id}/{concurrent_count}")
            manager.start_process()
            started_processes.append(process_id)
            
            # 等待启动间隔，除非是最后一个进程
            if process_id < concurrent_count:
                for _ in range(startup_interval):
                    if loop_test_config['stop_event'].is_set():
                        break
                    time.sleep(1)
    
    # 在后台线程中启动进程
    startup_thread = threading.Thread(target=startup_processes, daemon=True)
    startup_thread.start()
    
    # 监督循环
    check_interval = 5  # 检查间隔（秒）
    
    while not loop_test_config['stop_event'].is_set():
        try:
            time.sleep(check_interval)
            
            # 只监控已经启动的进程
            for process_id in started_processes:
                if loop_test_config['stop_event'].is_set():
                    break
                
                manager = process_managers[process_id]
                
                # 检查进程状态
                is_running, return_code = manager.check_status()
                
                if not is_running:
                    # 进程已结束，判断是否需要重启
                    max_retries = loop_test_config['max_retries']
                    if max_retries == -1 or manager.retry_count < max_retries:
                        # 等待重启延迟
                        restart_delay = loop_test_config['restart_delay']
                        logging.info(f"进程 {process_id}: {restart_delay}秒后重启...")
                        
                        # 分段等待，以便快速响应停止信号
                        for _ in range(restart_delay):
                            if loop_test_config['stop_event'].is_set():
                                break
                            time.sleep(1)
                        
                        if not loop_test_config['stop_event'].is_set():
                            manager.start_process()
                    else:
                        logging.warning(f"进程 {process_id}: 达到最大重试次数 ({max_retries})，停止重启")
                        # 发送达到重试限制通知
                        if webhook_url:
                            message = (
                                f"⚠️ **进程 {process_id} 达到重试限制**\n"
                                f"**最大重试次数**: {max_retries}\n"
                                f"**总运行次数**: {manager.total_runs}\n"
                                f"**停止重启**"
                            )
                            send_stress_test_notification(webhook_url, message, f"进程{process_id}")
        
        except Exception as e:
            logging.error(f"监督器异常: {str(e)}")
            logging.error(traceback.format_exc())
    
    # 停止所有进程
    logging.info("正在停止所有进程...")
    for manager in process_managers.values():
        manager.stop_process()
    
    logging.info("进程监督器已停止")

def start_loop_stress_test(concurrent_count, questions_file=None, access_mode=None, 
                          test_mode=False, webhook_url=None, tickets_config=None,
                          restart_delay=10, max_retries=-1, force_text_mode=False, startup_interval=30):
    """启动循环压力测试"""
    
    with loop_test_config['lock']:
        if loop_test_config['running']:
            logging.error("循环压力测试已在运行中")
            return False
        
        loop_test_config['enabled'] = True
        loop_test_config['running'] = True
        loop_test_config['restart_delay'] = restart_delay
        loop_test_config['max_retries'] = max_retries
        loop_test_config['stop_event'].clear()
    
    try:
        # 发送启动通知
        if webhook_url:
            message = (
                f"🎯 **循环压力测试启动**\n"
                f"**并发进程数**: {concurrent_count}\n"
                f"**启动间隔**: {startup_interval}秒\n"
                f"**重启延迟**: {restart_delay}秒\n"
                f"**最大重试**: {'无限' if max_retries == -1 else max_retries}\n"
                f"**启动时间**: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                f"**模式**: 循环保活模式"
            )
            send_stress_test_notification(webhook_url, message, "循环压测")
        
        # 启动监督线程
        supervisor_thread = threading.Thread(
            target=process_supervisor,
            args=(concurrent_count, questions_file, access_mode, test_mode, webhook_url, tickets_config, force_text_mode, startup_interval),
            daemon=True
        )
        
        loop_test_config['supervisor_thread'] = supervisor_thread
        supervisor_thread.start()
        
        logging.info("循环压力测试已启动，按 Ctrl+C 停止")
        
        # 主线程等待停止信号
        try:
            while loop_test_config['running']:
                time.sleep(1)
        except KeyboardInterrupt:
            logging.info("收到停止信号...")
        
        return True
        
    except Exception as e:
        logging.error(f"启动循环压力测试失败: {str(e)}")
        return False

def stop_loop_stress_test():
    """停止循环压力测试"""
    
    with loop_test_config['lock']:
        if not loop_test_config['running']:
            logging.info("循环压力测试未在运行")
            return
        
        logging.info("正在停止循环压力测试...")
        loop_test_config['stop_event'].set()
        loop_test_config['running'] = False
    
    # 等待监督线程结束
    if loop_test_config['supervisor_thread']:
        loop_test_config['supervisor_thread'].join(timeout=30)
    
    logging.info("循环压力测试已停止")

def create_process_ticket_config(process_dir, tickets_config, ticket):
    """为进程创建专用的ticket配置文件"""
    try:
        process_config = {
            'base_url': tickets_config.get('base_url', 'https://taikoo-li.gamenow.club/'),
            'mall_code': tickets_config.get('mall_code', 'TKH'),
            'devtool': 'false',
            'assigned_ticket': ticket,
            'full_url': build_url_with_ticket(tickets_config, ticket)
        }
        
        config_file = os.path.join(process_dir, 'process_ticket_config.json')
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(process_config, f, ensure_ascii=False, indent=2)
            
        logging.info(f"创建进程ticket配置: {config_file}")
        return config_file
        
    except Exception as e:
        logging.error(f"创建进程ticket配置失败: {str(e)}")
        return None

def setup_stress_logging():
    """设置压力测试日志"""
    log_dir = os.path.join(os.getcwd(), 'stress_logs')
    os.makedirs(log_dir, exist_ok=True)
    log_file = os.path.join(log_dir, f'stress_test_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - [STRESS] %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return log_file

def send_stress_test_notification(webhook_url, message, test_id=None):
    """发送压力测试专用钉钉通知"""
    if not webhook_url:
        return
        
    try:
        # 添加压力测试标识
        stress_message = f"🔥 **压力测试** {test_id or ''}\n\n{message}"
        
        payload = {
            "msgtype": "markdown",
            "markdown": {
                "title": f"压力测试通知 {test_id or ''}",
                "text": stress_message
            }
        }
        
        response = requests.post(webhook_url, json=payload, timeout=10)
        if response.status_code == 200:
            logging.info(f"压力测试通知发送成功: {test_id}")
        else:
            logging.error(f"压力测试通知发送失败: {response.status_code}")
            
    except Exception as e:
        logging.error(f"发送压力测试通知异常: {str(e)}")

def collect_process_logs(process_id, process_dir):
    """
    收集进程的日志文件到主目录
    
    Args:
        process_id: 进程ID
        process_dir: 进程工作目录
    """
    import shutil
    import glob
    
    try:
        # 主目录的日志目录
        main_logs_dir = os.path.join(os.getcwd(), 'logs')
        os.makedirs(main_logs_dir, exist_ok=True)
        
        # 进程日志目录
        process_logs_dir = os.path.join(process_dir, 'logs')
        
        if os.path.exists(process_logs_dir):
            # 查找进程目录中的所有日志文件
            log_files = glob.glob(os.path.join(process_logs_dir, '*.log'))
            
            for log_file in log_files:
                # 获取文件名并添加进程ID前缀
                file_name = os.path.basename(log_file)
                name_parts = file_name.split('_', 1)
                if len(name_parts) >= 2:
                    new_name = f"process{process_id}_{name_parts[1]}"
                else:
                    new_name = f"process{process_id}_{file_name}"
                
                # 复制到主目录
                dest_path = os.path.join(main_logs_dir, new_name)
                shutil.copy2(log_file, dest_path)
                logging.info(f"进程 {process_id}: 已收集日志文件 {file_name} -> {new_name}")
        
        # 同时收集其他重要文件（如果存在）
        important_files = ['results/*.txt', 'results/*.html', 'results/*.xlsx']
        main_results_dir = os.path.join(os.getcwd(), 'stress_results')
        os.makedirs(main_results_dir, exist_ok=True)
        
        for pattern in important_files:
            files = glob.glob(os.path.join(process_dir, pattern))
            for file_path in files:
                file_name = os.path.basename(file_path)
                name_parts = os.path.splitext(file_name)
                new_name = f"process{process_id}_{name_parts[0]}{name_parts[1]}"
                dest_path = os.path.join(main_results_dir, new_name)
                shutil.copy2(file_path, dest_path)
                logging.info(f"进程 {process_id}: 已收集结果文件 {file_name} -> {new_name}")
        
    except Exception as e:
        logging.error(f"进程 {process_id}: 收集日志时出错 - {str(e)}")
        raise

def create_process_workspace(process_id, base_dir=None, tickets_config=None, custom_questions_file=None):
    """为每个进程创建独立的工作空间"""
    if base_dir is None:
        base_dir = os.getcwd()
        
    process_dir = os.path.join(base_dir, f"stress_process_{process_id}")
    
    # 创建进程目录
    os.makedirs(process_dir, exist_ok=True)
    
    # 创建必要的子目录
    subdirs = ['screenshots', 'results', 'logs', 'console_logs', 'network_logs']
    for subdir in subdirs:
        os.makedirs(os.path.join(process_dir, subdir), exist_ok=True)
    
    # 复制默认问题文件到进程目录(如果存在)
    questions_files = ['questions.json', 'questions.txt', 'single_questions.txt', 'multi_questions.txt', 'default_questions_backup.json', 'test_questions.json']
    for qfile in questions_files:
        src_path = os.path.join(base_dir, qfile)
        if os.path.exists(src_path):
            dst_path = os.path.join(process_dir, qfile)
            shutil.copy2(src_path, dst_path)
    
    # 复制用户指定的问题文件(如果存在)
    if custom_questions_file and os.path.exists(custom_questions_file):
        dst_path = os.path.join(process_dir, os.path.basename(custom_questions_file))
        shutil.copy2(custom_questions_file, dst_path)
        logging.info(f"进程 {process_id}: 已复制自定义问题文件: {custom_questions_file}")
    
    # 复制其他配置文件
    config_files = ['ai_config.json', 'tickets_config.json']
    for config_file in config_files:
        src_path = os.path.join(base_dir, config_file)
        if os.path.exists(src_path):
            dst_path = os.path.join(process_dir, config_file)
            shutil.copy2(src_path, dst_path)
    
    # 为进程分配ticket并创建配置
    if tickets_config:
        ticket = get_ticket_for_process(process_id, tickets_config)
        if ticket:
            ticket_config_file = create_process_ticket_config(process_dir, tickets_config, ticket)
            logging.info(f"进程 {process_id}: ticket配置已创建")
        else:
            logging.warning(f"进程 {process_id}: 无法分配ticket")
    else:
        logging.warning(f"进程 {process_id}: 未提供ticket配置")
    
    return process_dir

def run_single_automation_process(process_id, questions_file=None, access_mode=None, test_mode=False, webhook_url=None, tickets_config=None, force_text_mode=False, concurrent_count=1):
    """运行单个聊天自动化进程"""
    start_time = datetime.datetime.now()
    process_dir = None
    assigned_ticket = None
    
    try:
        # 更新统计信息
        with stress_test_stats['lock']:
            stress_test_stats['running_processes'] += 1
            
        logging.info(f"启动进程 {process_id}: 开始聊天自动化")
        
        # 创建进程工作空间(包含ticket分配)
        process_dir = create_process_workspace(process_id, None, tickets_config, questions_file)
        
        # 获取分配的ticket
        if tickets_config:
            assigned_ticket = get_ticket_for_process(process_id, tickets_config)
            if assigned_ticket:
                logging.info(f"进程 {process_id}: 使用ticket {assigned_ticket}")
            else:
                logging.warning(f"进程 {process_id}: 未能获取ticket，将使用默认配置")
        
        # 构建运行命令
        script_path = os.path.join(os.getcwd(), 'tk_chat_automation.py')
        cmd = [sys.executable, script_path]
        
        if test_mode:
            cmd.append('--test-mode')
        if force_text_mode:
            cmd.append('--force-text-mode')
        if questions_file:
            # 使用进程目录中的问题文件
            local_questions_file = os.path.join(process_dir, os.path.basename(questions_file))
            if os.path.exists(local_questions_file):
                cmd.extend(['--questions-file', local_questions_file])
        if access_mode:
            cmd.extend(['--access-mode', access_mode])
            
        # 在进程目录中运行
        env = os.environ.copy()
        env['STRESS_TEST_MODE'] = 'true'
        env['STRESS_PROCESS_ID'] = str(process_id)
        
        # 计算并设置窗口位置
        window_x, window_y = calculate_window_position(process_id, concurrent_count)
        env['BROWSER_WINDOW_X'] = str(window_x)
        env['BROWSER_WINDOW_Y'] = str(window_y)
        
        # 如果有分配的ticket，设置到环境变量中
        if assigned_ticket:
            env['ASSIGNED_TICKET'] = assigned_ticket
            if tickets_config:
                env['TARGET_URL'] = build_url_with_ticket(tickets_config, assigned_ticket)
        
        logging.info(f"进程 {process_id}: 执行命令 {' '.join(cmd)}")
        logging.info(f"进程 {process_id}: 工作目录 {process_dir}")
        logging.info(f"进程 {process_id}: 窗口位置 ({window_x}, {window_y})")
        if assigned_ticket:
            logging.info(f"进程 {process_id}: 分配的ticket {assigned_ticket}")
        
        # 启动子进程
        process = subprocess.Popen(
            cmd,
            cwd=process_dir,
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待进程完成
        stdout, stderr = process.communicate()
        return_code = process.returncode
        
        end_time = datetime.datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        if return_code == 0:
            logging.info(f"进程 {process_id}: 完成成功, 耗时 {duration:.2f}秒")
            with stress_test_stats['lock']:
                stress_test_stats['completed_processes'] += 1
            
            # 收集进程日志文件到主目录
            # try:
            #     collect_process_logs(process_id, process_dir)
            # except Exception as log_error:
            #     logging.warning(f"进程 {process_id}: 收集日志失败 - {log_error}")
            
            # 发送成功通知
            if webhook_url:
                message = (
                    f"✅ **进程 {process_id} 执行成功**\n"
                    f"**耗时**: {duration:.2f}秒\n"
                    f"**开始时间**: {start_time.strftime('%H:%M:%S')}\n"
                    f"**结束时间**: {end_time.strftime('%H:%M:%S')}\n"
                    f"**工作目录**: `{process_dir}`\n"
                    f"**分配ticket**: `{assigned_ticket or '无'}`"
                )
                send_stress_test_notification(webhook_url, message, f"进程{process_id}")
        else:
            logging.error(f"进程 {process_id}: 执行失败, 返回码 {return_code}, 耗时 {duration:.2f}秒")
            logging.error(f"进程 {process_id}: 标准输出:\n{stdout}")
            logging.error(f"进程 {process_id}: 错误输出:\n{stderr}")
            
            with stress_test_stats['lock']:
                stress_test_stats['failed_processes'] += 1
            
            # 收集进程日志文件到主目录（即使失败也要收集）
            # try:
            #     collect_process_logs(process_id, process_dir)
            # except Exception as log_error:
            #     logging.warning(f"进程 {process_id}: 收集日志失败 - {log_error}")
            
            # 发送失败通知
            if webhook_url:
                message = (
                    f"❌ **进程 {process_id} 执行失败**\n"
                    f"**返回码**: {return_code}\n"
                    f"**耗时**: {duration:.2f}秒\n"
                    f"**分配ticket**: `{assigned_ticket or '无'}`\n"
                    f"**错误信息**: ```\n{stderr[:500]}...\n```" if len(stderr) > 500 else f"**错误信息**: ```\n{stderr}\n```"
                )
                send_stress_test_notification(webhook_url, message, f"进程{process_id}")
        
        return {
            'process_id': process_id,
            'success': return_code == 0,
            'return_code': return_code,
            'duration': duration,
            'start_time': start_time,
            'end_time': end_time,
            'stdout': stdout,
            'stderr': stderr,
            'process_dir': process_dir,
            'assigned_ticket': assigned_ticket
        }
        
    except Exception as e:
        logging.error(f"进程 {process_id}: 运行异常 {str(e)}")
        logging.error(traceback.format_exc())
        
        with stress_test_stats['lock']:
            stress_test_stats['failed_processes'] += 1
            
        # 发送异常通知
        if webhook_url:
            message = (
                f"💥 **进程 {process_id} 运行异常**\n"
                f"**异常信息**: {str(e)}\n"
                f"**分配ticket**: `{assigned_ticket or '无'}`\n"
                f"**堆栈**: ```\n{traceback.format_exc()[:800]}...\n```"
            )
            send_stress_test_notification(webhook_url, message, f"进程{process_id}")
        
        return {
            'process_id': process_id,
            'success': False,
            'error': str(e),
            'process_dir': process_dir,
            'assigned_ticket': assigned_ticket
        }
    finally:
        with stress_test_stats['lock']:
            stress_test_stats['running_processes'] -= 1

def monitor_system_resources():
    """监控系统资源使用情况"""
    logging.info("开始监控系统资源...")
    
    while stress_test_stats['running_processes'] > 0:
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_used_gb = memory.used / (1024 ** 3)
            memory_total_gb = memory.total / (1024 ** 3)
            
            # 磁盘使用率
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            
            # 进程数量
            running_count = stress_test_stats['running_processes']
            
            logging.info(
                f"系统资源: CPU {cpu_percent:.1f}%, "
                f"内存 {memory_percent:.1f}% ({memory_used_gb:.1f}GB/{memory_total_gb:.1f}GB), "
                f"磁盘 {disk_percent:.1f}%, "
                f"运行进程 {running_count}"
            )
            
            # 如果资源使用过高，记录警告
            if cpu_percent > 90:
                logging.warning(f"CPU使用率过高: {cpu_percent:.1f}%")
            if memory_percent > 90:
                logging.warning(f"内存使用率过高: {memory_percent:.1f}%")
                
            time.sleep(30)  # 每30秒检查一次
            
        except Exception as e:
            logging.error(f"监控系统资源时出错: {str(e)}")
            time.sleep(30)

def run_stress_test(concurrent_count=5, questions_file=None, access_mode=None, test_mode=False, webhook_url=None, duration_minutes=None, tickets_config=None, force_text_mode=False, startup_interval=30):
    """运行压力测试"""
    stress_test_stats['start_time'] = datetime.datetime.now()
    stress_test_stats['total_processes'] = concurrent_count
    
    logging.info(f"开始压力测试: {concurrent_count} 个并发进程")
    
    # 检查ticket配置
    if tickets_config:
        available_tickets = len(tickets_config.get('tickets', []))
        if available_tickets < concurrent_count:
            logging.warning(f"可用ticket数量({available_tickets})少于并发数({concurrent_count})，将循环使用ticket")
        else:
            logging.info(f"为 {concurrent_count} 个进程分配不同的ticket")
    else:
        logging.warning("未提供ticket配置，所有进程将使用默认配置")
    
    # 发送开始通知
    if webhook_url:
        message = (
            f"🚀 **压力测试开始**\n"
            f"**并发数**: {concurrent_count}\n"
            f"**启动间隔**: {startup_interval}秒\n"
            f"**开始时间**: {stress_test_stats['start_time'].strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"**测试模式**: {'是' if test_mode else '否'}\n"
            f"**问题文件**: {questions_file or '默认'}\n"
            f"**访问模式**: {access_mode or '顺序'}\n"
            f"**持续时间**: {duration_minutes or '无限制'}分钟\n"
            f"**可用票证**: {len(tickets_config.get('tickets', [])) if tickets_config else 0} 个"
        )
        send_stress_test_notification(webhook_url, message, "开始")
    
    # 启动系统资源监控线程
    monitor_thread = threading.Thread(target=monitor_system_resources, daemon=True)
    monitor_thread.start()
    
    results = []
    
    try:
        # 使用线程池执行并发测试
        with ThreadPoolExecutor(max_workers=concurrent_count) as executor:
            # 按照启动间隔逐个提交任务
            futures = []
            logging.info(f"开始启动进程，启动间隔: {startup_interval}秒")
            
            for i in range(concurrent_count):
                logging.info(f"启动进程 {i+1}/{concurrent_count}")
                
                future = executor.submit(
                    run_single_automation_process,
                    process_id=i+1,
                    questions_file=questions_file,
                    access_mode=access_mode,
                    test_mode=test_mode,
                    webhook_url=webhook_url,
                    tickets_config=tickets_config,
                    force_text_mode=force_text_mode,
                    concurrent_count=concurrent_count
                )
                futures.append(future)
                
                # 等待启动间隔，除非是最后一个进程
                if i < concurrent_count - 1:
                    logging.info(f"等待 {startup_interval}秒 后启动下一个进程...")
                    time.sleep(startup_interval)
            
            # 等待所有任务完成
            for future in as_completed(futures):
                try:
                    result = future.result()
                    results.append(result)
                    ticket_info = f", ticket: {result.get('assigned_ticket', 'N/A')}" if result.get('assigned_ticket') else ""
                    logging.info(f"进程完成: {result['process_id']}, 成功: {result['success']}{ticket_info}")
                except Exception as e:
                    logging.error(f"获取进程结果时出错: {str(e)}")
                    results.append({'success': False, 'error': str(e)})
    
    except KeyboardInterrupt:
        logging.info("收到中断信号，正在停止压力测试...")
        # 这里可以添加优雅停止的逻辑
    except Exception as e:
        logging.error(f"压力测试执行异常: {str(e)}")
        logging.error(traceback.format_exc())
    
    # 统计结果
    end_time = datetime.datetime.now()
    total_duration = (end_time - stress_test_stats['start_time']).total_seconds()
    
    successful_count = sum(1 for r in results if r.get('success', False))
    failed_count = len(results) - successful_count
    
    # 统计ticket使用情况
    used_tickets = set()
    for result in results:
        ticket = result.get('assigned_ticket')
        if ticket:
            used_tickets.add(ticket)
    
    logging.info(f"压力测试完成!")
    logging.info(f"总耗时: {total_duration:.2f}秒")
    logging.info(f"成功进程: {successful_count}/{concurrent_count}")
    logging.info(f"失败进程: {failed_count}/{concurrent_count}")
    logging.info(f"成功率: {(successful_count/concurrent_count*100):.1f}%")
    logging.info(f"使用的不同ticket数量: {len(used_tickets)}")
    
    # 发送结果通知
    # if webhook_url:
    #     message = (
    #         f"🏁 **压力测试完成**\n"
    #         f"**总耗时**: {total_duration:.2f}秒\n"
    #         f"**成功进程**: {successful_count}/{concurrent_count}\n"
    #         f"**失败进程**: {failed_count}/{concurrent_count}\n"
    #         f"**成功率**: {(successful_count/concurrent_count*100):.1f}%\n"
    #         f"**使用ticket数**: {len(used_tickets)}\n"
    #         f"**结束时间**: {end_time.strftime('%Y-%m-%d %H:%M:%S')}"
    #     )
    #     send_stress_test_notification(webhook_url, message, "完成")
    
    # 生成详细报告
    # generate_stress_test_report(results, stress_test_stats['start_time'], end_time)
    
    return results

def generate_stress_test_report(results, start_time, end_time):
    """生成压力测试报告"""
    report_dir = os.path.join(os.getcwd(), 'stress_reports')
    os.makedirs(report_dir, exist_ok=True)
    
    timestamp = start_time.strftime('%Y%m%d_%H%M%S')
    report_file = os.path.join(report_dir, f'stress_test_report_{timestamp}.json')
    
    # 统计ticket使用情况
    used_tickets = set()
    ticket_usage = {}
    for result in results:
        ticket = result.get('assigned_ticket')
        if ticket:
            used_tickets.add(ticket)
            if ticket in ticket_usage:
                ticket_usage[ticket] += 1
            else:
                ticket_usage[ticket] = 1
    
    report_data = {
        'test_info': {
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'total_duration_seconds': (end_time - start_time).total_seconds(),
            'concurrent_count': len(results)
        },
        'summary': {
            'total_processes': len(results),
            'successful_processes': sum(1 for r in results if r.get('success', False)),
            'failed_processes': sum(1 for r in results if not r.get('success', False)),
            'success_rate': sum(1 for r in results if r.get('success', False)) / len(results) * 100 if results else 0,
            'total_tickets_used': len(used_tickets),
            'ticket_usage': ticket_usage
        },
        'process_results': []
    }
    
    for result in results:
        process_data = {
            'process_id': result.get('process_id'),
            'success': result.get('success', False),
            'return_code': result.get('return_code'),
            'duration_seconds': result.get('duration'),
            'start_time': result.get('start_time').isoformat() if result.get('start_time') else None,
            'end_time': result.get('end_time').isoformat() if result.get('end_time') else None,
            'process_dir': result.get('process_dir'),
            'assigned_ticket': result.get('assigned_ticket'),
            'error': result.get('error')
        }
        report_data['process_results'].append(process_data)
    
    # 保存JSON报告
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report_data, f, ensure_ascii=False, indent=2)
    
    logging.info(f"压力测试报告已保存: {report_file}")
    
    # 生成HTML报告
    html_report_file = os.path.join(report_dir, f'stress_test_report_{timestamp}.html')
    generate_html_report(report_data, html_report_file)
    
    return report_file

def generate_html_report(report_data, output_file):
    """生成HTML格式的压力测试报告"""
    html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>压力测试报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        h1 {{ color: #333; text-align: center; }}
        .summary {{ background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; }}
        .summary h2 {{ margin-top: 0; color: #495057; }}
        .stats {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }}
        .stat-card {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; text-align: center; }}
        .stat-value {{ font-size: 2em; font-weight: bold; }}
        .stat-label {{ font-size: 0.9em; opacity: 0.9; }}
        table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
        th, td {{ padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }}
        th {{ background-color: #f8f9fa; font-weight: bold; }}
        .success {{ color: #28a745; font-weight: bold; }}
        .failed {{ color: #dc3545; font-weight: bold; }}
        .process-dir {{ font-family: monospace; font-size: 0.9em; color: #6c757d; }}
        .ticket {{ font-family: monospace; font-size: 0.8em; color: #007bff; background: #e7f3ff; padding: 2px 4px; border-radius: 3px; }}
        .ticket-usage {{ background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; }}
        .ticket-list {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 10px; }}
        .ticket-item {{ background: white; padding: 10px; border-radius: 3px; border-left: 4px solid #17a2b8; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 压力测试报告</h1>
        
        <div class="summary">
            <h2>测试概览</h2>
            <p><strong>开始时间:</strong> {report_data['test_info']['start_time']}</p>
            <p><strong>结束时间:</strong> {report_data['test_info']['end_time']}</p>
            <p><strong>总耗时:</strong> {report_data['test_info']['total_duration_seconds']:.2f} 秒</p>
            <p><strong>并发进程数:</strong> {report_data['test_info']['concurrent_count']}</p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-value">{report_data['summary']['total_processes']}</div>
                <div class="stat-label">总进程数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{report_data['summary']['successful_processes']}</div>
                <div class="stat-label">成功进程</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{report_data['summary']['failed_processes']}</div>
                <div class="stat-label">失败进程</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{report_data['summary']['success_rate']:.1f}%</div>
                <div class="stat-label">成功率</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{report_data['summary']['total_tickets_used']}</div>
                <div class="stat-label">使用票证数</div>
            </div>
        </div>"""
        
    # 添加ticket使用情况
    if report_data['summary'].get('ticket_usage'):
        html_content += """
        <div class="ticket-usage">
            <h2>🎫 票证使用情况</h2>
            <div class="ticket-list">"""
        
        for ticket, count in report_data['summary']['ticket_usage'].items():
            html_content += f"""
                <div class="ticket-item">
                    <strong>Ticket:</strong> <span class="ticket">{ticket}</span><br>
                    <strong>使用次数:</strong> {count}
                </div>"""
        
        html_content += """
            </div>
        </div>"""
    
    html_content += """
        <h2>进程详情</h2>
        <table>
            <thead>
                <tr>
                    <th>进程ID</th>
                    <th>状态</th>
                    <th>耗时(秒)</th>
                    <th>分配票证</th>
                    <th>开始时间</th>
                    <th>结束时间</th>
                    <th>工作目录</th>
                    <th>错误信息</th>
                </tr>
            </thead>
            <tbody>"""
    
    for process in report_data['process_results']:
        status_class = 'success' if process['success'] else 'failed'
        status_text = '✅ 成功' if process['success'] else '❌ 失败'
        
        # 修复嵌套f-string问题
        if process.get('assigned_ticket'):
            ticket_display = f'<span class="ticket">{process["assigned_ticket"]}</span>'
        else:
            ticket_display = '无'
        
        # 处理duration显示
        if process['duration_seconds']:
            duration_display = f"{process['duration_seconds']:.2f}"
        else:
            duration_display = 'N/A'
        
        html_content += f"""
                <tr>
                    <td>{process['process_id']}</td>
                    <td class="{status_class}">{status_text}</td>
                    <td>{duration_display}</td>
                    <td>{ticket_display}</td>
                    <td>{process['start_time'] or 'N/A'}</td>
                    <td>{process['end_time'] or 'N/A'}</td>
                    <td class="process-dir">{process['process_dir'] or 'N/A'}</td>
                    <td>{process['error'] or ''}</td>
                </tr>"""
    
    html_content += """
            </tbody>
        </table>
    </div>
</body>
</html>
    """
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    logging.info(f"HTML压力测试报告已保存: {output_file}")

def cleanup_old_stress_data(days_to_keep=7):
    """清理旧的压力测试数据"""
    try:
        cutoff_date = datetime.datetime.now() - datetime.timedelta(days=days_to_keep)
        
        # 清理旧的进程目录
        for item in os.listdir('.'):
            if item.startswith('stress_process_'):
                item_path = os.path.join('.', item)
                if os.path.isdir(item_path):
                    try:
                        # 获取目录创建时间
                        dir_mtime = datetime.datetime.fromtimestamp(os.path.getmtime(item_path))
                        if dir_mtime < cutoff_date:
                            shutil.rmtree(item_path)
                            logging.info(f"清理旧压力测试目录: {item}")
                    except Exception as e:
                        logging.warning(f"清理目录 {item} 时出错: {str(e)}")
        
        # 清理旧的报告文件
        reports_dir = os.path.join('.', 'stress_reports')
        if os.path.exists(reports_dir):
            for file in os.listdir(reports_dir):
                file_path = os.path.join(reports_dir, file)
                if os.path.isfile(file_path):
                    try:
                        file_mtime = datetime.datetime.fromtimestamp(os.path.getmtime(file_path))
                        if file_mtime < cutoff_date:
                            os.remove(file_path)
                            logging.info(f"清理旧报告文件: {file}")
                    except Exception as e:
                        logging.warning(f"清理文件 {file} 时出错: {str(e)}")
                        
        # 清理旧的日志文件
        logs_dir = os.path.join('.', 'stress_logs')
        if os.path.exists(logs_dir):
            for file in os.listdir(logs_dir):
                file_path = os.path.join(logs_dir, file)
                if os.path.isfile(file_path):
                    try:
                        file_mtime = datetime.datetime.fromtimestamp(os.path.getmtime(file_path))
                        if file_mtime < cutoff_date:
                            os.remove(file_path)
                            logging.info(f"清理旧日志文件: {file}")
                    except Exception as e:
                        logging.warning(f"清理文件 {file} 时出错: {str(e)}")
                        
    except Exception as e:
        logging.error(f"清理旧数据时出错: {str(e)}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='TikaBot聊天自动化压力测试')
    parser.add_argument('--concurrent', '-c', type=int, default=5, help='并发进程数量 (默认: 5)')
    parser.add_argument('--questions-file', type=str, help='问题文件路径')
    parser.add_argument('--access-mode', type=str, choices=['random', 'sequential'], default='sequential', help='问题访问模式')
    parser.add_argument('--test-mode', action='store_true', help='测试模式，只运行少量对话')
    parser.add_argument('--force-text-mode', action='store_true', help='强制文本模式，在第一次对话后自动切换到文本模式')
    parser.add_argument('--webhook-url', type=str, help='钉钉机器人Webhook URL')
    parser.add_argument('--duration', type=int, help='测试持续时间(分钟)')
    parser.add_argument('--cleanup', action='store_true', help='清理旧的测试数据')
    parser.add_argument('--cleanup-days', type=int, default=7, help='保留最近几天的数据 (默认: 7)')
    parser.add_argument('--tickets-config', type=str, default='tickets_config.json', help='票证配置文件路径 (默认: tickets_config.json)')
    
    # 循环压测相关参数
    parser.add_argument('--loop', action='store_true', help='启用循环压力测试模式（进程完成或异常后自动重启）')
    parser.add_argument('--restart-delay', type=int, default=10, help='进程重启延迟时间（秒，默认10秒）')
    parser.add_argument('--max-retries', type=int, default=-1, help='每个进程最大重试次数（-1表示无限重试，默认-1）')
    parser.add_argument('--startup-interval', type=int, default=30, help='进程启动间隔时间（秒，默认30秒）')
    parser.add_argument('--test-window-positions', action='store_true', help='测试窗口位置计算（不启动实际测试）')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_stress_logging()
    
    # 如果只是测试窗口位置，直接运行测试并退出
    if args.test_window_positions:
        test_window_positions(args.concurrent)
        return
    
    logging.info("=" * 60)
    logging.info("TikaBot 聊天自动化压力测试")
    logging.info("=" * 60)
    
    # 清理旧数据(如果需要)
    if args.cleanup:
        logging.info("开始清理旧数据...")
        cleanup_old_stress_data(args.cleanup_days)
    
    # 加载ticket配置
    tickets_config = load_tickets_config(args.tickets_config)
    if tickets_config:
        available_tickets = len(tickets_config.get('tickets', []))
        logging.info(f"加载ticket配置成功: {available_tickets} 个可用ticket")
        
        # 检查并发数是否超过可用ticket数
        if args.concurrent > available_tickets and available_tickets > 0:
            logging.warning(f"并发数({args.concurrent})超过可用ticket数({available_tickets})")
            logging.warning("某些进程将共享ticket")
    else:
        logging.warning("未能加载ticket配置，将使用默认URL")
    
    # 检查依赖文件
    if not os.path.exists('tk_chat_automation.py'):
        logging.error("找不到 tk_chat_automation.py 文件，请确保该文件在当前目录")
        sys.exit(1)
    
    if args.questions_file and not os.path.exists(args.questions_file):
        logging.error(f"找不到问题文件: {args.questions_file}")
        sys.exit(1)
    
    # 验证并发数量
    if args.concurrent < 1 or args.concurrent > 50:
        logging.error("并发数量应该在 1-50 之间")
        sys.exit(1)
    
    # 检查系统资源
    available_memory_gb = psutil.virtual_memory().available / (1024 ** 3)
    if available_memory_gb < args.concurrent * 0.5:  # 每个进程估计需要0.5GB内存
        logging.warning(f"可用内存可能不足 ({available_memory_gb:.1f}GB)，建议减少并发数量")
    
    try:
        # 根据模式选择运行方式
        if args.loop:
            # 循环压力测试模式
            logging.info("启动循环压力测试模式")
            logging.info(f"重启延迟: {args.restart_delay}秒")
            logging.info(f"最大重试次数: {'无限' if args.max_retries == -1 else args.max_retries}")
            
            # 设置信号处理器，优雅停止
            def signal_handler(sig, frame):
                logging.info(f"收到信号 {sig}，正在停止循环压力测试...")
                stop_loop_stress_test()
                sys.exit(0)
            
            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)
            
            # 启动循环压力测试
            success = start_loop_stress_test(
                concurrent_count=args.concurrent,
                questions_file=args.questions_file,
                access_mode=args.access_mode,
                test_mode=args.test_mode,
                webhook_url=args.webhook_url,
                tickets_config=tickets_config,
                restart_delay=args.restart_delay,
                max_retries=args.max_retries,
                force_text_mode=args.force_text_mode,
                startup_interval=args.startup_interval
            )
            
            if not success:
                logging.error("循环压力测试启动失败")
                sys.exit(1)
            
            # 停止循环压力测试
            stop_loop_stress_test()
            
        else:
            # 传统的一次性压力测试模式
            logging.info("启动传统压力测试模式")
            logging.info(f"进程启动间隔: {args.startup_interval}秒")
            
        results = run_stress_test(
            concurrent_count=args.concurrent,
            questions_file=args.questions_file,
            access_mode=args.access_mode,
            test_mode=args.test_mode,
            webhook_url=args.webhook_url,
            duration_minutes=args.duration,
                tickets_config=tickets_config,
                force_text_mode=args.force_text_mode,
                startup_interval=args.startup_interval
        )
        
        logging.info("压力测试完成!")
        
        # 输出ticket使用汇总
        if tickets_config and results:
            used_tickets = set()
            ticket_usage = {}
            
            for result in results:
                ticket = result.get('assigned_ticket')
                if ticket:
                    used_tickets.add(ticket)
                    if ticket in ticket_usage:
                        ticket_usage[ticket] += 1
                    else:
                        ticket_usage[ticket] = 1
            
            logging.info("=" * 40)
            logging.info("票证使用汇总:")
            for ticket, count in ticket_usage.items():
                logging.info(f"  {ticket}: 使用 {count} 次")
            logging.info(f"总计使用了 {len(used_tickets)} 个不同的ticket")
            logging.info("=" * 40)
        
    except KeyboardInterrupt:
        if args.loop:
            logging.info("用户中断了循环压力测试")
            stop_loop_stress_test()
        else:
            logging.info("用户中断了压力测试")
        sys.exit(0)
    except Exception as e:
        logging.error(f"压力测试异常: {str(e)}")
        logging.error(traceback.format_exc())
        if args.loop:
            stop_loop_stress_test()
        sys.exit(1)

if __name__ == "__main__":
    main() 