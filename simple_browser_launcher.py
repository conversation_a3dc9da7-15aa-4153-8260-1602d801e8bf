#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单浏览器启动器
使用更稳定的方式启动多个浏览器窗口
"""

import os
import sys
import time
import logging
import argparse
import subprocess
import threading
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor

def setup_logging():
    """设置日志"""
    log_dir = "logs"
    os.makedirs(log_dir, exist_ok=True)
    
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"simple_launcher_{timestamp}.log")
    
    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    return log_file

def calculate_window_position(process_id, total_count):
    """计算窗口位置，避免重叠"""
    screen_width = 2800
    screen_height = 1800
    window_width = 400
    window_height = 600
    margin = 30
    gap = 80
    
    # 计算每行可以放置的窗口数量
    windows_per_row = (screen_width - margin * 2) // (window_width + gap)
    
    if windows_per_row <= 0:
        windows_per_row = 1
    
    row = process_id // windows_per_row
    col = process_id % windows_per_row
    
    x = margin + col * (window_width + gap)
    y = margin + row * (window_height + gap)
    
    # 确保不超出屏幕边界
    if x + window_width > screen_width:
        x = screen_width - window_width - margin
    if y + window_height > screen_height:
        y = screen_height - window_height - margin
    
    return x, y

def launch_browser_with_os_command(url, process_id, total_count):
    """使用系统命令启动浏览器"""
    log_file = setup_logging()
    
    try:
        # 计算窗口位置
        window_x, window_y = calculate_window_position(process_id, total_count)
        
        logging.info(f"进程 {process_id}: 启动浏览器，URL: {url}")
        logging.info(f"进程 {process_id}: 窗口位置 X={window_x}, Y={window_y}")
        
        # 检测操作系统并使用相应的命令
        if sys.platform == "darwin":  # macOS
            # 使用 open 命令启动 Chrome
            cmd = [
                "open", "-a", "Google Chrome",
                "--args",
                f"--window-position={window_x},{window_y}",
                "--window-size=400,600",
                "--new-window",
                "--user-data-dir=/tmp/chrome_profile_" + str(process_id),
                url
            ]
        elif sys.platform.startswith("linux"):
            # Linux 系统
            cmd = [
                "google-chrome",
                f"--window-position={window_x},{window_y}",
                "--window-size=400,600",
                "--new-window",
                f"--user-data-dir=/tmp/chrome_profile_{process_id}",
                url
            ]
        elif sys.platform == "win32":  # Windows
            # Windows 系统
            cmd = [
                "start", "chrome",
                f"--window-position={window_x},{window_y}",
                "--window-size=400,600",
                "--new-window",
                f"--user-data-dir=C:\\temp\\chrome_profile_{process_id}",
                url
            ]
        else:
            logging.error(f"进程 {process_id}: 不支持的操作系统: {sys.platform}")
            return False
        
        # 执行命令
        logging.info(f"进程 {process_id}: 执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            logging.info(f"进程 {process_id}: 浏览器启动成功")
            return True
        else:
            logging.error(f"进程 {process_id}: 浏览器启动失败 - {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logging.error(f"进程 {process_id}: 启动超时")
        return False
    except Exception as e:
        logging.error(f"进程 {process_id}: 启动失败 - {str(e)}")
        return False

def load_urls_from_file(file_path):
    """从文件加载URL列表"""
    urls = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    urls.append(line)
        logging.info(f"从文件 {file_path} 加载了 {len(urls)} 个URL")
    except Exception as e:
        logging.error(f"加载URL文件失败: {str(e)}")
        return []
    
    return urls

def run_single_browser_process(url, process_id, total_count):
    """运行单个浏览器进程"""
    try:
        success = launch_browser_with_os_command(url, process_id, total_count)
        if success:
            logging.info(f"进程 {process_id}: 完成")
        else:
            logging.error(f"进程 {process_id}: 失败")
        return success
    except Exception as e:
        logging.error(f"进程 {process_id}: 异常 - {str(e)}")
        return False

def main():
    parser = argparse.ArgumentParser(description='简单浏览器启动器')
    parser.add_argument('--urls-file', default='test_urls.txt', help='URL文件路径')
    parser.add_argument('--concurrent', type=int, default=5, help='并发数量')
    parser.add_argument('--startup-interval', type=int, default=2, help='启动间隔（秒）')
    parser.add_argument('--test-window-positions', action='store_true', help='测试窗口位置计算')
    
    args = parser.parse_args()
    
    # 设置日志
    log_file = setup_logging()
    logging.info("=== 简单浏览器启动器开始 ===")
    logging.info(f"URL文件: {args.urls_file}")
    logging.info(f"并发数量: {args.concurrent}")
    logging.info(f"启动间隔: {args.startup_interval}秒")
    logging.info(f"操作系统: {sys.platform}")
    
    # 测试窗口位置
    if args.test_window_positions:
        logging.info("=== 测试窗口位置计算 ===")
        for i in range(args.concurrent):
            x, y = calculate_window_position(i, args.concurrent)
            logging.info(f"进程 {i}: X={x}, Y={y}")
        return
    
    # 加载URL
    urls = load_urls_from_file(args.urls_file)
    if not urls:
        logging.error("没有找到有效的URL，退出")
        return
    
    logging.info(f"总共加载了 {len(urls)} 个URL")
    
    # 使用线程池启动浏览器
    with ThreadPoolExecutor(max_workers=args.concurrent) as executor:
        futures = []
        
        for i, url in enumerate(urls):
            # 提交任务
            future = executor.submit(run_single_browser_process, url, i, len(urls))
            futures.append(future)
            
            # 启动间隔
            if i < len(urls) - 1:  # 不是最后一个
                time.sleep(args.startup_interval)
        
        # 等待所有任务完成
        logging.info("等待所有浏览器进程完成...")
        for i, future in enumerate(futures):
            try:
                result = future.result()
                if result:
                    logging.info(f"进程 {i}: 成功完成")
                else:
                    logging.error(f"进程 {i}: 失败")
            except Exception as e:
                logging.error(f"进程 {i}: 异常 - {str(e)}")
    
    logging.info("=== 简单浏览器启动器结束 ===")

if __name__ == "__main__":
    main() 