#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
TK Chat 聊天模式选择器配置
基于HTML文件分析的结果
"""

# 通用输入框选择器（两种模式通用）
INPUT_SELECTORS = [
    # 基于观察到的class结构（优先级最高）
    'input.flex.h-9.w-full.rounded-md.border.border-input.bg-transparent',
    
    # 简化的class选择器
    'input.flex.h-9.w-full',
    'input[class*="border-input"]',
    'input[class*="bg-transparent"]',
    
    # 基于placeholder（如果有）
    'input[placeholder*="快来"]',
    'input[placeholder*="聊"]',
    
    # 通用选择器
    'input[type="text"]',
    'textarea',
    
    # 容器内的输入框
    '.fixed.bottom-6 input',
    '[class*="max-w-3xl"] input',
    
    # 备用选择器
    '[contenteditable="true"]',
    '[role="textbox"]'
]

# 发送按钮选择器
SEND_BUTTON_SELECTORS = [
    # 基于新的文本模式HTML结构（最高优先级）
    'button[aria-label="发送消息"]',
    'button img[alt="箭头向上-黑"]',
    'button img[src*="arrow_up_black"]',
    'button.animate-button-in',
    
    # 基于位置和结构
    '.fixed.bottom-6 button',
    '[class*="max-w-3xl"] button',
    
    # 基于class
    'button.inline-flex',
    'button[class*="rounded-md"]',
    'button[class*="p-0"]',
    
    # 基于功能
    'button[type="submit"]',
    'button:has-text("发送")',
    'button:has-text("Send")',
    
    # SVG图标按钮
    'button svg',
    'button[class*="send"]',
    
    # 通用选择器
    '[role="button"]'
]

# 发送状态检测选择器
SENDING_STATUS_SELECTORS = [
    'button[aria-label="停止连接"]',
    'button img[alt="停止-黑"]',
    'button img[src*="circle_stop_black"]',
    'button.animate-button-out'
]

# 回复文本选择器（新的文本模式结构）
REPLY_TEXT_SELECTORS = [
    # 基于新的HTML结构（最高优先级）
    'p.text-sm.text-justify.break-all',
    'p[node]',
    'p.text-sm',
    
    # 原有的选择器（保持兼容性）
    '.message-content',
    '.chat-message',
    '.reply-text',
    '[class*="message"]',
    '[class*="reply"]'
]

# 模式检测选择器
MODE_DETECTION_SELECTORS = [
    # 版本信息
    'div:has-text("version")',
    '.version',
    
    # 可能的模式切换按钮
    'button:has-text("文本")',
    'button:has-text("数字人")',
    'button:has-text("语音")',
    
    # 模式指示器
    '[class*="mode"]',
    '[data-mode]',
    '[role="tab"]'
]

def get_input_selector_priority():
    """获取按优先级排序的输入框选择器"""
    return INPUT_SELECTORS

def get_send_button_selector_priority():
    """获取按优先级排序的发送按钮选择器"""
    return SEND_BUTTON_SELECTORS

def test_selectors_on_page(page):
    """在页面上测试选择器"""
    print("🧪 测试输入框选择器...")
    input_results = []
    
    for selector in INPUT_SELECTORS:
        try:
            elements = page.query_selector_all(selector)
            visible_elements = [el for el in elements if el.is_visible()]
            
            if visible_elements:
                result = {
                    'selector': selector,
                    'count': len(visible_elements),
                    'success': True
                }
                input_results.append(result)
                print(f"✅ {selector} - 找到 {len(visible_elements)} 个可见元素")
            else:
                print(f"❌ {selector} - 未找到可见元素")
                
        except Exception as e:
            print(f"⚠️ {selector} - 错误: {e}")
    
    print("\n🧪 测试发送按钮选择器...")
    button_results = []
    
    for selector in SEND_BUTTON_SELECTORS:
        try:
            elements = page.query_selector_all(selector)
            visible_elements = [el for el in elements if el.is_visible()]
            
            if visible_elements:
                result = {
                    'selector': selector,
                    'count': len(visible_elements),
                    'success': True
                }
                button_results.append(result)
                print(f"✅ {selector} - 找到 {len(visible_elements)} 个可见元素")
            else:
                print(f"❌ {selector} - 未找到可见元素")
                
        except Exception as e:
            print(f"⚠️ {selector} - 错误: {e}")
    
    return input_results, button_results

if __name__ == "__main__":
    print("📋 TK Chat 选择器配置")
    print("=" * 40)
    
    print("\n🎯 输入框选择器（按优先级）:")
    for i, selector in enumerate(INPUT_SELECTORS, 1):
        print(f"  {i:2d}. {selector}")
    
    print("\n🚀 发送按钮选择器（按优先级）:")
    for i, selector in enumerate(SEND_BUTTON_SELECTORS, 1):
        print(f"  {i:2d}. {selector}")
    
    print("\n💡 使用建议:")
    print("  1. 按优先级顺序尝试选择器")
    print("  2. 如果第一个失败，自动尝试下一个")
    print("  3. 记录成功的选择器用于后续使用") 