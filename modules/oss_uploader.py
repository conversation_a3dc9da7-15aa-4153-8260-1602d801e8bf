# -*- coding: utf-8 -*-
import time
import os
import random
import json
import logging
import datetime
import traceback
import mimetypes
import oss2

# 阿里云OSS配置
# 使用前请先安装依赖: pip install oss2
oss_access_key_id = 'LTAI5t6yLR9SawePy4TxhihE'
oss_access_key_secret = '******************************'
endpoint = 'http://oss-cn-shanghai.aliyuncs.com'
bucket_name = 'cloudgame-test'
bucket_domain = 'cloudgame-test.oss-cn-shanghai.aliyuncs.com'  # OSS外网域名
folder_name = 'tk_automation'

# 文件转换配置
convert_text_to_html = False  # 是否将文本文件转换为HTML以便查看

def upload_image_to_oss(image_path, max_retries=3):
    """将图片或文本文件上传到OSS，返回可访问的URL"""
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            # 如果这是重试，添加随机延迟
            if retry_count > 0:
                retry_delay = random.uniform(1, 3)  # 1-3秒随机延迟
                logging.info(f"上传重试 {retry_count}/{max_retries}，等待 {retry_delay:.2f} 秒...")
                time.sleep(retry_delay)
            
            # 尝试导入oss2模块，如果未安装则提示安装
            try:
                import oss2
            except ImportError:
                logging.error("未找到oss2模块，请安装依赖: pip install oss2")
                return None
                
            logging.info(f"上传文件到OSS: {image_path}")
            
            # 检查文件是否存在
            if not os.path.exists(image_path):
                logging.error(f"要上传的文件不存在: {image_path}")
                return None
                
            # 创建OSS认证和Bucket对象
            auth = oss2.Auth(oss_access_key_id, oss_access_key_secret)
            bucket = oss2.Bucket(auth, endpoint, bucket_name)
            
            # 构建OSS对象名称，包含日期前缀和文件夹名
            date_prefix = datetime.datetime.now().strftime('%Y%m%d')
            filename = os.path.basename(image_path)
            object_name = f"{folder_name}/{date_prefix}/{filename}"
            
            # 确保目录结构存在（对象存储没有目录概念，但我们可以创建空对象表示目录）
            ensure_oss_dir(bucket, f"{folder_name}/{date_prefix}/")
            
            # 获取文件类型，为设置Content-Type做准备
            content_type = mimetypes.guess_type(image_path)[0]
            headers = {}
            # 新增：如果是png图片，强制设置Content-Type
            if filename.lower().endswith('.png'):
                content_type = 'image/png'
            # 如果是文本文件，特别处理
            if filename.endswith('.txt'):
                content_type = 'text/plain; charset=utf-8'
                headers['Content-Disposition'] = f'inline; filename="{filename}"'
                # 如果需要转换为HTML，则进行转换
                if convert_text_to_html:
                    # 创建HTML版本的文件
                    html_path = convert_txt_to_html(image_path)
                    if html_path:
                        # 上传HTML版本
                        html_object_name = f"{folder_name}/{date_prefix}/{os.path.basename(html_path)}"
                        html_content_type = 'text/html; charset=utf-8'
                        html_headers = {'Content-Type': html_content_type}
                        bucket.put_object_from_file(html_object_name, html_path, headers=html_headers)
                        # 修改返回的文件路径为HTML版本
                        object_name = html_object_name
                        content_type = html_content_type
                        # 删除临时HTML文件
                        try:
                            os.remove(html_path)
                        except:
                            pass
            
            if content_type:
                headers['Content-Type'] = content_type
            
            # 设置上传超时时间
            bucket.timeout = 600  # 设置30秒超时
            
            # 上传文件到OSS，带上内容类型
            result = bucket.put_object_from_file(object_name, image_path, headers=headers)
            
            # 检查上传结果
            if result.status == 200:
                # 构建可访问的URL
                url = f"https://{bucket_domain}/{object_name}"
                logging.info(f"文件上传成功: {url}")
                return url
            else:
                logging.error(f"文件上传失败，状态码: {result.status}")
                retry_count += 1
                continue
                
        except Exception as e:
            logging.error(f"上传文件到OSS失败 (尝试 {retry_count+1}/{max_retries}): {str(e)}")
            
            # 特别处理OSS权限相关错误
            if "AccessDenied" in str(e) or "403" in str(e):
                logging.error("OSS访问被拒绝，可能的原因：")
                logging.error("1. OSS访问密钥没有上传权限")
                logging.error("2. Bucket策略限制了当前用户的操作")
                logging.error("3. 文件路径或Bucket名称不正确")
                logging.error(f"当前尝试上传到: {object_name}")
                logging.error(f"使用的Bucket: {bucket_name}")
                logging.error(f"使用的Endpoint: {endpoint}")
                
                # 权限错误通常不需要重试，直接返回
                if retry_count >= max_retries - 1:
                    logging.error("OSS权限错误，停止重试")
                    return None
                    
            elif "SignatureDoesNotMatch" in str(e):
                logging.error("OSS签名验证失败，请检查AccessKey和AccessKeySecret是否正确")
                return None
                
            elif "NoSuchBucket" in str(e):
                logging.error(f"OSS Bucket不存在: {bucket_name}")
                return None
            
            if retry_count < max_retries - 1:
                retry_count += 1
                continue
            else:
                logging.error("已达到最大重试次数，上传失败")
                logging.error(traceback.format_exc())
                return None
    
    return None

def convert_txt_to_html(txt_path):
    """将文本文件转换为HTML格式，以便于浏览器查看"""
    try:
        # 生成HTML文件路径
        html_path = txt_path.replace('.txt', '.html')
        
        # 读取文本内容
        with open(txt_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 转义HTML特殊字符
        import html as html_module
        escaped_content = html_module.escape(content)
        
        # 替换换行符为<br>标签
        html_content = escaped_content.replace('\n', '<br>\n')
        
        # 添加样式的HTML模板
        html_template = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天记录</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}
        h1 {{
            color: #333;
            text-align: center;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }}
        .question {{
            color: #2c3e50;
            font-weight: bold;
        }}
        .answer {{
            color: #16a085;
        }}
        .error {{
            color: #e74c3c;
        }}
        .dialogue {{
            margin-bottom: 30px;
            border-bottom: 1px dashed #ddd;
            padding-bottom: 20px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>太古数字人聊天记录</h1>
        <div class="content">
"""
        
        # 处理内容转换，不在f-string中使用复杂操作
        processed_content = html_content
        processed_content = processed_content.replace('问: ', '<span class="question">问: </span>')
        processed_content = processed_content.replace('答: ', '<span class="answer">答: </span>')
        processed_content = processed_content.replace('错误: ', '<span class="error">错误: </span>')
        processed_content = processed_content.replace('对话 ', '<div class="dialogue"><strong>对话 ')
        processed_content = processed_content.replace('-'*40, '</strong>')
        processed_content = processed_content.replace('\n\n', '</div>\n\n')
        
        # 完成HTML模板
        html_template += processed_content
        html_template += """
        </div>
    </div>
</body>
</html>"""
        
        # 写入HTML文件
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_template)
            
        logging.info(f"成功将文本文件转换为HTML: {html_path}")
        return html_path
    except Exception as e:
        logging.error(f"转换文本到HTML失败: {str(e)}")
        logging.error(traceback.format_exc())
        return None

def ensure_oss_dir(bucket, dir_path):
    """确保OSS目录结构存在，不存在则创建"""
    try:
        # 对象存储没有目录的概念，但可以通过创建一个空对象来模拟目录
        # 检查目录是否存在
        if not dir_path.endswith('/'):
            dir_path += '/'
        
        # 优化：跳过目录检查，因为OSS会在上传文件时自动创建目录结构
        # 而且list_objects可能受到bucket policy限制
        logging.info(f"跳过OSS目录检查，上传时将自动创建: {dir_path}")
        return True
        
        # 原有的检查逻辑注释掉，避免权限问题
        # try:
        #     # 使用适当的迭代方式检查目录是否存在
        #     # 使用delimiter参数以目录方式列出对象
        #     objects = bucket.list_objects(prefix=dir_path, delimiter='/', max_keys=1)
        #     
        #     # 检查返回的结果
        #     if len(list(objects.object_list)) > 0:
        #         exist = True
        #         logging.info(f"OSS目录已存在: {dir_path}")
        #     else:
        #         logging.info(f"OSS目录不存在: {dir_path}")
        # except Exception as e:
        #     logging.warning(f"检查OSS目录时可能遇到权限限制: {str(e)}")
        #     # 权限不足时也继续执行，因为上传时会自动创建目录
        #     logging.info("继续执行上传，OSS将自动创建目录结构")
        #     return True
        
        # # 如果目录不存在，创建一个空对象表示目录
        # if not exist:
        #     logging.info(f"在OSS中创建目录: {dir_path}")
        #     bucket.put_object(dir_path, '') 
            
    except Exception as e:
        logging.warning(f"确保OSS目录存在时出错: {str(e)}")
        # 即使出错也继续上传，因为OSS会自动创建目录结构
        logging.info("继续执行上传，OSS将自动创建目录结构")
        return True

def upload_text_to_oss(local_file, oss_filename=None):
    """将文本文件上传到OSS并返回URL"""
    if not os.path.exists(local_file):
        logging.error(f"上传文件失败: 文件不存在 {local_file}")
        return None
    
    try:
        # 如果未指定OSS文件名，则使用本地文件名
        if not oss_filename:
            oss_filename = os.path.basename(local_file)
        
        # 确保文件名为小写（用户期望的格式）
        oss_filename = oss_filename.lower()
        
        # 特殊处理调试报告文件 - 即使扩展名是.txt，仍设置正确的Content-Type
        is_debug_report = False
        if "debug_report" in oss_filename:
            is_debug_report = True
        
        # 生成OSS路径
        timestamp = datetime.datetime.now().strftime("%Y%m%d")
        oss_path = f"debug/{timestamp}/{oss_filename}"
        
        # 直接读取二进制内容，避免编码问题
        with open(local_file, 'rb') as f:
            content = f.read()
        
        # 检查文件内容是否为HTML
        is_html_content = content.startswith(b'<!DOCTYPE html>') or content.startswith(b'<html>') or b'<html ' in content[:1000]
        
        # 使用二进制方式上传文件内容，但对于HTML文件使用特殊处理
        if is_debug_report or is_html_content:
            return upload_binary_to_oss(content, oss_path, '.html')
        else:
            return upload_binary_to_oss(content, oss_path, os.path.splitext(local_file)[1])
        
    except Exception as e:
        logging.error(f"上传文本文件到OSS失败: {str(e)}")
        logging.error(traceback.format_exc())
        return None

def upload_binary_to_oss(content, oss_path, extension=None):
    """将二进制内容上传到OSS并返回URL"""
    try:
        # OSS配置参数
        auth = oss2.Auth(oss_access_key_id, oss_access_key_secret)
        bucket = oss2.Bucket(auth, endpoint, bucket_name)
        
        # 根据扩展名设置Content-Type
        headers = {
            'Content-Disposition': 'inline'
        }
        
        # 特殊处理调试报告，即使扩展名是.txt，也设置为HTML类型
        if extension == '.html' or oss_path.endswith('.html') or 'debug_report' in oss_path:
            headers['Content-Type'] = 'text/html; charset=utf-8'
        elif extension == '.log' or oss_path.endswith('.log'):
            headers['Content-Type'] = 'text/plain; charset=utf-8'
        elif extension == '.txt' or oss_path.endswith('.txt'):
            headers['Content-Type'] = 'text/plain; charset=utf-8'
        else:
            headers['Content-Type'] = 'application/octet-stream'
        
        result = bucket.put_object(oss_path, content, headers=headers)
        
        if result.status == 200:
            # 生成可访问的URL
            url = f"https://{bucket_domain}/{oss_path}"
            logging.info(f"内容已上传到OSS: {url}")
            return url
        else:
            logging.error(f"上传内容到OSS失败: 状态码 {result.status}")
            return None
            
    except Exception as e:
        logging.error(f"上传内容到OSS失败: {str(e)}")
        
        # 特别处理OSS权限相关错误
        if "AccessDenied" in str(e) or "403" in str(e):
            logging.error("OSS访问被拒绝，可能的原因：")
            logging.error("1. OSS访问密钥没有上传权限")
            logging.error("2. Bucket策略限制了当前用户的操作")
            logging.error("3. 文件路径或Bucket名称不正确")
            logging.error(f"当前尝试上传到: {oss_path}")
            logging.error(f"使用的Bucket: {bucket_name}")
            logging.error(f"使用的Endpoint: {endpoint}")
            
        elif "SignatureDoesNotMatch" in str(e):
            logging.error("OSS签名验证失败，请检查AccessKey和AccessKeySecret是否正确")
            
        elif "NoSuchBucket" in str(e):
            logging.error(f"OSS Bucket不存在: {bucket_name}")
        
        logging.error(traceback.format_exc())
        return None 