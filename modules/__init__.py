# -*- coding: utf-8 -*-
"""
TK聊天自动化重构模块包
将原始的tk_chat_automation.py拆分为多个功能模块
"""

__version__ = "1.0.0"
__author__ = "TK Automation Team"

# 导入所有功能模块
from .ai_service import (
    load_ai_config,
    get_ai_api_config,
    generate_ai_questions,
    build_question_generation_prompt,
    call_ollama_api,
    call_openai_api,
    call_custom_api,
    parse_generated_questions
)

from .ai_evaluation import (
    load_evaluation_config,
    get_evaluation_api_config,
    evaluate_ai_response,
    build_evaluation_prompt,
    call_ollama_evaluation_api,
    call_openai_evaluation_api,
    call_custom_evaluation_api,
    parse_evaluation_response
)

from .dingtalk_notifier import (
    send_dingtalk_notification,
    RESTART_COMMAND,
    RESTART_FLAG_FILE
)

from .logging_utils import (
    setup_logging,
    find_latest_log_file
)

from .oss_uploader import (
    upload_image_to_oss,
    convert_txt_to_html,
    ensure_oss_dir,
    upload_text_to_oss,
    upload_binary_to_oss
)

from .question_manager import (
    load_questions,
    get_next_questions,
    get_next_question_unified,
    format_question_for_execution,
    process_ai_generated_question,
    process_ai_followup_question
)

__all__ = [
    # AI服务
    'load_ai_config',
    'get_ai_api_config', 
    'generate_ai_questions',
    'build_question_generation_prompt',
    'call_ollama_api',
    'call_openai_api',
    'call_custom_api',
    'parse_generated_questions',
    
    # AI评估
    'load_evaluation_config',
    'get_evaluation_api_config',
    'evaluate_ai_response', 
    'build_evaluation_prompt',
    'call_ollama_evaluation_api',
    'call_openai_evaluation_api',
    'call_custom_evaluation_api',
    'parse_evaluation_response',
    
    # 钉钉通知
    'send_dingtalk_notification',
    'RESTART_COMMAND',
    'RESTART_FLAG_FILE',
    
    # 日志工具
    'setup_logging',
    'find_latest_log_file',
    
    # OSS上传
    'upload_image_to_oss',
    'convert_txt_to_html',
    'ensure_oss_dir',
    'upload_text_to_oss',
    'upload_binary_to_oss',
    
    # 问题管理
    'load_questions',
    'get_next_questions',
    'get_next_question_unified',
    'format_question_for_execution',
    'process_ai_generated_question',
    'process_ai_followup_question'
] 