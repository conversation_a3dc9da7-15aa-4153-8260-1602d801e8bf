# -*- coding: utf-8 -*-
import os
import json
import logging
import random
import traceback

def load_questions(questions_file=None, access_mode=None):
    """加载问题语料，支持新的统一格式和旧格式
    
    Args:
        questions_file: 问题文件路径，默认None使用默认文件
        access_mode: 访问模式，'random'或'sequential'，默认None使用文件中的设置或默认为sequential
    """
    single_questions = []
    multi_questions = []
    image_text_questions = []
    unified_questions = []  # 新的统一问题列表
    mode = access_mode
    
    # 如果未指定问题文件，则使用默认文件
    if questions_file is None:
        questions_file = "questions.json"
        logging.info(f"未指定问题文件，将使用默认文件: {questions_file}")
    
    # 加载JSON文件
    if os.path.exists(questions_file):
        try:
            with open(questions_file, "r", encoding="utf-8") as f:
                data = json.load(f)
            
            # 检查是否是新的统一格式
            if "questions" in data:
                logging.info("检测到新的统一问题格式")
                unified_questions = data.get("questions", [])
                
                # 为了兼容旧代码，将统一格式转换为分类格式
                for question_data in unified_questions:
                    question_type = question_data.get("question_type", "single")
                    content = question_data.get("content")
                    
                    if question_type == "single":
                        single_questions.append(content)
                    elif question_type == "multi":
                        multi_questions.append(content)
                    elif question_type == "image_text":
                        image_text_questions.append(content)
                    elif question_type == "ai_generated":
                        # AI生成问题保持在统一格式中处理，不转换为旧格式
                        pass
                    elif question_type == "ai_followup":
                        # AI追问对话保持在统一格式中处理，不转换为旧格式
                        pass
                    else:
                        logging.warning(f"未知的问题类型: {question_type}")
                
                logging.info(f"已从统一格式转换: {len(single_questions)} 个单问题")
                logging.info(f"已转换: {len(multi_questions)} 组多问题对话")
                logging.info(f"已转换: {len(image_text_questions)} 个图文问题")
            else:
                # 旧格式兼容
                logging.info("检测到旧的分类问题格式")
                single_questions = data.get("single_questions", [])
                multi_questions = data.get("multi_questions", [])
                image_text_questions = data.get("image_text_questions", [])
                
                logging.info(f"已从旧格式加载 {len(single_questions)} 个单问题")
                logging.info(f"已加载 {len(multi_questions)} 组多问题对话")
                logging.info(f"已加载 {len(image_text_questions)} 个图文问题")
            
            # 如果未指定访问模式，则使用文件中的设置
            if not mode:
                mode = data.get("mode", "sequential")
                
            logging.info(f"访问模式: {mode}")
            
        except Exception as e:
            logging.error(f"加载问题文件失败: {str(e)}")
            logging.error(traceback.format_exc())
    
    # 如果问题列表为空，尝试从单独的文件加载
    if not single_questions and not multi_questions and not image_text_questions:
        try:
            with open("single_questions.txt", "r", encoding="utf-8") as f:
                single_questions = [line.strip() for line in f.readlines() if line.strip()]
            logging.info(f"已加载 {len(single_questions)} 个单问题")
        except Exception as e:
            logging.error(f"加载单问题失败: {str(e)}")
            single_questions = ["你好", "介绍一下自己", "帮我推荐几款黑色包包"]
    
    if not multi_questions:
        try:
            with open("multi_questions.txt", "r", encoding="utf-8") as f:
                multi_questions = json.load(f)
            logging.info(f"已加载 {len(multi_questions)} 组多问题对话")
        except Exception as e:
            logging.error(f"加载多问题对话失败: {str(e)}")
            multi_questions = [["你好", "介绍一下自己", "你能做什么"]]
    
    # 如果还没有设置访问模式，默认使用sequential
    if not mode:
        mode = "sequential"
    
    # 返回四个值：三个分类列表和统一列表
    return single_questions, multi_questions, image_text_questions, unified_questions

def get_next_questions(single_questions, multi_questions, image_text_questions, used_single_indices, used_multi_indices, used_image_text_indices, mode="sequential"):
    """选择下一组问题
    
    Args:
        single_questions: 单问题列表
        multi_questions: 多问题组列表
        image_text_questions: 图文问题列表
        used_single_indices: 已使用的单问题索引集合
        used_multi_indices: 已使用的多问题组索引集合
        used_image_text_indices: 已使用的图文问题索引集合
        mode: 访问模式，'random'或'sequential'
    """
    if not single_questions and not multi_questions and not image_text_questions:
        return None
    
    # 检查是否所有问题都已使用过
    all_single_used = len(used_single_indices) >= len(single_questions)
    all_multi_used = len(used_multi_indices) >= len(multi_questions)
    all_image_text_used = len(used_image_text_indices) >= len(image_text_questions)
    
    # 如果所有问题都已使用，则返回None
    if all_single_used and all_multi_used and all_image_text_used:
        return None
    
    if mode == "random":
        # 随机模式 - 随机选择问题类型
        available_types = []
        if not all_single_used:
            available_types.append('single')
        if not all_multi_used:
            available_types.append('multi')
        if not all_image_text_used:
            available_types.append('image_text')
        
        if not available_types:
            return None
        
        question_type = random.choice(available_types)
        
        if question_type == 'single':
            # 选择未使用过的单问题
            available_indices = [i for i in range(len(single_questions)) if i not in used_single_indices]
            if not available_indices:
                return None
            
            selected_index = random.choice(available_indices)
            used_single_indices.add(selected_index)
            return {'type': 'text', 'questions': [single_questions[selected_index]]}
            
        elif question_type == 'multi':
            # 选择未使用过的多问题组
            available_indices = [i for i in range(len(multi_questions)) if i not in used_multi_indices]
            if not available_indices:
                return None
            
            selected_index = random.choice(available_indices)
            used_multi_indices.add(selected_index)
            return {'type': 'text', 'questions': multi_questions[selected_index]}
            
        else:  # image_text
            # 选择未使用过的图文问题
            available_indices = [i for i in range(len(image_text_questions)) if i not in used_image_text_indices]
            if not available_indices:
                return None
            
            selected_index = random.choice(available_indices)
            used_image_text_indices.add(selected_index)
            return {'type': 'image_text', 'question_data': image_text_questions[selected_index]}
    else:
        # 顺序模式 - 先使用所有单问题，再使用多问题，最后使用图文问题
        if not all_single_used:
            # 顺序选择未使用的单问题
            for i in range(len(single_questions)):
                if i not in used_single_indices:
                    used_single_indices.add(i)
                    return {'type': 'text', 'questions': [single_questions[i]]}
        elif not all_multi_used:
            # 顺序选择未使用的多问题组
            for i in range(len(multi_questions)):
                if i not in used_multi_indices:
                    used_multi_indices.add(i)
                    return {'type': 'text', 'questions': multi_questions[i]}
        else:
            # 顺序选择未使用的图文问题
            for i in range(len(image_text_questions)):
                if i not in used_image_text_indices:
                    used_image_text_indices.add(i)
                    return {'type': 'image_text', 'question_data': image_text_questions[i]}
        
        return None

def get_next_question_unified(unified_questions, used_indices, mode="sequential"):
    """从统一问题列表中选择下一个问题
    
    Args:
        unified_questions: 统一问题列表
        used_indices: 已使用的问题索引集合
        mode: 访问模式，'random'或'sequential'
    """
    if not unified_questions:
        return None
    
    # 检查是否所有问题都已使用过
    if len(used_indices) >= len(unified_questions):
        return None
    
    if mode == "random":
        # 随机模式
        available_indices = [i for i in range(len(unified_questions)) if i not in used_indices]
        if not available_indices:
            return None
        
        selected_index = random.choice(available_indices)
        used_indices.add(selected_index)
        question_data = unified_questions[selected_index]
        
        return format_question_for_execution(question_data)
    else:
        # 顺序模式
        for i in range(len(unified_questions)):
            if i not in used_indices:
                used_indices.add(i)
                question_data = unified_questions[i]
                return format_question_for_execution(question_data)
        
        return None

def format_question_for_execution(question_data):
    """将统一格式的问题数据转换为执行格式
    
    Args:
        question_data: 统一格式的问题数据
    
    Returns:
        dict: 适合执行的问题格式
    """
    question_type = question_data.get("question_type", "single")
    content = question_data.get("content")
    
    if question_type == "single":
        return {
            'type': 'text',
            'questions': [content]
        }
    elif question_type == "multi":
        return {
            'type': 'text', 
            'questions': content
        }
    elif question_type == "image_text":
        return {
            'type': 'image_text',
            'question_data': content
        }
    elif question_type == "ai_generated":
        # 处理AI生成问题
        return process_ai_generated_question(content)
    elif question_type == "ai_followup":
        # 处理AI追问对话
        return process_ai_followup_question(content)
    else:
        logging.warning(f"未知的问题类型: {question_type}")
        return None

def process_ai_generated_question(question_data):
    """处理AI生成的问题数据
    
    Args:
        question_data: AI生成问题的数据结构
        
    Returns:
        dict: 处理后的问题执行格式
    """
    try:
        # 从AI生成问题数据中提取相关信息
        generated_questions = question_data.get('generated_questions', [])
        domain = question_data.get('domain', '通用')
        intent_type = question_data.get('intent_type', '普通询问')
        
        if not generated_questions:
            logging.warning("AI生成问题数据中没有找到生成的问题列表")
            return None
        
        # 随机选择一个生成的问题
        selected_question = random.choice(generated_questions)
        
        logging.info(f"选择AI生成问题: {selected_question} (来源: {domain}/{intent_type})")
        
        return {
            'type': 'text',
            'questions': [selected_question],
            'metadata': {
                'source': 'ai_generated',
                'domain': domain,
                'intent_type': intent_type,
                'total_generated': len(generated_questions)
            }
        }
        
    except Exception as e:
        logging.error(f"处理AI生成问题时出错: {str(e)}")
        return None

def process_ai_followup_question(question_data):
    """处理AI追问对话数据
    
    Args:
        question_data: AI追问对话的数据结构
        
    Returns:
        dict: 处理后的问题执行格式
    """
    try:
        # 从AI追问数据中提取相关信息
        conversation_flow = question_data.get('conversation_flow', [])
        domain = question_data.get('domain', '通用')
        scenario = question_data.get('scenario', '普通对话')
        
        if not conversation_flow:
            logging.warning("AI追问数据中没有找到对话流程")
            return None
        
        # 提取所有用户问题
        user_questions = []
        for turn in conversation_flow:
            if turn.get('speaker') == 'user':
                user_questions.append(turn.get('message', ''))
        
        if not user_questions:
            logging.warning("AI追问对话中没有找到用户问题")
            return None
        
        logging.info(f"加载AI追问对话: {len(user_questions)} 个问题 (场景: {domain}/{scenario})")
        
        return {
            'type': 'text',
            'questions': user_questions,
            'metadata': {
                'source': 'ai_followup',
                'domain': domain,
                'scenario': scenario,
                'total_turns': len(conversation_flow),
                'user_questions_count': len(user_questions)
            }
        }
        
    except Exception as e:
        logging.error(f"处理AI追问对话时出错: {str(e)}")
        return None 