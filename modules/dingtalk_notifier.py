# -*- coding: utf-8 -*-
import json
import logging
import requests

# 钉钉消息接收处理
RESTART_COMMAND = "重启"  # 钉钉机器人重启命令
RESTART_FLAG_FILE = "restart_flag.txt"  # 重启标志文件

def send_dingtalk_notification(webhook_url, message, is_error=False, at_phone=None, image_url=None):
    """发送钉钉通知"""
    if not webhook_url or webhook_url == "DISABLE_NOTIFICATION":
        logging.info(f"[钉钉通知-已禁用] 消息内容: {message}")
        if at_phone:
            logging.info(f"[钉钉通知-已禁用] @用户: {at_phone}")
        if image_url:
            logging.info(f"[钉钉通知-已禁用] 图片URL: {image_url}")
        return
        
    headers = {'Content-Type': 'application/json'}
    
    # 图片消息
    if image_url:
        data = {
            "msgtype": "markdown",
            "markdown": {
                "title": "聊天机器人测试通知",
                "text": message + f"\n\n![screenshot]({image_url})"
            }
        }
    else:
        # 普通文本消息
        data = {
            "msgtype": "markdown",
            "markdown": {
                "title": "聊天机器人测试通知",
                "text": message
            }
        }
    
    # 添加 @ 通知
    if at_phone:
        data["at"] = {
            "atMobiles": [at_phone] if isinstance(at_phone, str) else at_phone,
            "isAtAll": False
        }
    
    try:
        response = requests.post(webhook_url, headers=headers, data=json.dumps(data))
        if response.status_code == 200:
            result = response.json()
            if result.get("errcode") == 0:
                logging.info(f"钉钉通知发送成功: {response.text}")
                return True
            else:
                logging.error(f"钉钉通知发送失败: {response.text}")
        else:
            logging.error(f"钉钉通知发送失败，状态码: {response.status_code}, 响应: {response.text}")
    except Exception as e:
        logging.error(f"发送钉钉通知时出错: {str(e)}")
    
    return False 