# -*- coding: utf-8 -*-
import os
import logging
import datetime

def setup_logging():
    """配置日志系统"""
    log_dir = os.path.join(os.getcwd(), 'logs')
    os.makedirs(log_dir, exist_ok=True)
    log_file = os.path.join(log_dir, f'chat_log_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    
    # 配置根日志记录器
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )
    return log_file

def find_latest_log_file(directory, prefix, suffix):
    """查找最新的日志文件
    
    Args:
        directory: 目录路径
        prefix: 文件名前缀
        suffix: 文件名后缀
        
    Returns:
        str: 最新文件的路径，如果没有找到返回None
    """
    try:
        if not os.path.exists(directory):
            return None
        
        # 获取所有匹配的文件
        matching_files = []
        for filename in os.listdir(directory):
            if filename.startswith(prefix) and filename.endswith(suffix):
                filepath = os.path.join(directory, filename)
                if os.path.isfile(filepath):
                    # 获取文件的修改时间
                    mtime = os.path.getmtime(filepath)
                    matching_files.append((filepath, mtime))
        
        if not matching_files:
            return None
        
        # 按修改时间排序，返回最新的文件
        matching_files.sort(key=lambda x: x[1], reverse=True)
        latest_file = matching_files[0][0]
        
        logging.debug(f"找到最新的文件: {latest_file}")
        return latest_file
        
    except Exception as e:
        logging.error(f"查找最新日志文件时出错: {str(e)}")
        return None 