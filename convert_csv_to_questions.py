import csv
import json
import os

def convert_csv_to_questions(csv_file_path, json_file_path):
    """
    读取CSV文件，并将其转换为自动化脚本所需的JSON问题格式。

    Args:
        csv_file_path (str): 输入的CSV文件路径。
        json_file_path (str): 输出的JSON文件路径。
    """
    if not os.path.exists(csv_file_path):
        print(f"❌ 错误：找不到CSV文件 '{csv_file_path}'")
        return

    questions_list = []
    
    try:
        with open(csv_file_path, mode='r', encoding='utf-8') as infile:
            reader = csv.DictReader(infile)
            for i, row in enumerate(reader):
                prompt = row.get('prompt', '').strip()
                image_url = row.get('image', '').strip()

                if not prompt:
                    print(f"⚠️ 警告：第 {i+2} 行的 'prompt' 列为空，已跳过。")
                    continue

                if image_url and (image_url.startswith('http://') or image_url.startswith('https://')):
                    # 处理为图文问题
                    question = {
                        "type": "image_text",
                        "text": prompt,
                        "image": image_url,
                        "imageType": "url"
                    }
                    questions_list.append(question)
                else:
                    # 处理为普通文本问题
                    question = {
                        "type": "single",
                        "text": prompt
                    }
                    questions_list.append(question)
                    
    except Exception as e:
        print(f"❌ 处理CSV文件时出错: {e}")
        return

    # 构建最终的JSON结构
    output_data = {"questions": questions_list}

    try:
        with open(json_file_path, mode='w', encoding='utf-8') as outfile:
            json.dump(output_data, outfile, indent=4, ensure_ascii=False)
        print(f"✅ 成功将 {len(questions_list)} 个问题写入到 '{json_file_path}'")
    except Exception as e:
        print(f"❌ 写入JSON文件时出错: {e}")


if __name__ == '__main__':
    # 定义输入和输出文件路径
    #
    # INPUT_CSV = '/Users/<USER>/workspace/tk-automation/realcasev4-25070902_25070901-25070901-25070901.csv'
    # OUTPUT_JSON = 'generated_questions_01.json'

    #穿搭评价

    #其他
    # INPUT_CSV = '/Users/<USER>/workspace/tk-automation/csv/其他.csv'
    # OUTPUT_JSON = 'generated_questions_other.json'

    #认知
    # INPUT_CSV = '/Users/<USER>/workspace/tk-automation/csv/认知.csv'
    # OUTPUT_JSON = 'generated_questions_cognition.json'


    #本地生活
    # INPUT_CSV = '/Users/<USER>/workspace/tk-automation/csv/本地生活.csv'
    # OUTPUT_JSON = 'generated_questions_local_life.json'

    #时尚
    # INPUT_CSV = '/Users/<USER>/workspace/tk-automation/csv/时尚.csv'
    # OUTPUT_JSON = 'generated_questions_fashion.json'

    #时尚知识
    INPUT_CSV = '/Users/<USER>/workspace/tk-automation/csv/时尚知识.csv'
    OUTPUT_JSON = 'generated_questions_fashion_knowledge.json'
    
    
    # 执行转换
    convert_csv_to_questions(INPUT_CSV, OUTPUT_JSON)

