"""
AI服务模块
负责AI问题生成、追问对话、回答评测等功能
"""

import os
import json
import logging
import requests
import time
import traceback
import random


def load_ai_config(config_file="ai_config.json"):
    """加载AI配置文件
    
    Args:
        config_file: 配置文件路径
        
    Returns:
        dict: AI配置信息
    """
    default_config = {
        "ai_generation_config": {
            "default_provider": "ollama",
            "providers": {
                "ollama": {
                    "provider": "ollama",
                    "model": "qwen2.5:7b",
                    "base_url": "http://localhost:11434",
                    "temperature": 0.7,
                    "max_tokens": 1000,
                    "timeout": 120
                }
            }
        },
        "generation_settings": {
            "default_count": 3,
            "max_count": 10,
            "fallback_to_example": True,
            "retry_attempts": 2
        },
        "ai_evaluation_config": {
            "enabled": False,
            "default_provider": "ollama_eval",
            "providers": {
                "ollama_eval": {
                    "provider": "ollama",
                    "model": "deepseek-r1:14b",
                    "base_url": "http://localhost:11434",
                    "temperature": 0.3,
                    "max_tokens": 1500,
                    "timeout": 180
                }
            }
        },
        "evaluation_settings": {
            "score_scale": 5,
            "evaluation_dimensions": [
                "理解力", "记忆力", "专业性", "情商话术", "知识储备", "合规性"
            ],
            "domain": "时尚"
        }
    }
    
    try:
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            # 创建默认配置文件
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, ensure_ascii=False, indent=2)
            logging.info(f"已创建默认AI配置文件: {config_file}")
            return default_config
    except Exception as e:
        logging.error(f"加载AI配置失败: {str(e)}")
        return default_config


def get_ai_api_config(provider_name=None):
    """获取API配置
    
    Args:
        provider_name: 提供商名称，None时使用默认提供商
        
    Returns:
        dict: API配置信息
    """
    config = load_ai_config()
    ai_config = config.get("ai_generation_config", {})
    
    if provider_name is None:
        provider_name = ai_config.get("default_provider", "ollama")
    
    providers = ai_config.get("providers", {})
    api_config = providers.get(provider_name)
    
    if not api_config:
        logging.warning(f"未找到提供商配置: {provider_name}, 使用默认配置")
        api_config = providers.get("ollama", {
            "provider": "ollama",
            "model": "qwen2.5:7b", 
            "base_url": "http://localhost:11434"
        })
    
    # 处理环境变量替换
    if api_config.get("api_key") and api_config["api_key"].startswith("${") and api_config["api_key"].endswith("}"):
        env_var = api_config["api_key"][2:-1]
        api_config["api_key"] = os.environ.get(env_var)
    
    return api_config


def generate_ai_questions(domain, intent_type, example_question, count, provider_name=None):
    """使用AI模型生成问题变种
    
    Args:
        domain: 问题领域（如"时尚领域"、"美食推荐"）
        intent_type: 意图类型（如"否定意图"、"特殊需求"）
        example_question: 示例问题
        count: 生成问题数量
        provider_name: API提供商名称，None时使用默认提供商
        
    Returns:
        list: 生成的问题列表，失败时返回None
    """
    start_time = time.time()
    try:
        logging.info(f"开始使用AI生成问题 - 领域: {domain}, 意图类型: {intent_type}, 数量: {count}")
        
        # 获取API配置
        api_config = get_ai_api_config(provider_name)
        
        # 构建prompt
        prompt = build_question_generation_prompt(domain, intent_type, example_question, count)
        
        # 根据API提供商调用相应的接口
        provider = api_config.get("provider", "ollama").lower()
        
        api_start_time = time.time()
        if provider == "ollama":
            generated_questions = call_ollama_api(prompt, api_config)
        elif provider == "openai":
            generated_questions = call_openai_api(prompt, api_config)
        elif provider == "custom":
            generated_questions = call_custom_api(prompt, api_config)
        else:
            logging.error(f"不支持的API提供商: {provider}")
            return None
        
        api_end_time = time.time()
        api_duration = api_end_time - api_start_time
        
        if generated_questions:
            total_duration = time.time() - start_time
            logging.info(f"✅ AI问题生成成功 - API耗时: {api_duration:.2f}秒, 总耗时: {total_duration:.2f}秒")
            logging.info(f"📝 成功生成 {len(generated_questions)} 个问题")
            for i, q in enumerate(generated_questions, 1):
                logging.info(f"  {i}. {q}")
            return generated_questions
        else:
            total_duration = time.time() - start_time
            logging.error(f"❌ AI问题生成失败 - 总耗时: {total_duration:.2f}秒")
            return None
            
    except Exception as e:
        total_duration = time.time() - start_time
        logging.error(f"AI生成问题时出错 - 耗时: {total_duration:.2f}秒, 错误: {str(e)}")
        logging.error(traceback.format_exc())
        return None


def build_question_generation_prompt(domain, intent_type, example_question, count):
    """构建AI问题生成的prompt"""
    prompt = f"""你是一个专业的问题生成助手。请根据以下要求生成问题变种：

【任务要求】
- 领域：{domain}
- 意图类型：{intent_type}
- 参考示例：{example_question}
- 生成数量：{count}个

【生成规则】
1. 保持与示例问题相同的意图类型和领域
2. 变换表达方式、具体物品、颜色、场景等细节
3. 保持问题的自然性和实用性
4. 每个问题都应该是完整的、可以直接使用的
5. 问题之间要有一定差异性，避免重复

【输出格式】
请严格按照以下JSON格式输出，不要添加任何其他文字：
{{
    "questions": [
        "生成的问题1",
        "生成的问题2",
        "生成的问题3"
    ]
}}

现在请生成{count}个符合要求的问题："""
    
    return prompt


def call_ollama_api(prompt, api_config):
    """调用Ollama API生成问题"""
    try:
        base_url = api_config.get("base_url", "http://localhost:11434")
        model = api_config.get("model", "qwen2.5:7b")
        
        url = f"{base_url}/api/generate"
        
        payload = {
            "model": model,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": 0.7,
                "top_p": 0.9,
                "num_predict": 1000
            }
        }
        
        logging.info(f"调用Ollama API: {url}, 模型: {model}")
        response = requests.post(url, json=payload, timeout=120)
        response.raise_for_status()
        
        result = response.json()
        generated_text = result.get("response", "")
        
        # 解析生成的问题
        questions = parse_generated_questions(generated_text)
        return questions
        
    except Exception as e:
        logging.error(f"调用Ollama API失败: {str(e)}")
        return None


def call_openai_api(prompt, api_config):
    """调用OpenAI API生成问题"""
    try:
        api_key = api_config.get("api_key")
        model = api_config.get("model", "gpt-3.5-turbo")
        base_url = api_config.get("base_url", "https://api.openai.com/v1")
        
        if not api_key:
            logging.error("OpenAI API密钥未配置")
            return None
        
        url = f"{base_url}/chat/completions"
        
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": model,
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.7,
            "max_tokens": 1000
        }
        
        logging.info(f"调用OpenAI API: {model}")
        response = requests.post(url, json=payload, headers=headers, timeout=120)
        response.raise_for_status()
        
        result = response.json()
        generated_text = result["choices"][0]["message"]["content"]
        
        # 解析生成的问题
        questions = parse_generated_questions(generated_text)
        return questions
        
    except Exception as e:
        logging.error(f"调用OpenAI API失败: {str(e)}")
        return None


def call_custom_api(prompt, api_config):
    """调用自定义API生成问题"""
    try:
        url = api_config.get("url")
        headers = api_config.get("headers", {})
        
        if not url:
            logging.error("自定义API URL未配置")
            return None
        
        # 根据具体API格式构建请求
        payload = api_config.get("payload_template", {})
        if "prompt" in payload:
            payload["prompt"] = prompt
        else:
            payload = {"prompt": prompt}
        
        logging.info(f"调用自定义API: {url}")
        response = requests.post(url, json=payload, headers=headers, timeout=120)
        response.raise_for_status()
        
        result = response.json()
        # 根据API响应格式解析，这里假设返回字段为"text"
        generated_text = result.get("text", result.get("response", ""))
        
        # 解析生成的问题
        questions = parse_generated_questions(generated_text)
        return questions
        
    except Exception as e:
        logging.error(f"调用自定义API失败: {str(e)}")
        return None


def parse_generated_questions(generated_text):
    """解析AI生成的问题文本"""
    try:
        logging.info("开始解析AI生成的问题文本")
        logging.debug(f"生成的原始文本: {generated_text}")
        
        # 首先尝试解析JSON格式
        try:
            # 查找JSON部分
            json_start = generated_text.find('{')
            json_end = generated_text.rfind('}') + 1
            
            if json_start != -1 and json_end > json_start:
                json_text = generated_text[json_start:json_end]
                parsed_data = json.loads(json_text)
                
                if "questions" in parsed_data:
                    questions = parsed_data["questions"]
                    logging.info(f"成功解析JSON格式，获得 {len(questions)} 个问题")
                    return [q.strip() for q in questions if q.strip()]
        except json.JSONDecodeError as e:
            logging.warning(f"JSON解析失败: {e}")
        
        # 如果JSON解析失败，尝试使用正则表达式提取
        questions = []
        
        # 方法1: 查找引号包围的问题
        import re
        quoted_patterns = [
            r'"([^"]+)"',  # 双引号
            r"'([^']+)'",  # 单引号
        ]
        
        for pattern in quoted_patterns:
            matches = re.findall(pattern, generated_text)
            if matches:
                questions.extend([q.strip() for q in matches if len(q.strip()) > 10])
        
        # 方法2: 查找以数字开头的行
        lines = generated_text.split('\n')
        for line in lines:
            line = line.strip()
            # 匹配 "1. 问题" 或 "1、问题" 格式
            if re.match(r'^\d+[.、]\s*(.+)', line):
                question = re.sub(r'^\d+[.、]\s*', '', line).strip()
                if len(question) > 5:
                    questions.append(question)
        
        # 方法3: 查找以"-"开头的行  
        for line in lines:
            line = line.strip()
            if line.startswith('- ') and len(line) > 8:
                question = line[2:].strip()
                questions.append(question)
        
        # 去重并过滤
        unique_questions = []
        seen = set()
        for q in questions:
            q_clean = q.strip()
            if q_clean and len(q_clean) > 5 and q_clean not in seen:
                # 过滤掉明显不是问题的文本
                if not any(keyword in q_clean.lower() for keyword in ['json', 'format', '格式', '输出', 'output']):
                    unique_questions.append(q_clean)
                    seen.add(q_clean)
        
        logging.info(f"使用正则表达式解析，获得 {len(unique_questions)} 个问题")
        return unique_questions
        
    except Exception as e:
        logging.error(f"解析生成的问题文本时出错: {str(e)}")
        return []


# ==================== AI追问对话功能 ====================

def generate_followup_question(conversation_history, domain, scenario, current_round, total_rounds, round_type, provider_name=None):
    """生成追问问题"""
    start_time = time.time()
    try:
        logging.info(f"开始生成AI追问问题 - 轮次: {current_round}/{total_rounds}, 领域: {domain}")
        
        # 构建追问prompt
        prompt = build_followup_prompt(conversation_history, domain, scenario, current_round, total_rounds, round_type)
        
        # 获取API配置
        api_config = get_ai_api_config(provider_name)
        
        # 根据API提供商调用相应的接口
        provider = api_config.get("provider", "ollama").lower()
        
        api_start_time = time.time()
        if provider == "ollama":
            result = call_ollama_followup_api(prompt, api_config)
        elif provider == "openai":
            result = call_openai_followup_api(prompt, api_config)
        elif provider == "custom":
            result = call_custom_followup_api(prompt, api_config)
        else:
            logging.error(f"不支持的API提供商: {provider}")
            return None
        
        api_end_time = time.time()
        api_duration = api_end_time - api_start_time
        
        if result:
            total_duration = time.time() - start_time
            logging.info(f"✅ AI追问问题生成成功 - API耗时: {api_duration:.2f}秒, 总耗时: {total_duration:.2f}秒")
            
            # 确保result是字典格式
            if isinstance(result, str):
                result = {
                    "question": result,
                    "should_end": False,
                    "reason": "AI生成的问题"
                }
            elif isinstance(result, dict):
                if 'question' not in result:
                    result['question'] = "生成的追问问题解析失败"
                if 'should_end' not in result:
                    result['should_end'] = False
                if 'reason' not in result:
                    result['reason'] = "问题生成成功"
            else:
                result = {
                    "question": "追问问题生成异常",
                    "should_end": False,
                    "reason": "返回值格式错误"
                }
            
            logging.info(f"📝 生成的问题: {result.get('question', 'N/A')}")
            logging.info(f"🎯 设计原因: {result.get('reason', 'N/A')}")
            
            # 在结果中添加耗时信息
            result['generation_time'] = total_duration
            result['api_time'] = api_duration
        else:
            total_duration = time.time() - start_time
            logging.error(f"❌ AI追问问题生成失败 - 总耗时: {total_duration:.2f}秒")
        
        return result
        
    except Exception as e:
        total_duration = time.time() - start_time
        logging.error(f"生成追问问题时出错 - 耗时: {total_duration:.2f}秒, 错误: {str(e)}")
        logging.error(traceback.format_exc())
        # 返回默认结果而不是None
        return {
            "question": "AI生成追问失败，请继续对话",
            "should_end": False,
            "reason": f"生成失败: {str(e)}"
        }


def build_followup_prompt(conversation_history, domain, scenario, current_round, total_rounds, round_type):
    """构建追问对话的prompt"""
    
    # 构建对话历史文本
    history_text = ""
    for i, item in enumerate(conversation_history, 1):
        if isinstance(item, dict):
            # 支持两种格式：{'question': x, 'answer': y} 或 {'role': x, 'content': y}
            if 'question' in item and 'answer' in item:
                # 原始格式
                history_text += f"第{i}轮:\n问: {item['question']}\n答: {item['answer']}\n\n"
            elif 'role' in item and 'content' in item:
                # OpenAI格式
                role_text = "问" if item['role'] == 'user' else "答"
                history_text += f"{role_text}: {item['content']}\n"
        else:
            # 如果是字符串格式，直接添加
            history_text += f"第{i}轮: {str(item)}\n\n"
    
    # 根据轮次类型设置不同的结束策略
    if round_type == "ai_judge":
        end_strategy = "如果你认为对话已经达到满意的结果或用户的问题已经充分解决，设置 \"should_end\": true"
    elif round_type == "fixed":
        end_strategy = f"这是{total_rounds}轮固定对话，当前第{current_round}轮"
    else:  # random
        end_strategy = f"这是随机{total_rounds}轮对话，当前第{current_round}轮"
    
    prompt = f"""你是一名专业的AI数字人评测专家，正在和对AI数字人对话，进行{domain}领域的对话能力测试和评估。

【测试场景】
- 测试领域：{domain}
- 测试场景：{scenario}
- 当前轮次：{current_round}/{total_rounds}
- 测试策略：{end_strategy}

【对话历史】
{history_text if history_text else "测试刚开始"}

【测试任务】
作为AI数字人评测专家，你需要：
1. **关键**：仔细分析AI数字人在上一轮的回答（即"答："后面的内容）
2. 基于AI数字人的实际回答内容，设计下一个测试问题
3. 测试问题应该延续AI数字人回答的话题方向，深入测试其专业能力
4. 问题要有针对性，能够有效评估AI数字人的知识储备和应答能力
5. 保持测试的连贯性和逻辑性，形成完整的测试链条
6. 如果是最后一轮，可以设计总结性或挑战性问题

【输出格式】
请严格按照以下JSON格式输出，不要添加任何其他文字：
{{
    "question": "你的测试问题",
    "should_end": false,
    "reason": "设计这个测试问题的原因和评测目标"
}}

现在请生成下一个测试问题："""
    
    return prompt


def call_ollama_followup_api(prompt, api_config):
    """调用Ollama API生成追问问题"""
    try:
        base_url = api_config.get("base_url", "http://localhost:11434")
        model = api_config.get("model", "qwen2.5:7b")
        
        url = f"{base_url}/api/generate"
        
        payload = {
            "model": model,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": 0.7,
                "top_p": 0.9,
                "num_predict": 500
            }
        }
        
        logging.info(f"调用Ollama API生成追问: {url}, 模型: {model}")
        response = requests.post(url, json=payload, timeout=120)
        response.raise_for_status()
        
        result = response.json()
        generated_text = result.get("response", "")
        
        # 解析生成的追问结果
        followup_result = parse_followup_response(generated_text)
        return followup_result
        
    except Exception as e:
        logging.error(f"调用Ollama API生成追问失败: {str(e)}")
        return None


def call_openai_followup_api(prompt, api_config):
    """调用OpenAI API生成追问问题"""
    try:
        api_key = api_config.get("api_key")
        model = api_config.get("model", "gpt-3.5-turbo")
        base_url = api_config.get("base_url", "https://api.openai.com/v1")
        
        if not api_key:
            logging.error("OpenAI API密钥未配置")
            return None
        
        url = f"{base_url}/chat/completions"
        
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": model,
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.7,
            "max_tokens": 500
        }
        
        logging.info(f"调用OpenAI API生成追问: {model}")
        response = requests.post(url, json=payload, headers=headers, timeout=120)
        response.raise_for_status()
        
        result = response.json()
        generated_text = result["choices"][0]["message"]["content"]
        
        # 解析生成的追问结果
        followup_result = parse_followup_response(generated_text)
        return followup_result
        
    except Exception as e:
        logging.error(f"调用OpenAI API生成追问失败: {str(e)}")
        return None


def call_custom_followup_api(prompt, api_config):
    """调用自定义API生成追问问题"""
    try:
        url = api_config.get("url")
        headers = api_config.get("headers", {})
        
        if not url:
            logging.error("自定义API URL未配置")
            return None
        
        payload = api_config.get("payload_template", {})
        if "prompt" in payload:
            payload["prompt"] = prompt
        else:
            payload = {"prompt": prompt}
        
        logging.info(f"调用自定义API生成追问: {url}")
        response = requests.post(url, json=payload, headers=headers, timeout=120)
        response.raise_for_status()
        
        result = response.json()
        generated_text = result.get("text", result.get("response", ""))
        
        # 解析生成的追问结果
        followup_result = parse_followup_response(generated_text)
        return followup_result
        
    except Exception as e:
        logging.error(f"调用自定义API生成追问失败: {str(e)}")
        return None


def parse_followup_response(generated_text):
    """解析AI追问生成的响应"""
    try:
        logging.info(f"开始解析AI追问响应...")
        logging.debug(f"原始响应文本: {generated_text}")
        
        # 方法1: 尝试解析JSON格式
        import re
        
        # 提取JSON部分
        json_match = re.search(r'\{.*\}', generated_text, re.DOTALL)
        if json_match:
            try:
                json_str = json_match.group()
                data = json.loads(json_str)
                if 'question' in data:
                    question = data['question'].strip()
                    if question:
                        logging.info(f"✅ JSON格式解析成功: {question}")
                        return question
            except json.JSONDecodeError:
                pass
        
        # 方法2: 提取问号结尾的句子
        lines = generated_text.split('\n')
        for line in lines:
            line = line.strip()
            if line and line.endswith('？') and len(line) > 3:
                logging.info(f"✅ 问句解析成功: {line}")
                return line
        
        # 方法3: 提取"问:"或"追问:"后的内容
        question_patterns = [
            r'问[:：]\s*(.+)',
            r'追问[:：]\s*(.+)',
            r'测试问题[:：]\s*(.+)',
            r'[^\n]*\?$',
            r'[^\n]*？$'
        ]
        
        for pattern in question_patterns:
            matches = re.findall(pattern, generated_text, re.MULTILINE)
            if matches:
                question = matches[0].strip()
                if question and len(question) > 3:
                    logging.info(f"✅ 模式匹配解析成功: {question}")
                    return question
        
        # 方法4: 直接使用第一个非空行作为问题
        for line in lines:
            line = line.strip()
            if line and len(line) > 5 and not line.startswith(('你', '我', '根据', '基于')):
                logging.info(f"✅ 首行解析成功: {line}")
                return line
        
        logging.error("❌ 无法解析出有效的追问问题")
        return None
        
    except Exception as e:
        logging.error(f"解析AI追问响应时出错: {str(e)}")
        return None


# ==================== AI回答评测功能 ====================

def load_evaluation_config(config_file="ai_config.json"):
    """加载AI评测配置"""
    try:
        ai_config = load_ai_config(config_file)
        eval_config = ai_config.get("ai_evaluation_config", {})
        
        if not eval_config.get("enabled", False):
            return None
            
        return eval_config
    except Exception as e:
        logging.error(f"加载评测配置失败: {str(e)}")
        return None


def get_evaluation_api_config(provider_name=None):
    """获取评测API配置"""
    try:
        eval_config = load_evaluation_config()
        if not eval_config:
            return None
            
        providers = eval_config.get("providers", {})
        
        if provider_name and provider_name in providers:
            api_config = providers[provider_name]
        else:
            default_provider = eval_config.get("default_provider", "ollama_eval")
            api_config = providers.get(default_provider, {})
        
        # 处理环境变量替换
        if api_config.get("api_key") and api_config["api_key"].startswith("${") and api_config["api_key"].endswith("}"):
            env_var = api_config["api_key"][2:-1]
            api_config["api_key"] = os.environ.get(env_var)
        
        return api_config
    except Exception as e:
        logging.error(f"获取评测API配置失败: {str(e)}")
        return None


def evaluate_ai_response(question, answer, conversation_history=None, provider_name=None):
    """评测AI回答质量"""
    try:
        start_time = time.time()
        logging.info("开始评测AI回答质量...")
        
        # 加载评测配置
        config = load_evaluation_config()
        if not config or not config.get('enabled', False):
            logging.info("AI评测功能未启用，跳过评测")
            return None
        
        # 获取API配置
        api_config = get_evaluation_api_config(provider_name)
        if not api_config:
            logging.warning("评测API配置无效，跳过评测")
            return None
        
        # 构建评测prompt
        prompt = build_evaluation_prompt(question, answer, conversation_history)
        
        # 调用API
        provider = api_config.get("provider")
        api_start_time = time.time()
        
        result = None
        try:
            if provider == "ollama":
                result = call_ollama_evaluation_api(prompt, api_config)
            elif provider == "openai":
                result = call_openai_evaluation_api(prompt, api_config)
            elif provider == "custom":
                result = call_custom_evaluation_api(prompt, api_config)
            else:
                logging.error(f"不支持的评测API提供商: {provider}")
                return None
        except Exception as api_error:
            api_duration = time.time() - api_start_time
            logging.error(f"调用评测API失败 - 耗时: {api_duration:.2f}秒, 错误: {str(api_error)}")
            return None
        
        api_duration = time.time() - api_start_time
        
        if result:
            total_duration = time.time() - start_time
            logging.info(f"✅ AI回答评测成功 - API耗时: {api_duration:.2f}秒, 总耗时: {total_duration:.2f}秒")
            
            # 在结果中添加耗时信息
            result['evaluation_time'] = total_duration
            result['api_time'] = api_duration
            return result
        else:
            total_duration = time.time() - start_time
            logging.warning(f"⚠️ AI回答评测返回空结果 - 总耗时: {total_duration:.2f}秒")
            return None
        
    except Exception as e:
        total_duration = time.time() - start_time
        logging.error(f"❌ AI回答评测失败 - 总耗时: {total_duration:.2f}秒")
        logging.error(f"评测错误详情: {str(e)}")
        return None


def build_evaluation_prompt(question, answer, conversation_history=None):
    """构建AI回答评测的prompt"""
    # 获取评测设置
    ai_config = load_ai_config()
    eval_settings = ai_config.get("evaluation_settings", {})
    score_scale = eval_settings.get("score_scale", 5)
    dimensions = eval_settings.get("evaluation_dimensions", [
        "理解力", "记忆力", "专业性", "情商话术", "知识储备", "合规性"
    ])
    domain = eval_settings.get("domain", "时尚")
    
    # 构建对话历史部分
    history_text = ""
    if conversation_history:
        history_text = "\n【对话历史】\n"
        for i, (q, a) in enumerate(conversation_history[-3:], 1):  # 只显示最近3轮
            history_text += f"第{i}轮:\n问: {q}\n答: {a}\n\n"
    
    prompt = f"""你是一名专业的AI助手评测专家，专门评估{domain}领域AI数字人的回答质量。请对以下AI回答进行全面、客观的评测。

{history_text}
【当前对话】
用户问题: {question}
AI回答: {answer}

【评测要求】
请从以下{len(dimensions)}个维度对AI回答进行评测，每个维度使用{score_scale}分制评分：

1. **理解力** (1-{score_scale}分): AI是否准确理解了用户的问题意图、需求和上下文
2. **记忆力** (1-{score_scale}分): AI是否能够记住和引用之前的对话内容，保持对话连贯性
3. **专业性** (1-{score_scale}分): AI在{domain}领域的专业知识是否准确、权威、深入
4. **情商话术** (1-{score_scale}分): AI的表达是否得体、亲和、有感染力，能否恰当处理用户情绪
5. **知识储备** (1-{score_scale}分): AI回答中体现的知识面是否广泛、信息是否丰富实用
6. **合规性** (1-{score_scale}分): AI回答是否合规合法，无有害内容，符合{domain}行业标准

【评分标准】
- {score_scale}分: 优秀，完全满足要求，表现卓越
- {score_scale-1}分: 良好，基本满足要求，有轻微不足
- {score_scale-2}分: 一般，部分满足要求，有明显不足
- {score_scale-3}分: 较差，勉强满足要求，有严重不足
- 1分: 极差，完全不满足要求

【输出格式】
请严格按照以下JSON格式输出，不要添加任何其他文字：
{{
    "detailed_scores": {{
        "理解力": {{"score": 4, "reason": "详细评分理由"}},
        "记忆力": {{"score": 3, "reason": "详细评分理由"}},
        "专业性": {{"score": 5, "reason": "详细评分理由"}},
        "情商话术": {{"score": 4, "reason": "详细评分理由"}},
        "知识储备": {{"score": 4, "reason": "详细评分理由"}},
        "合规性": {{"score": 5, "reason": "详细评分理由"}}
    }},
    "overall_score": 4.2,
    "evaluation_summary": "整体评价：AI回答在专业性和合规性方面表现优秀...",
    "suggestions": "改进建议：1. 可以更好地结合用户的个人偏好... 2. 在某些细节上可以更加深入..."
}}

请现在开始评测："""
    
    return prompt


def call_ollama_evaluation_api(prompt, api_config):
    """调用Ollama API进行回答评测"""
    try:
        base_url = api_config.get("base_url", "http://localhost:11434")
        model = api_config.get("model", "qwen2.5:32b")
        
        url = f"{base_url}/api/generate"
        
        payload = {
            "model": model,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": 0.3,  # 降低温度确保评测的一致性
                "top_p": 0.9,
                "num_predict": 1500
            }
        }
        
        logging.info(f"调用Ollama评测API: {url}, 模型: {model}")
        response = requests.post(url, json=payload, timeout=60)
        response.raise_for_status()
        
        result = response.json()
        generated_text = result.get("response", "")
        
        # 解析评测结果
        evaluation_result = parse_evaluation_response(generated_text)
        return evaluation_result
        
    except Exception as e:
        logging.error(f"调用Ollama评测API失败: {str(e)}")
        return None


def call_openai_evaluation_api(prompt, api_config):
    """调用OpenAI API进行回答评测"""
    try:
        api_key = api_config.get("api_key")
        model = api_config.get("model", "gpt-4")
        base_url = api_config.get("base_url", "https://api.openai.com/v1")
        
        if not api_key:
            logging.error("OpenAI API密钥未配置")
            return None
        
        url = f"{base_url}/chat/completions"
        
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": model,
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.3,
            "max_tokens": 1500
        }
        
        logging.info(f"调用OpenAI评测API: {model}")
        response = requests.post(url, json=payload, headers=headers, timeout=60)
        response.raise_for_status()
        
        result = response.json()
        generated_text = result["choices"][0]["message"]["content"]
        
        # 解析评测结果
        evaluation_result = parse_evaluation_response(generated_text)
        return evaluation_result
        
    except Exception as e:
        logging.error(f"调用OpenAI评测API失败: {str(e)}")
        return None


def call_custom_evaluation_api(prompt, api_config):
    """调用自定义API进行回答评测"""
    try:
        url = api_config.get("url")
        headers = api_config.get("headers", {})
        payload_template = api_config.get("payload_template", {})
        
        # 构建请求负载
        payload = payload_template.copy()
        payload["prompt"] = prompt
        
        logging.info(f"调用自定义评测API: {url}")
        response = requests.post(url, json=payload, headers=headers, timeout=60)
        response.raise_for_status()
        
        result = response.json()
        generated_text = result.get("response", result.get("text", ""))
        
        # 解析评测结果
        evaluation_result = parse_evaluation_response(generated_text)
        return evaluation_result
        
    except Exception as e:
        logging.error(f"调用自定义评测API失败: {str(e)}")
        return None


def parse_evaluation_response(generated_text):
    """解析AI评测响应"""
    try:
        import re
        
        logging.info(f"开始解析AI评测响应...")
        logging.debug(f"原始评测文本: {generated_text}")
        
        # 尝试提取JSON格式
        json_match = re.search(r'\{.*\}', generated_text, re.DOTALL)
        if json_match:
            try:
                json_str = json_match.group()
                data = json.loads(json_str)
                
                # 验证必要字段
                if 'detailed_scores' in data:
                    # 计算综合评分（如果没有提供）
                    if 'overall_score' not in data:
                        scores = []
                        for dimension, info in data['detailed_scores'].items():
                            if isinstance(info, dict) and 'score' in info:
                                scores.append(info['score'])
                        if scores:
                            data['overall_score'] = round(sum(scores) / len(scores), 1)
                    
                    logging.info(f"✅ 评测结果解析成功，综合评分: {data.get('overall_score', 'N/A')}")
                    return data
                    
            except json.JSONDecodeError as e:
                logging.error(f"JSON解析失败: {str(e)}")
        
        # 备用解析：尝试从文本中提取评分信息
        logging.warning("JSON格式解析失败，尝试文本解析...")
        
        # 简单的备用解析逻辑
        scores = {}
        dimensions = ["理解力", "记忆力", "专业性", "情商话术", "知识储备", "合规性"]
        
        for dimension in dimensions:
            # 查找类似 "理解力: 4分" 或 "理解力: 4" 的模式
            pattern = rf'{dimension}[：:]\s*(\d+)'
            match = re.search(pattern, generated_text)
            if match:
                score = int(match.group(1))
                scores[dimension] = {"score": score, "reason": "评分理由解析失败"}
        
        if scores:
            overall_score = round(sum(info['score'] for info in scores.values()) / len(scores), 1)
            result = {
                "detailed_scores": scores,
                "overall_score": overall_score,
                "evaluation_summary": "评测结果解析不完整",
                "suggestions": "建议检查评测模型输出格式"
            }
            logging.info(f"✅ 备用解析成功，综合评分: {overall_score}")
            return result
        
        logging.error("❌ 无法解析出有效的评测结果")
        return None
        
    except Exception as e:
        logging.error(f"解析AI评测响应时出错: {str(e)}")
        return None 