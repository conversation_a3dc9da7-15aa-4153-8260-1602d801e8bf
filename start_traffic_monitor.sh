#!/bin/bash

# Android流量监控工具快速启动脚本

echo "📱 Android应用流量监控工具"
echo "=================================="

# 检查Python版本
if ! command -v python3 &> /dev/null; then
    echo "❌ 未找到Python3，请先安装Python 3.7或更高版本"
    exit 1
fi

echo "✅ Python版本: $(python3 --version)"

# 检查ADB工具
if ! command -v adb &> /dev/null; then
    echo "❌ 未找到ADB工具，请安装Android SDK Platform Tools"
    echo "💡 macOS: brew install android-platform-tools"
    echo "💡 其他系统: 请下载Android SDK Platform Tools"
    exit 1
fi

echo "✅ ADB版本: $(adb version | head -n 1)"

# 检查设备连接
devices=$(adb devices | grep -v "List of devices" | grep "device$" | wc -l)
if [ "$devices" -eq 0 ]; then
    echo "❌ 未找到连接的Android设备"
    echo "💡 请确保:"
    echo "   1. 设备已通过USB连接"
    echo "   2. 已开启开发者选项和USB调试"
    echo "   3. 已授权ADB调试权限"
    exit 1
fi

echo "✅ 找到 $devices 个已连接的设备"
echo ""

# 运行工具
echo "🚀 启动流量监控工具..."
python3 android_traffic_monitor_simple.py 