"""
通知服务模块
负责钉钉通知、邮件通知等功能
"""

import logging
import requests
import json


def send_dingtalk_notification(webhook_url, message, is_error=False, at_phone=None, image_url=None):
    """
    发送钉钉通知
    
    Args:
        webhook_url: 钉钉机器人webhook地址
        message: 消息内容
        is_error: 是否为错误消息
        at_phone: @的手机号
        image_url: 图片URL（可选）
        
    Returns:
        bool: 发送是否成功
    """
    try:
        if not webhook_url:
            logging.warning("钉钉webhook URL未配置，跳过通知")
            return False
        
        # 构建消息内容
        content = {
            "msgtype": "markdown",
            "markdown": {
                "title": "TikaBot自动化通知",
                "text": message
            }
        }
        
        # 添加@某人功能
        if at_phone:
            content["at"] = {
                "atMobiles": [at_phone],
                "isAtAll": False
            }
        
        # 如果有图片，使用图片+文本的形式
        if image_url:
            content = {
                "msgtype": "feedCard",
                "feedCard": {
                    "links": [
                        {
                            "title": "TikaBot自动化通知",
                            "messageURL": image_url,
                            "picURL": image_url,
                            "text": message
                        }
                    ]
                }
            }
        
        # 发送请求
        headers = {
            'Content-Type': 'application/json'
        }
        
        response = requests.post(
            webhook_url,
            data=json.dumps(content),
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('errcode') == 0:
                logging.info("钉钉通知发送成功")
                return True
            else:
                logging.error(f"钉钉通知发送失败: {result.get('errmsg')}")
                return False
        else:
            logging.error(f"钉钉通知请求失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        logging.error(f"发送钉钉通知时出错: {str(e)}")
        return False


def send_email_notification(smtp_config, to_emails, subject, content, attachments=None):
    """
    发送邮件通知
    
    Args:
        smtp_config: SMTP配置 {'host': '', 'port': 587, 'username': '', 'password': ''}
        to_emails: 收件人邮箱列表
        subject: 邮件主题
        content: 邮件内容
        attachments: 附件列表
        
    Returns:
        bool: 发送是否成功
    """
    try:
        import smtplib
        from email.mime.text import MIMEText
        from email.mime.multipart import MIMEMultipart
        from email.mime.base import MIMEBase
        from email import encoders
        import os
        
        # 创建邮件对象
        msg = MIMEMultipart()
        msg['From'] = smtp_config['username']
        msg['To'] = ', '.join(to_emails)
        msg['Subject'] = subject
        
        # 添加邮件正文
        msg.attach(MIMEText(content, 'html', 'utf-8'))
        
        # 添加附件
        if attachments:
            for attachment_path in attachments:
                if os.path.exists(attachment_path):
                    with open(attachment_path, 'rb') as f:
                        part = MIMEBase('application', 'octet-stream')
                        part.set_payload(f.read())
                        encoders.encode_base64(part)
                        part.add_header(
                            'Content-Disposition',
                            f'attachment; filename= {os.path.basename(attachment_path)}'
                        )
                        msg.attach(part)
        
        # 连接SMTP服务器并发送
        server = smtplib.SMTP(smtp_config['host'], smtp_config['port'])
        server.starttls()
        server.login(smtp_config['username'], smtp_config['password'])
        server.send_message(msg)
        server.quit()
        
        logging.info(f"邮件通知发送成功: {to_emails}")
        return True
        
    except ImportError:
        logging.warning("邮件发送功能需要额外的库支持")
        return False
    except Exception as e:
        logging.error(f"发送邮件通知时出错: {str(e)}")
        return False


def send_slack_notification(webhook_url, message, channel=None, username="TikaBot"):
    """
    发送Slack通知
    
    Args:
        webhook_url: Slack webhook URL
        message: 消息内容
        channel: 频道（可选）
        username: 用户名
        
    Returns:
        bool: 发送是否成功
    """
    try:
        payload = {
            "text": message,
            "username": username
        }
        
        if channel:
            payload["channel"] = channel
        
        response = requests.post(
            webhook_url,
            data=json.dumps(payload),
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        if response.status_code == 200:
            logging.info("Slack通知发送成功")
            return True
        else:
            logging.error(f"Slack通知发送失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        logging.error(f"发送Slack通知时出错: {str(e)}")
        return False


def send_teams_notification(webhook_url, title, message, color="0078D4"):
    """
    发送Microsoft Teams通知
    
    Args:
        webhook_url: Teams webhook URL
        title: 消息标题
        message: 消息内容
        color: 消息颜色
        
    Returns:
        bool: 发送是否成功
    """
    try:
        payload = {
            "@type": "MessageCard",
            "@context": "http://schema.org/extensions",
            "themeColor": color,
            "summary": title,
            "sections": [{
                "activityTitle": title,
                "text": message,
                "markdown": True
            }]
        }
        
        response = requests.post(
            webhook_url,
            data=json.dumps(payload),
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        if response.status_code == 200:
            logging.info("Teams通知发送成功")
            return True
        else:
            logging.error(f"Teams通知发送失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        logging.error(f"发送Teams通知时出错: {str(e)}")
        return False


def send_webhook_notification(webhook_url, data, headers=None, method='POST'):
    """
    发送通用webhook通知
    
    Args:
        webhook_url: webhook URL
        data: 数据（字典格式）
        headers: 请求头
        method: HTTP方法
        
    Returns:
        bool: 发送是否成功
    """
    try:
        if headers is None:
            headers = {'Content-Type': 'application/json'}
        
        if method.upper() == 'POST':
            response = requests.post(
                webhook_url,
                data=json.dumps(data),
                headers=headers,
                timeout=10
            )
        elif method.upper() == 'PUT':
            response = requests.put(
                webhook_url,
                data=json.dumps(data),
                headers=headers,
                timeout=10
            )
        else:
            logging.error(f"不支持的HTTP方法: {method}")
            return False
        
        if response.status_code in [200, 201, 204]:
            logging.info("Webhook通知发送成功")
            return True
        else:
            logging.error(f"Webhook通知发送失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        logging.error(f"发送Webhook通知时出错: {str(e)}")
        return False


def format_error_message(error_info, context=None):
    """
    格式化错误消息
    
    Args:
        error_info: 错误信息
        context: 上下文信息
        
    Returns:
        str: 格式化的消息
    """
    message = f"## ❌ TikaBot运行错误\n\n"
    message += f"**错误信息:** {error_info}\n\n"
    
    if context:
        message += f"**上下文信息:**\n"
        for key, value in context.items():
            message += f"- {key}: {value}\n"
    
    message += f"\n**时间:** {logging.Formatter().formatTime(logging.LogRecord('', 0, '', 0, '', (), None))}"
    
    return message


def format_success_message(stats, files=None):
    """
    格式化成功消息
    
    Args:
        stats: 统计信息
        files: 文件列表
        
    Returns:
        str: 格式化的消息
    """
    message = f"## ✅ TikaBot运行成功\n\n"
    
    if stats:
        message += f"**统计信息:**\n"
        for key, value in stats.items():
            message += f"- {key}: {value}\n"
    
    if files:
        message += f"\n**生成文件:**\n"
        for file_info in files:
            if isinstance(file_info, dict):
                message += f"- [{file_info.get('name', 'unknown')}]({file_info.get('url', '#')})\n"
            else:
                message += f"- {file_info}\n"
    
    message += f"\n**时间:** {logging.Formatter().formatTime(logging.LogRecord('', 0, '', 0, '', (), None))}"
    
    return message


class NotificationManager:
    """通知管理器"""
    
    def __init__(self, config=None):
        """
        初始化通知管理器
        
        Args:
            config: 通知配置
        """
        self.config = config or {}
        self.enabled_services = self.config.get('enabled_services', [])
    
    def send_notification(self, message, notification_type='info', **kwargs):
        """
        发送通知到所有启用的服务
        
        Args:
            message: 消息内容
            notification_type: 通知类型（info/error/success）
            **kwargs: 其他参数
        """
        results = {}
        
        for service in self.enabled_services:
            try:
                if service == 'dingtalk' and 'dingtalk' in self.config:
                    webhook_url = self.config['dingtalk'].get('webhook_url')
                    at_phone = self.config['dingtalk'].get('at_phone')
                    results[service] = send_dingtalk_notification(
                        webhook_url, message, 
                        is_error=(notification_type == 'error'),
                        at_phone=at_phone
                    )
                
                elif service == 'email' and 'email' in self.config:
                    smtp_config = self.config['email'].get('smtp')
                    to_emails = self.config['email'].get('to_emails', [])
                    subject = f"TikaBot通知 - {notification_type.upper()}"
                    results[service] = send_email_notification(
                        smtp_config, to_emails, subject, message
                    )
                
                elif service == 'slack' and 'slack' in self.config:
                    webhook_url = self.config['slack'].get('webhook_url')
                    channel = self.config['slack'].get('channel')
                    results[service] = send_slack_notification(
                        webhook_url, message, channel
                    )
                
                elif service == 'teams' and 'teams' in self.config:
                    webhook_url = self.config['teams'].get('webhook_url')
                    title = f"TikaBot - {notification_type.upper()}"
                    color = "FF0000" if notification_type == 'error' else "0078D4"
                    results[service] = send_teams_notification(
                        webhook_url, title, message, color
                    )
                    
            except Exception as e:
                logging.error(f"发送{service}通知失败: {str(e)}")
                results[service] = False
        
        return results
    
    def send_error_notification(self, error_info, context=None):
        """发送错误通知"""
        message = format_error_message(error_info, context)
        return self.send_notification(message, 'error')
    
    def send_success_notification(self, stats, files=None):
        """发送成功通知"""
        message = format_success_message(stats, files)
        return self.send_notification(message, 'success') 