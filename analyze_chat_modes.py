#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
分析聊天页面的文本模式和数字人模式差异
"""

import sys
import os
import time
import logging
from datetime import datetime
from playwright.sync_api import sync_playwright

def analyze_input_elements(page):
    """分析输入框元素"""
    print("\n📝 分析输入框元素...")
    
    # 基于已知HTML结构的选择器
    selectors_to_check = [
        # 通用输入框
        'input[placeholder*="快来"]',
        'input[placeholder*="聊"]',
        'input[type="text"]',
        'textarea',
        
        # 基于观察到的class结构
        'input.flex.h-9.w-full',
        'input[class*="rounded-md"]',
        'input[class*="border-input"]',
        'input[class*="bg-transparent"]',
        
        # 可能的容器
        '.fixed.bottom-6',
        '[class*="max-w-3xl"]',
        
        # contenteditable
        '[contenteditable="true"]',
        
        # 其他可能的输入元素
        '[role="textbox"]',
        '.input-field',
        '.chat-input'
    ]
    
    found_inputs = []
    
    for selector in selectors_to_check:
        try:
            elements = page.query_selector_all(selector)
            for element in elements:
                if element.is_visible():
                    info = {
                        'selector': selector,
                        'tag': element.evaluate('el => el.tagName.toLowerCase()'),
                        'placeholder': element.get_attribute('placeholder') or '',
                        'class': element.get_attribute('class') or '',
                        'id': element.get_attribute('id') or '',
                        'type': element.get_attribute('type') or '',
                        'visible': element.is_visible(),
                        'enabled': element.is_enabled(),
                        'bounding_box': element.bounding_box()
                    }
                    found_inputs.append(info)
                    print(f"✅ 输入框: {selector}")
                    print(f"   标签: {info['tag']}")
                    print(f"   占位符: '{info['placeholder']}'")
                    print(f"   类名: {info['class'][:100]}...")
                    print(f"   位置: {info['bounding_box']}")
                    print()
        except Exception as e:
            print(f"❌ 检查失败 {selector}: {e}")
    
    return found_inputs

def analyze_send_buttons(page):
    """分析发送按钮"""
    print("\n🚀 分析发送按钮...")
    
    selectors_to_check = [
        # 通用按钮
        'button[type="submit"]',
        'button:has-text("发送")',
        'button:has-text("Send")',
        
        # SVG图标按钮
        'button svg',
        'button[class*="send"]',
        
        # 基于观察到的结构
        'button.inline-flex',
        'button[class*="rounded-md"]',
        'button[class*="p-0"]',
        
        # 可能在输入框附近的按钮
        '.fixed.bottom-6 button',
        '[class*="max-w-3xl"] button',
        
        # 图标或其他形式
        '[role="button"]',
        '.send-btn',
        '.submit-btn'
    ]
    
    found_buttons = []
    
    for selector in selectors_to_check:
        try:
            elements = page.query_selector_all(selector)
            for element in elements:
                if element.is_visible():
                    info = {
                        'selector': selector,
                        'text': element.inner_text() or '',
                        'class': element.get_attribute('class') or '',
                        'id': element.get_attribute('id') or '',
                        'type': element.get_attribute('type') or '',
                        'visible': element.is_visible(),
                        'enabled': element.is_enabled(),
                        'bounding_box': element.bounding_box()
                    }
                    found_buttons.append(info)
                    print(f"✅ 按钮: {selector}")
                    print(f"   文本: '{info['text']}'")
                    print(f"   类名: {info['class'][:100]}...")
                    print(f"   位置: {info['bounding_box']}")
                    print()
        except Exception as e:
            print(f"❌ 检查失败 {selector}: {e}")
    
    return found_buttons

def analyze_chat_modes(page):
    """分析聊天模式"""
    print("\n🎭 分析聊天模式...")
    
    # 检查可能的模式切换元素
    mode_selectors = [
        # 可能的切换按钮
        'button:has-text("文本")',
        'button:has-text("数字人")',
        'button:has-text("语音")',
        
        # 可能的模式指示器
        '[class*="mode"]',
        '[data-mode]',
        '.toggle',
        '.switch',
        
        # 可能的标签页
        '[role="tab"]',
        '.tab',
        
        # 版本信息
        'div:has-text("version")',
        '.version'
    ]
    
    for selector in mode_selectors:
        try:
            elements = page.query_selector_all(selector)
            for element in elements:
                text = element.inner_text() or ''
                class_name = element.get_attribute('class') or ''
                print(f"✅ 模式相关: {selector}")
                print(f"   文本: '{text}'")
                print(f"   类名: {class_name}")
                print()
        except Exception as e:
            print(f"❌ 检查失败 {selector}: {e}")

def save_page_screenshot(page, filename):
    """保存页面截图"""
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        screenshot_path = f"debug_screenshot_{filename}_{timestamp}.png"
        page.screenshot(path=screenshot_path, full_page=True)
        print(f"📸 截图已保存: {screenshot_path}")
        return screenshot_path
    except Exception as e:
        print(f"❌ 截图失败: {e}")
        return None

def main():
    """主函数"""
    print("🔍 TK Chat 模式分析工具")
    print("=" * 50)
    
    with sync_playwright() as p:
        # 使用更稳定的浏览器启动参数
        browser = p.chromium.launch(
            headless=False,
            args=[
                '--disable-dev-shm-usage',
                '--disable-extensions',
                '--no-first-run',
                '--disable-default-apps',
                '--disable-background-timer-throttling',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ],
            slow_mo=1000  # 添加延迟以提高稳定性
        )
        
        try:
            context = browser.new_context(
                viewport={'width': 1280, 'height': 720},
                user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
                ignore_https_errors=True
            )
            
            page = context.new_page()
            
            # 访问页面
            url = os.getenv('TARGET_URL', 'https://tkmates.swireproperties.com.cn?ticket=61653113-3c5f-4bdb-9f73-3be15272d1ca&mallCode=PUBLIC&devtool=false')
            print(f"🌐 访问: {url}")
            
            try:
                page.goto(url, wait_until='networkidle', timeout=60000)
            except Exception as e:
                print(f"⚠️ 网络超时，尝试较短超时时间...")
                page.goto(url, timeout=30000)
            
            # 等待页面稳定
            time.sleep(5)
            
            print("\n📋 当前页面信息:")
            print(f"URL: {page.url}")
            print(f"标题: {page.title()}")
            
            # 初始分析
            print("\n" + "="*50)
            print("🎯 初始页面分析（可能是数字人模式）")
            print("="*50)
            
            save_page_screenshot(page, "initial")
            analyze_chat_modes(page)
            inputs_initial = analyze_input_elements(page)
            buttons_initial = analyze_send_buttons(page)
            
            # 等待用户手动切换模式
            print("\n" + "="*50)
            print("⏸️  请手动操作:")
            print("1. 观察当前页面状态")
            print("2. 如果有模式切换选项，请切换到文本模式")
            print("3. 完成后按回车键继续分析")
            print("="*50)
            input()
            
            # 切换后分析
            print("\n" + "="*50)
            print("🎯 切换后页面分析（可能是文本模式）")
            print("="*50)
            
            save_page_screenshot(page, "after_switch")
            analyze_chat_modes(page)
            inputs_after = analyze_input_elements(page)
            buttons_after = analyze_send_buttons(page)
            
            # 对比分析
            print("\n" + "="*50)
            print("📊 对比分析")
            print("="*50)
            
            print(f"初始输入框数量: {len(inputs_initial)}")
            print(f"切换后输入框数量: {len(inputs_after)}")
            
            print(f"初始按钮数量: {len(buttons_initial)}")
            print(f"切换后按钮数量: {len(buttons_after)}")
            
            # 生成推荐的选择器
            print("\n" + "="*50)
            print("💡 推荐的选择器配置")
            print("="*50)
            
            if inputs_after:
                best_input = inputs_after[0]
                print("🎯 推荐输入框选择器:")
                if best_input['placeholder']:
                    print(f"   input[placeholder=\"{best_input['placeholder']}\"]")
                if best_input['class']:
                    # 提取关键class
                    classes = best_input['class'].split()
                    key_classes = [c for c in classes if c in ['input', 'text', 'chat', 'message']]
                    if key_classes:
                        print(f"   input.{'.'.join(key_classes)}")
                print(f"   {best_input['tag']}  # 通用标签选择器")
            
            if buttons_after:
                best_button = buttons_after[0]
                print("\n🚀 推荐发送按钮选择器:")
                if best_button['text']:
                    print(f"   button:has-text(\"{best_button['text']}\")")
                if best_button['class']:
                    # 提取关键class
                    classes = best_button['class'].split()
                    key_classes = [c for c in classes if c in ['send', 'submit', 'button']]
                    if key_classes:
                        print(f"   button.{'.'.join(key_classes)}")
            
            print("\n✅ 分析完成！请查看截图和输出的选择器建议。")
            
        except Exception as e:
            print(f"❌ 分析过程出错: {e}")
            import traceback
            traceback.print_exc()
            
        finally:
            print("\n按回车键关闭浏览器...")
            input()
            try:
                browser.close()
            except:
                pass

if __name__ == "__main__":
    main() 