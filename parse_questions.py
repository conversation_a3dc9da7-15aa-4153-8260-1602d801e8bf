#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os
import json
import argparse
import uuid
import oss2
import logging
from typing import List, Dict, Any

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def parse_single_questions_file(input_file: str) -> List[str]:
    """
    解析单问题文件，每行一个问题
    
    Args:
        input_file: 输入文本文件路径
    
    Returns:
        单问题列表
    """
    logging.info(f"开始解析单问题文件: {input_file}")
    
    # 读取输入文件
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 过滤空行并去除首尾空白字符
    questions = [line.strip() for line in lines if line.strip()]
    
    logging.info(f"单问题文件解析完成，共 {len(questions)} 个问题")
    return questions

def parse_multi_questions_file(input_file: str) -> List[List[str]]:
    """
    解析多问题JSON文件
    
    Args:
        input_file: 输入JSON文件路径
    
    Returns:
        多问题组列表
    """
    logging.info(f"开始解析多问题文件: {input_file}")
    
    # 读取输入文件
    with open(input_file, 'r', encoding='utf-8') as f:
        question_groups = json.load(f)
    
    logging.info(f"多问题文件解析完成，共 {len(question_groups)} 组问题")
    return question_groups

def merge_questions(single_questions: List[str], multi_questions: List[List[str]]) -> Dict[str, Any]:
    """
    合并单问题和多问题
    
    Args:
        single_questions: 单问题列表
        multi_questions: 多问题组列表
    
    Returns:
        合并后的问题字典
    """
    return {
        "version": "1.0",
        "timestamp": "",  # 在上传时填充
        "single_questions": single_questions,
        "multi_questions": multi_questions,
        "mode": "sequential",  # 默认顺序模式
    }

def upload_to_oss(merged_data: Dict[str, Any], oss_access_key_id: str, oss_access_key_secret: str, 
                 oss_bucket: str, oss_endpoint: str) -> str:
    """
    上传合并后的数据到OSS
    
    Args:
        merged_data: 合并后的问题数据
        oss_access_key_id: OSS访问密钥ID
        oss_access_key_secret: OSS访问密钥Secret
        oss_bucket: OSS存储桶名称
        oss_endpoint: OSS访问域名
    
    Returns:
        上传后的文件URL
    """
    try:
        # 生成文件名
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        merged_data["timestamp"] = timestamp
        filename = f"questions_{timestamp}.json"
        
        # 连接到OSS
        auth = oss2.Auth(oss_access_key_id, oss_access_key_secret)
        bucket = oss2.Bucket(auth, oss_endpoint, oss_bucket)
        
        # 上传文件
        json_data = json.dumps(merged_data, ensure_ascii=False, indent=2)
        bucket.put_object(filename, json_data.encode('utf-8'))
        
        # 构建URL
        url = f"https://{oss_bucket}.{oss_endpoint}/{filename}"
        logging.info(f"文件已上传到OSS: {url}")
        return url
    except Exception as e:
        logging.error(f"上传OSS失败: {str(e)}")
        return ""

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='解析并合并问题文件')
    parser.add_argument('--single', type=str, required=True, help='单问题文件路径')
    parser.add_argument('--multi', type=str, required=True, help='多问题文件路径')
    parser.add_argument('--output', type=str, default='merged_questions.json', help='输出文件路径')
    parser.add_argument('--upload-oss', action='store_true', help='是否上传到OSS')
    parser.add_argument('--oss-key-id', type=str, help='OSS访问密钥ID')
    parser.add_argument('--oss-key-secret', type=str, help='OSS访问密钥Secret')
    parser.add_argument('--oss-bucket', type=str, help='OSS存储桶名称')
    parser.add_argument('--oss-endpoint', type=str, help='OSS访问域名')
    
    args = parser.parse_args()
    
    # 检查输入文件是否存在
    if not os.path.exists(args.single):
        logging.error(f"错误：找不到单问题文件 {args.single}")
        return
        
    if not os.path.exists(args.multi):
        logging.error(f"错误：找不到多问题文件 {args.multi}")
        return
    
    # 解析文件
    single_questions = parse_single_questions_file(args.single)
    multi_questions = parse_multi_questions_file(args.multi)
    
    # 合并问题
    merged_data = merge_questions(single_questions, multi_questions)
    
    # 保存到本地
    with open(args.output, 'w', encoding='utf-8') as f:
        json.dump(merged_data, f, ensure_ascii=False, indent=2)
    logging.info(f"已将合并后的问题保存到: {args.output}")
    
    # 显示样例数据
    print("\n合并后的问题数据统计:")
    print(f"单问题数量: {len(single_questions)}")
    print(f"多问题组数量: {len(multi_questions)}")
    
    # 上传到OSS
    if args.upload_oss:
        if not all([args.oss_key_id, args.oss_key_secret, args.oss_bucket, args.oss_endpoint]):
            logging.error("错误：需要提供OSS的所有参数才能上传")
            return
            
        url = upload_to_oss(merged_data, args.oss_key_id, args.oss_key_secret, 
                           args.oss_bucket, args.oss_endpoint)
        if url:
            print(f"\n已上传到OSS，访问URL: {url}")

if __name__ == "__main__":
    main() 