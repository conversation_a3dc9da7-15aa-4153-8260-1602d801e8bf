#!/bin/bash

# TK Chat Automation 启动脚本
# 解决conda环境激活问题

echo "=== TK Chat Automation 启动脚本 ==="

# 检查conda环境是否存在
if [ ! -d "$HOME/miniforge3/envs/tk_chat" ]; then
    echo "❌ 错误: tk_chat conda环境不存在"
    echo "请先创建环境: conda create -n tk_chat python=3.9"
    exit 1
fi

# 检查playwright是否已安装
if ! $HOME/miniforge3/envs/tk_chat/bin/python3 -c "import playwright" 2>/dev/null; then
    echo "❌ 错误: playwright未安装在tk_chat环境中"
    echo "正在安装playwright..."
    
    # 激活环境并安装playwright
    source $HOME/miniforge3/etc/profile.d/conda.sh
    conda activate tk_chat
    conda install -c conda-forge playwright -y
    playwright install
    
    if [ $? -eq 0 ]; then
        echo "✅ playwright安装成功"
    else
        echo "❌ playwright安装失败"
        exit 1
    fi
fi

# 检查tk_chat_automation.py是否存在
if [ ! -f "tk_chat_automation.py" ]; then
    echo "❌ 错误: tk_chat_automation.py文件不存在"
    echo "请确保在正确的目录中运行此脚本"
    exit 1
fi

echo "✅ 环境检查完成"
echo "🚀 启动TK Chat Automation..."
echo ""

# 使用conda环境的Python直接运行脚本
$HOME/miniforge3/envs/tk_chat/bin/python3 tk_chat_automation.py

echo ""
echo "📋 脚本执行完成"
echo "📁 检查以下目录获取结果:"
echo "   - 截图: ./screenshots"
echo "   - 对话记录: ./results"  
echo "   - 日志: ./logs"
