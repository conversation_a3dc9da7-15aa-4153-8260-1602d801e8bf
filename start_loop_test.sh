#!/bin/bash

# TK聊天机器人循环压力测试启动脚本
# 使用方法：./start_loop_test.sh [进程数] [重启延迟] [最大重试次数]

set -e

# 默认参数
DEFAULT_CONCURRENT=5
DEFAULT_RESTART_DELAY=15
DEFAULT_MAX_RETRIES=-1

# 获取参数
CONCURRENT=${1:-$DEFAULT_CONCURRENT}
RESTART_DELAY=${2:-$DEFAULT_RESTART_DELAY}
MAX_RETRIES=${3:-$DEFAULT_MAX_RETRIES}

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}================================================${NC}"
echo -e "${GREEN}    TK聊天机器人循环压力测试启动器${NC}"
echo -e "${GREEN}================================================${NC}"
echo ""

# 检查必要文件
echo -e "${BLUE}检查必要文件...${NC}"

if [ ! -f "stress_test.py" ]; then
    echo -e "${RED}错误: 找不到 stress_test.py 文件${NC}"
    exit 1
fi

if [ ! -f "tk_chat_automation.py" ]; then
    echo -e "${RED}错误: 找不到 tk_chat_automation.py 文件${NC}"
    exit 1
fi

if [ ! -f "questions.json" ]; then
    echo -e "${YELLOW}警告: 找不到 questions.json 文件，将使用默认问题${NC}"
fi

if [ ! -f "tickets_config.json" ]; then
    echo -e "${YELLOW}警告: 找不到 tickets_config.json 文件，将使用默认URL${NC}"
fi

echo -e "${GREEN}✓ 文件检查完成${NC}"
echo ""

# 显示配置
echo -e "${BLUE}压测配置:${NC}"
echo -e "  并发进程数: ${YELLOW}${CONCURRENT}${NC}"
echo -e "  重启延迟: ${YELLOW}${RESTART_DELAY}秒${NC}"
echo -e "  最大重试: ${YELLOW}$([ $MAX_RETRIES -eq -1 ] && echo '无限' || echo $MAX_RETRIES)${NC}"
echo -e "  测试模式: ${YELLOW}启用${NC} (每个进程测试1个问题)"
echo ""

# 确认启动
echo -e "${YELLOW}即将启动循环压力测试，按 Ctrl+C 可随时停止${NC}"
echo -e "${YELLOW}5秒后开始启动...${NC}"
sleep 5

echo -e "${GREEN}启动循环压力测试...${NC}"
echo ""

# 构建命令
CMD="python stress_test.py --concurrent $CONCURRENT --test-mode --loop --restart-delay $RESTART_DELAY"

if [ $MAX_RETRIES -ne -1 ]; then
    CMD="$CMD --max-retries $MAX_RETRIES"
fi

# 如果有webhook配置，添加通知
if [ -n "$WEBHOOK_URL" ]; then
    CMD="$CMD --webhook-url $WEBHOOK_URL"
fi

echo -e "${BLUE}执行命令: ${CMD}${NC}"
echo ""

# 执行命令
exec $CMD 