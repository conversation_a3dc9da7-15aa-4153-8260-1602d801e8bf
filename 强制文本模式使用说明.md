# 强制文本模式功能使用说明

## 📋 功能概述

新增 `--force-text-mode` 参数，当启用后，脚本将在第一次对话完成后自动切换到文本模式，然后继续后续的对话。

## 🎯 适用场景

- 需要在文本模式下进行测试，但页面默认是数字人模式
- 想要测试文本模式下的输入框和发送按钮功能
- 对比数字人模式和文本模式的回复效果

## 🚀 使用方法

### 基本使用

```bash
# 启用强制文本模式
python3 tk_chat_automation.py --force-text-mode

# 结合测试模式使用
python3 tk_chat_automation.py --test-mode --force-text-mode

# 指定问题文件并启用强制文本模式
python3 tk_chat_automation.py --questions-file questions.json --force-text-mode

# 结合其他参数使用
python3 tk_chat_automation.py --test-mode --questions-file test_questions.json --access-mode random --force-text-mode
```

### 压力测试中使用

```bash
# 在压力测试中启用强制文本模式
python3 stress_test.py --concurrent 2 --questions-file questions.json --loop --force-text-mode
```

## 🔧 工作流程

1. **脚本启动** - 加载页面，进入默认的聊天模式（通常是数字人模式）
2. **第一次对话** - 发送第一个问题，等待回复完成
3. **模式切换检测** - 检测到第一次对话成功完成
4. **自动切换** - 查找并点击文本模式按钮
5. **等待稳定** - 等待模式切换完成
6. **继续对话** - 使用文本模式继续后续对话

## 📝 日志输出

启用强制文本模式后，你会看到以下日志：

```
2025-07-23 12:00:00,123 - INFO - 🎯 启用强制文本模式: 将在第一次对话后切换到文本模式
...
2025-07-23 12:01:30,456 - INFO - 🎯 第一次对话完成，开始执行强制文本模式切换...
2025-07-23 12:01:32,789 - INFO - 🔄 正在切换到文本模式...
2025-07-23 12:01:33,012 - INFO - 尝试文本模式按钮选择器 1/5: img[alt="文本"]
2025-07-23 12:01:33,234 - INFO - ✅ 通过图片找到文本模式按钮: img[alt="文本"]
2025-07-23 12:01:33,567 - INFO - ✅ 成功点击文本模式按钮
2025-07-23 12:01:35,890 - INFO - 📸 文本模式切换后截图: text_mode_switched_120135.png
2025-07-23 12:01:36,123 - INFO - ✅ 强制文本模式切换成功
```

## 🔍 技术实现

### 文本模式按钮识别

脚本使用以下选择器来识别文本模式按钮：

```python
text_mode_selectors = [
    'img[alt="文本"]',                      # 基于alt属性
    'img[src*="text_model"]',               # 基于src路径
    'img[src*="text_model.66434ac9.png"]',  # 完整路径匹配
    'button img[alt="文本"]',               # 包含图片的按钮
    'button:has(img[alt="文本"])'           # Playwright专用选择器
]
```

### 智能按钮点击

- 对于 `img` 选择器，自动查找包含该图片的父级 `button` 元素
- 对于 `button` 选择器，直接点击按钮
- 支持多重回退机制，确保在不同页面结构下都能成功切换

### 状态验证

- 点击前验证按钮可见性和可点击性
- 点击后截图记录切换状态
- 等待模式切换完全生效后再继续

## 📸 截图记录

切换过程中会自动保存以下截图：

- `text_mode_switched_HHMMSS.png` - 成功切换后的页面状态
- `text_mode_error_HHMMSS.png` - 切换失败时的页面状态

## ⚠️ 注意事项

1. **页面加载时间**: 确保页面完全加载后再启动脚本
2. **网络稳定性**: 模式切换需要稳定的网络连接
3. **按钮可见性**: 如果页面结构变化导致文本模式按钮无法找到，切换会失败但不会终止脚本
4. **兼容性**: 该功能与所有现有参数兼容，可以安全地与其他功能组合使用

## 🛠️ 故障排查

### 如果文本模式切换失败

1. **检查页面结构** - 确认页面上确实存在文本模式切换按钮
2. **查看截图** - 检查错误截图，确认页面状态
3. **查看日志** - 检查详细的选择器尝试日志
4. **手动验证** - 手动访问页面确认按钮是否可点击

### 常见问题

**Q: 切换失败但脚本继续运行？**
A: 这是正常行为。切换失败不会终止脚本，会继续使用当前模式进行对话。

**Q: 可以在已经是文本模式的页面上使用吗？**
A: 可以。如果页面已经是文本模式，脚本会尝试切换但不会影响正常对话流程。

**Q: 切换是否影响对话计数？**
A: 不影响。模式切换是在第一次对话完成后进行的，不会影响对话轮次统计。

## 📊 使用效果

启用强制文本模式后，你可以：

- 测试文本模式下的新HTML结构（如图片发送按钮、回复格式等）
- 对比数字人模式和文本模式的回复差异
- 确保脚本在两种模式下都能正常工作
- 验证文本模式专用的选择器和逻辑

---

**更新时间**: 2025-07-23  
**版本**: v4.0 - 强制文本模式功能  
**文件**: `tk_chat_automation.py` 