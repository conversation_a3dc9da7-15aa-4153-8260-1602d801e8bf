# AI回答评测功能使用说明

## 功能概述

AI回答评测功能是一个可选的增强功能，可以自动对AI数字人的回答质量进行多维度评分。该功能使用独立的AI模型（如本地Ollama）对每个AI回答进行专业评测，从理解力、记忆力、专业性、情商话术、知识储备、合规性等多个维度给出详细评分和改进建议。

## 功能特点

- ✅ **多维度评测**：从6个专业维度评估AI回答质量
- ✅ **独立配置**：使用独立的AI模型进行评测，不影响原有功能
- ✅ **可选启用**：通过配置文件控制是否启用评测功能
- ✅ **详细报告**：提供综合评分、各维度评分、评价总结和改进建议
- ✅ **多种API支持**：支持Ollama、OpenAI、自定义API
- ✅ **自动集成**：评测结果自动记录到对话日志和报告中

## 评测维度

1. **理解力** (1-5分)：AI是否准确理解了用户的问题意图、需求和上下文
2. **记忆力** (1-5分)：AI是否能够记住和引用之前的对话内容，保持对话连贯性
3. **专业性** (1-5分)：AI在时尚领域的专业知识是否准确、权威、深入
4. **情商话术** (1-5分)：AI的表达是否得体、亲和、有感染力，能否恰当处理用户情绪
5. **知识储备** (1-5分)：AI回答中体现的知识面是否广泛、信息是否丰富实用
6. **合规性** (1-5分)：AI回答是否合规合法，无有害内容，符合行业标准

## 配置说明

### 1. 启用评测功能

在 `ai_config.json` 文件中设置：

```json
{
    "ai_evaluation_config": {
        "enabled": true,  // 设置为true启用评测功能
        "default_provider": "ollama_eval",
        "providers": {
            "ollama_eval": {
                "provider": "ollama",
                "model": "qwen2.5:7b",  // 评测使用的模型
                "base_url": "http://localhost:11434",
                "temperature": 0.3,  // 较低温度确保评测一致性
                "max_tokens": 1500,
                "timeout": 60
            }
        }
    }
}
```

### 2. 评测设置

```json
{
    "evaluation_settings": {
        "score_scale": 5,  // 评分量表（1-5分）
        "evaluation_dimensions": [  // 评测维度
            "理解力",
            "记忆力", 
            "专业性",
            "情商话术",
            "知识储备",
            "合规性"
        ],
        "domain": "时尚",  // 评测领域
        "retry_attempts": 2  // 失败重试次数
    }
}
```

## 使用方法

### 自动评测

当启用评测功能后，系统会在每次获取到AI回答后自动进行评测：

1. 系统获取AI数字人的回答
2. 自动调用评测API进行质量评估
3. 将评测结果记录到对话日志中
4. 评测结果包含在最终的HTML和Excel报告中

### 手动测试

可以使用提供的测试脚本验证评测功能：

```bash
# 完整功能测试
python test_evaluation.py

# 简化测试
python simple_eval_test.py
```

## 评测结果示例

### 对话日志中的评测结果

```
问: 我想买一件适合春天穿的外套，有什么推荐吗？
答: 春天的话，我推荐您选择轻薄的风衣或者针织开衫...
🔍 AI回答评测:
综合评分: 4.2/5
理解力: 5/5 - 完全理解了用户对春季外套的需求
记忆力: 4/5 - 能够结合对话上下文进行回答
专业性: 4/5 - 提供了专业的时尚建议和搭配知识
情商话术: 4/5 - 表达亲和，询问用户偏好体现了良好的服务意识
知识储备: 4/5 - 展现了丰富的服装搭配知识
合规性: 5/5 - 回答内容完全合规，无任何问题
评价总结: AI回答在专业性和理解力方面表现优秀，能够准确理解用户需求并提供专业建议
改进建议: 可以更具体地询问用户的身材特点、预算范围等，提供更个性化的推荐
```

### JSON格式的评测结果

```json
{
    "overall_score": 4.2,
    "detailed_scores": {
        "理解力": {"score": 5, "reason": "完全理解了用户对春季外套的需求"},
        "记忆力": {"score": 4, "reason": "能够结合对话上下文进行回答"},
        "专业性": {"score": 4, "reason": "提供了专业的时尚建议和搭配知识"},
        "情商话术": {"score": 4, "reason": "表达亲和，询问用户偏好体现了良好的服务意识"},
        "知识储备": {"score": 4, "reason": "展现了丰富的服装搭配知识"},
        "合规性": {"score": 5, "reason": "回答内容完全合规，无任何问题"}
    },
    "evaluation_summary": "AI回答在专业性和理解力方面表现优秀...",
    "suggestions": "可以更具体地询问用户的身材特点、预算范围等..."
}
```

## API提供商配置

### Ollama（推荐）

```json
"ollama_eval": {
    "provider": "ollama",
    "model": "qwen2.5:7b",  // 或其他可用模型
    "base_url": "http://localhost:11434",
    "temperature": 0.3,
    "max_tokens": 1500,
    "timeout": 60
}
```

### OpenAI

```json
"openai_eval": {
    "provider": "openai",
    "model": "gpt-4",
    "api_key": "${OPENAI_API_KEY}",  // 环境变量
    "base_url": "https://api.openai.com/v1",
    "temperature": 0.3,
    "max_tokens": 1500,
    "timeout": 60
}
```

### 自定义API

```json
"custom_eval": {
    "provider": "custom",
    "url": "http://your-api.com/evaluate",
    "headers": {
        "Content-Type": "application/json",
        "Authorization": "Bearer your-token"
    },
    "payload_template": {
        "prompt": "",
        "max_tokens": 1500,
        "temperature": 0.3
    },
    "timeout": 60
}
```

## 注意事项

1. **性能影响**：评测功能会增加每次对话的处理时间（通常1-10秒）
2. **模型要求**：建议使用7B以上参数的模型以获得更准确的评测结果
3. **网络连接**：确保评测API服务可正常访问
4. **成本考虑**：使用付费API（如OpenAI）时注意成本控制
5. **可选功能**：可随时通过配置文件启用或禁用评测功能

## 故障排除

### 常见问题

1. **评测功能未启用**
   - 检查 `ai_config.json` 中 `enabled` 是否为 `true`
   - 确认配置文件格式正确

2. **API调用失败**
   - 检查网络连接和API服务状态
   - 验证模型名称是否正确
   - 检查API密钥或认证信息

3. **评测超时**
   - 增加 `timeout` 配置值
   - 使用更快的模型或API服务
   - 检查网络延迟

4. **评测结果解析失败**
   - 检查模型输出格式
   - 尝试不同的模型或调整温度参数

### 测试命令

```bash
# 检查Ollama服务状态
curl -s http://localhost:11434/api/tags

# 测试评测功能
python simple_eval_test.py

# 完整功能测试
python test_evaluation.py
```

## 更新日志

- **v1.0.0** (2024-12-XX)
  - 初始版本发布
  - 支持多维度AI回答评测
  - 集成Ollama、OpenAI、自定义API
  - 自动生成评测报告 