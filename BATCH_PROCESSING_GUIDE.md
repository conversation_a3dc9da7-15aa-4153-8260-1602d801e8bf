# Chrome批量启动器 - 分批处理指南

## 🚀 分批处理功能

为了避免一次性启动大量浏览器导致系统卡顿，脚本支持**分批处理**功能：

- 📦 **分批启动** - 每批启动指定数量的浏览器
- 🔄 **自动切换** - 启动下一批时自动关闭上一批
- 💾 **节省资源** - 同时只运行一批浏览器，避免内存不足
- 🎯 **专注操作** - 减少干扰，专注处理当前批次

## 📋 使用方法

### 基本语法

```bash
python3 batch_chrome_simple_mobile.py [URL文件] [设备类型] [批次大小]
```

### 参数说明

- **URL文件**: URL列表文件路径（默认: `test_urls_pre.txt`）
- **设备类型**: 移动设备类型（默认: `iPhone 13 Pro`）
- **批次大小**: 每批启动的浏览器数量（默认: `3`）

### 使用示例

```bash
# 默认配置 - 每批3个浏览器
python3 batch_chrome_simple_mobile.py

# 小批次测试 - 每批2个浏览器
python3 batch_chrome_simple_mobile.py test_urls_small.txt "iPhone 13 Pro" 2

# 大批次处理 - 每批5个浏览器
python3 batch_chrome_simple_mobile.py test_urls_pre.txt "iPad Pro" 5

# 一次性启动（不分批）- 批次大小设为很大的数
python3 batch_chrome_simple_mobile.py test_urls_small.txt "iPhone 13 Pro" 100
```

## 🎮 交互命令

启动后可使用以下命令：

| 命令 | 简写 | 功能 |
|------|------|------|
| `next` | `n` | 启动下一批浏览器（自动关闭上一批） |
| `status` | `s` | 查看当前进程状态 |
| `quit` | `q` | 关闭所有浏览器并退出 |
| `help` | `h` | 显示帮助信息 |

## 📊 操作流程示例

### 1. 启动脚本

```bash
$ python3 batch_chrome_simple_mobile.py test_urls_pre.txt "iPhone 13 Pro" 3

=== Chrome批量启动器 (移动设备模拟版 - 分批处理) ===
URL文件: test_urls_pre.txt
找到 40 个URL
模拟设备: iPhone 13 Pro
设备配置: 390x844 @ 3x
分批处理: 每批 3 个浏览器，共 14 批
```

### 2. 第一批启动

```bash
=== 启动第 1/14 批 ===
URL范围: 1-3
启动浏览器 1/40: https://example1.com
启动浏览器 2/40: https://example2.com
启动浏览器 3/40: https://example3.com

✓ 第 1/14 批启动完成
本批启动了 3 个浏览器
当前运行 3 个浏览器
```

### 3. 启动下一批（自动关闭上一批）

```bash
请输入命令 (还有 13 批待启动，输入 'next' 启动下一批): next

关闭上一批的 3 个浏览器...
✓ 已关闭上一批的 3 个浏览器

=== 启动第 2/14 批 ===
URL范围: 4-6
启动浏览器 4/40: https://example4.com
启动浏览器 5/40: https://example5.com
启动浏览器 6/40: https://example6.com

✓ 第 2/14 批启动完成
本批启动了 3 个浏览器
当前运行 3 个浏览器 (已自动关闭上一批)
```

### 4. 查看状态

```bash
请输入命令 (还有 12 批待启动，输入 'next' 启动下一批): status

进程状态检查:
------------------------------------------------------------
✓ 运行中 - PID 12345: URL 4: https://example4.com
✓ 运行中 - PID 12346: URL 5: https://example5.com
✓ 运行中 - PID 12347: URL 6: https://example6.com
------------------------------------------------------------
总计: 3 个进程，3 个正在运行
```

## 💡 最佳实践

### 批次大小建议

| 系统配置 | 建议批次大小 | 说明 |
|----------|--------------|------|
| 8GB内存以下 | 2-3个 | 保守配置，确保系统流畅 |
| 8-16GB内存 | 3-5个 | 平衡配置，适合大多数情况 |
| 16GB内存以上 | 5-8个 | 高性能配置，提高处理效率 |

### 使用技巧

1. **先用小文件测试** - 使用 `test_urls_small.txt` 测试配置
2. **合理设置批次大小** - 根据系统性能和URL数量调整
3. **及时处理当前批次** - 完成操作后立即切换到下一批
4. **监控系统资源** - 使用 `status` 命令查看进程状态

### 故障排除

**问题**: 浏览器启动失败
- **解决**: 减小批次大小，检查系统资源

**问题**: 切换批次时卡顿
- **解决**: 等待上一批完全关闭后再操作

**问题**: 进程无法关闭
- **解决**: 使用 `Ctrl+C` 强制退出，或手动关闭浏览器

## 🔧 高级配置

### 快速启动脚本

使用 `quick_start.sh` 快速选择配置：

```bash
./quick_start.sh

请选择启动方式:
1. 小测试 (3个URL) - iPhone 13 Pro - 一次性启动
2. 小测试 (3个URL) - iPad Pro - 一次性启动
3. 完整测试 (40个URL) - iPhone 13 Pro - 分批启动 (每批3个)
4. 完整测试 (40个URL) - iPad Pro - 分批启动 (每批3个)
5. 完整测试 (40个URL) - iPhone 13 Pro - 分批启动 (每批5个)
6. 自定义
```

### 自定义URL文件

创建自己的URL文件：

```bash
# my_urls.txt
# 注释行会被忽略
https://example1.com
https://example2.com

# 空行也会被忽略
https://example3.com
```

然后使用：

```bash
python3 batch_chrome_simple_mobile.py my_urls.txt "iPad Pro" 4
```

## 📈 性能对比

| 模式 | 内存使用 | CPU使用 | 系统响应 | 适用场景 |
|------|----------|---------|----------|----------|
| 一次性启动40个 | 很高 | 很高 | 卡顿 | 高性能服务器 |
| 分批启动(每批3个) | 低 | 低 | 流畅 | 普通电脑 |
| 分批启动(每批5个) | 中等 | 中等 | 较流畅 | 中高配置电脑 |

## 🎯 总结

分批处理功能让Chrome批量启动器更加实用和高效：

- ✅ **资源友好** - 避免系统过载
- ✅ **操作简便** - 一键切换批次
- ✅ **灵活配置** - 可调整批次大小
- ✅ **自动管理** - 无需手动关闭上一批

现在你可以轻松处理大量URL，而不用担心系统卡顿！
