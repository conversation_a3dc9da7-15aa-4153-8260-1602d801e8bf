#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速测试强制文本模式功能
用于验证修复后的自适应发送按钮选择器
"""

import subprocess
import sys
import os
import json

def create_test_questions():
    """创建测试问题文件"""
    questions = {
        "questions": [
            {"text": "你好，请介绍一下太古汇"},
            {"text": "有什么好吃的推荐吗？"}
        ]
    }
    
    with open("test_questions_force_text.json", "w", encoding="utf-8") as f:
        json.dump(questions, f, ensure_ascii=False, indent=2)
    
    print("✅ 已创建测试问题文件: test_questions_force_text.json")
    return "test_questions_force_text.json"

def run_test():
    """运行强制文本模式测试"""
    print("🚀 开始强制文本模式测试")
    print("=" * 50)
    
    # 创建测试问题
    questions_file = create_test_questions()
    
    # 测试命令
    cmd = [
        sys.executable, 
        'tk_chat_automation.py',
        '--test-mode',
        '--force-text-mode',
        '--questions-file', questions_file
    ]
    
    print("📋 测试命令:")
    print(" ".join(cmd))
    print("\n🎯 测试目标:")
    print("1. 第一次对话后自动切换到文本模式")
    print("2. 使用自适应发送按钮选择器")
    print("3. 验证文本模式下的发送逻辑")
    print("\n⏰ 开始执行测试...")
    print("=" * 50)
    
    try:
        # 运行测试
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # 实时显示输出
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                print(output.strip())
        
        # 获取返回码
        return_code = process.poll()
        
        print("\n" + "=" * 50)
        if return_code == 0:
            print("✅ 测试完成，返回码: 0")
        else:
            print(f"⚠️ 测试完成，返回码: {return_code}")
        
        return return_code
        
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断测试")
        return 1
    except Exception as e:
        print(f"\n❌ 测试执行出错: {e}")
        return 1
    finally:
        # 清理测试文件
        try:
            if os.path.exists(questions_file):
                os.remove(questions_file)
                print(f"🧹 已清理测试文件: {questions_file}")
        except:
            pass

def main():
    """主函数"""
    print("🔧 强制文本模式修复验证测试")
    print("目的: 验证自适应发送按钮选择器是否解决了超时问题")
    print()
    
    # 检查文件是否存在
    if not os.path.exists('tk_chat_automation.py'):
        print("❌ 找不到 tk_chat_automation.py 文件")
        return 1
    
    # 运行测试
    return_code = run_test()
    
    print("\n📊 测试总结:")
    if return_code == 0:
        print("🎉 测试成功完成！强制文本模式功能已修复。")
        print("🔧 修复内容:")
        print("  - 添加了自适应发送按钮查找函数")
        print("  - 支持文本模式的新选择器")
        print("  - 替换了所有旧的固定选择器")
    else:
        print("⚠️ 测试未正常完成，请检查日志输出")
    
    return return_code

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 