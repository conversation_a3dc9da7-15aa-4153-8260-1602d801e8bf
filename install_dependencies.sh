#!/bin/bash

# Chrome批量启动器依赖安装脚本

echo "=== Chrome批量启动器依赖安装 ==="

# 检查Python版本
echo "检查Python版本..."
if command -v python3 &> /dev/null; then
    PYTHON_VERSION=$(python3 --version 2>&1 | awk '{print $2}')
    echo "找到Python: $PYTHON_VERSION"
else
    echo "错误: 未找到Python3，请先安装Python 3.8+"
    exit 1
fi

# 检查pip
echo "检查pip..."
if command -v pip3 &> /dev/null; then
    echo "找到pip3"
elif command -v pip &> /dev/null; then
    echo "找到pip"
else
    echo "错误: 未找到pip，请先安装pip"
    exit 1
fi

# 安装Python依赖
echo "安装Python依赖..."
if [ -f "requirements_batch_launcher.txt" ]; then
    pip3 install -r requirements_batch_launcher.txt
    if [ $? -eq 0 ]; then
        echo "✓ Python依赖安装成功"
    else
        echo "✗ Python依赖安装失败"
        exit 1
    fi
else
    echo "错误: 未找到requirements_batch_launcher.txt文件"
    exit 1
fi

# 安装Playwright浏览器
echo "安装Playwright浏览器..."
playwright install chromium
if [ $? -eq 0 ]; then
    echo "✓ Playwright浏览器安装成功"
else
    echo "✗ Playwright浏览器安装失败"
    exit 1
fi

# 设置脚本执行权限
echo "设置脚本执行权限..."
chmod +x batch_chrome_launcher.sh
chmod +x install_dependencies.sh

echo ""
echo "=== 安装完成 ==="
echo "现在可以使用以下命令启动批量浏览器："
echo "  python3 batch_chrome_launcher.py"
echo "  或"
echo "  ./batch_chrome_launcher.sh"
echo ""
echo "使用 'python3 batch_chrome_launcher.py --help' 查看更多选项"
