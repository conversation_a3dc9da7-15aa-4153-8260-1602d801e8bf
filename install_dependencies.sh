#!/bin/bash

# Chrome批量启动器依赖检查脚本

echo "=== Chrome批量启动器依赖检查 ==="

# 检查Python版本
echo "检查Python版本..."
if command -v python3 &> /dev/null; then
    PYTHON_VERSION=$(python3 --version 2>&1 | awk '{print $2}')
    echo "✓ 找到Python: $PYTHON_VERSION"
else
    echo "✗ 未找到Python3，请先安装Python 3.6+"
    exit 1
fi

# 检查Chrome浏览器
echo "检查Chrome浏览器..."
CHROME_FOUND=false

# macOS
if [ -f "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome" ]; then
    echo "✓ 找到Chrome浏览器 (macOS)"
    CHROME_FOUND=true
# Linux
elif command -v google-chrome &> /dev/null; then
    echo "✓ 找到Chrome浏览器 (Linux)"
    CHROME_FOUND=true
elif command -v chromium-browser &> /dev/null; then
    echo "✓ 找到Chromium浏览器 (Linux)"
    CHROME_FOUND=true
# Windows WSL
elif [ -f "/mnt/c/Program Files/Google/Chrome/Application/chrome.exe" ]; then
    echo "✓ 找到Chrome浏览器 (Windows)"
    CHROME_FOUND=true
fi

if [ "$CHROME_FOUND" = false ]; then
    echo "✗ 未找到Chrome浏览器，请先安装Google Chrome或Chromium"
    echo "  macOS: 从 https://www.google.com/chrome/ 下载安装"
    echo "  Linux: sudo apt install google-chrome-stable 或 sudo apt install chromium-browser"
    exit 1
fi

# 设置脚本执行权限
echo "设置脚本执行权限..."
chmod +x batch_chrome_launcher.sh
chmod +x install_dependencies.sh

echo ""
echo "=== 检查完成 ==="
echo "✓ 所有依赖都已就绪！"
echo ""
echo "现在可以使用以下命令启动批量浏览器："
echo "  python3 batch_chrome_launcher.py                    # 使用默认设备 (iPhone 13 Pro)"
echo "  python3 batch_chrome_launcher.py test_urls_pre.txt \"iPad Pro\"  # 指定设备类型"
echo "  ./batch_chrome_launcher.sh                          # 使用Bash版本"
echo ""
echo "支持的设备类型: iPhone 13 Pro, iPhone 12, iPad Pro, Samsung Galaxy S21"
