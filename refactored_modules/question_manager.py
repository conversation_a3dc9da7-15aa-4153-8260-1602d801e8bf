"""
问题管理模块
负责问题加载、生成、调度等功能
"""

import os
import json
import random
import logging
import csv


def load_questions(questions_file, question_type=None):
    """
    从文件加载问题列表
    
    Args:
        questions_file: 问题文件路径
        question_type: 问题类型过滤
        
    Returns:
        list: 问题列表
    """
    try:
        if not os.path.exists(questions_file):
            logging.warning(f"问题文件不存在: {questions_file}")
            return []
        
        questions = []
        
        # 根据文件扩展名选择加载方式
        if questions_file.endswith('.json'):
            questions = load_questions_from_json(questions_file, question_type)
        elif questions_file.endswith('.csv'):
            questions = load_questions_from_csv(questions_file, question_type)
        elif questions_file.endswith('.txt'):
            questions = load_questions_from_txt(questions_file)
        else:
            logging.error(f"不支持的问题文件格式: {questions_file}")
            return []
        
        if question_type:
            questions = [q for q in questions if q.get('type') == question_type]
        
        logging.info(f"成功加载 {len(questions)} 个问题 from {questions_file}")
        return questions
        
    except Exception as e:
        logging.error(f"加载问题文件失败: {questions_file}, 错误: {str(e)}")
        return []


def load_questions_from_json(json_file, question_type=None):
    """从JSON文件加载问题"""
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if isinstance(data, list):
            return data
        elif isinstance(data, dict):
            if 'questions' in data:
                return data['questions']
            elif question_type and question_type in data:
                return data[question_type]
            else:
                # 如果是按类型分组的字典，合并所有问题
                questions = []
                for key, value in data.items():
                    if isinstance(value, list):
                        for q in value:
                            if isinstance(q, str):
                                questions.append({'text': q, 'type': key})
                            elif isinstance(q, dict):
                                q['type'] = q.get('type', key)
                                questions.append(q)
                return questions
        
        return []
        
    except Exception as e:
        logging.error(f"解析JSON问题文件失败: {str(e)}")
        return []


def load_questions_from_csv(csv_file, question_type=None):
    """从CSV文件加载问题"""
    try:
        questions = []
        
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                question = {
                    'text': row.get('question', row.get('text', '')),
                    'type': row.get('type', 'default'),
                    'category': row.get('category', ''),
                    'difficulty': row.get('difficulty', 'normal'),
                    'expected_answer': row.get('expected_answer', ''),
                    'tags': row.get('tags', '').split(',') if row.get('tags') else []
                }
                questions.append(question)
        
        return questions
        
    except Exception as e:
        logging.error(f"解析CSV问题文件失败: {str(e)}")
        return []


def load_questions_from_txt(txt_file):
    """从TXT文件加载问题"""
    try:
        questions = []
        
        with open(txt_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line and not line.startswith('#'):  # 跳过空行和注释
                    questions.append({
                        'text': line,
                        'type': 'text',
                        'line_number': line_num
                    })
        
        return questions
        
    except Exception as e:
        logging.error(f"解析TXT问题文件失败: {str(e)}")
        return []


def get_next_questions(questions, count=5, method='random', used_questions=None):
    """
    获取下一批问题
    
    Args:
        questions: 问题列表
        count: 获取数量
        method: 获取方式 ('random', 'sequential', 'weighted')
        used_questions: 已使用的问题集合
        
    Returns:
        list: 选中的问题列表
    """
    try:
        if not questions:
            return []
        
        if used_questions is None:
            used_questions = set()
        
        # 过滤已使用的问题
        available_questions = []
        for i, q in enumerate(questions):
            question_id = get_question_id(q, i)
            if question_id not in used_questions:
                available_questions.append((i, q))
        
        if not available_questions:
            logging.warning("所有问题都已使用，重置使用记录")
            available_questions = list(enumerate(questions))
        
        # 根据方法选择问题
        if method == 'random':
            selected = random.sample(available_questions, min(count, len(available_questions)))
        elif method == 'sequential':
            selected = available_questions[:count]
        elif method == 'weighted':
            selected = get_weighted_questions(available_questions, count)
        else:
            logging.warning(f"未知的问题获取方法: {method}，使用随机方式")
            selected = random.sample(available_questions, min(count, len(available_questions)))
        
        result_questions = [q for _, q in selected]
        
        # 更新已使用问题集合
        for i, q in selected:
            question_id = get_question_id(q, i)
            used_questions.add(question_id)
        
        logging.info(f"获取了 {len(result_questions)} 个问题，方法: {method}")
        return result_questions
        
    except Exception as e:
        logging.error(f"获取下一批问题失败: {str(e)}")
        return []


def get_question_id(question, index):
    """获取问题的唯一标识"""
    if isinstance(question, dict):
        return question.get('id', f"{index}_{hash(question.get('text', ''))}")
    else:
        return f"{index}_{hash(str(question))}"


def get_weighted_questions(available_questions, count):
    """根据权重选择问题"""
    try:
        # 计算权重
        weighted_questions = []
        for i, q in available_questions:
            weight = 1.0  # 默认权重
            
            if isinstance(q, dict):
                # 根据问题属性调整权重
                difficulty = q.get('difficulty', 'normal')
                if difficulty == 'easy':
                    weight = 0.5
                elif difficulty == 'hard':
                    weight = 2.0
                
                # 根据问题类型调整权重
                q_type = q.get('type', 'default')
                if q_type == 'important':
                    weight *= 1.5
                elif q_type == 'optional':
                    weight *= 0.8
                
                # 如果有优先级设置
                priority = q.get('priority', 5)  # 1-10，5为默认
                weight *= (priority / 5.0)
            
            weighted_questions.append((i, q, weight))
        
        # 按权重排序并选择
        weighted_questions.sort(key=lambda x: x[2], reverse=True)
        selected = weighted_questions[:count]
        
        return [(i, q) for i, q, _ in selected]
        
    except Exception as e:
        logging.error(f"加权选择问题失败: {str(e)}")
        # 回退到随机选择
        return random.sample(available_questions, min(count, len(available_questions)))


def get_next_question_unified(questions, question_index=0, method='sequential'):
    """
    统一的问题获取接口
    
    Args:
        questions: 问题列表
        question_index: 当前问题索引
        method: 获取方式
        
    Returns:
        tuple: (question, new_index)
    """
    try:
        if not questions:
            return None, 0
        
        if method == 'sequential':
            if question_index >= len(questions):
                return None, question_index  # 已到达末尾
            question = questions[question_index]
            return question, question_index + 1
        
        elif method == 'random':
            question = random.choice(questions)
            return question, question_index + 1
        
        elif method == 'loop':
            index = question_index % len(questions)
            question = questions[index]
            return question, question_index + 1
        
        else:
            logging.warning(f"未知的问题获取方法: {method}")
            return questions[0] if questions else None, 1
            
    except Exception as e:
        logging.error(f"获取问题失败: {str(e)}")
        return None, question_index


def format_question_for_execution(question, context=None):
    """
    格式化问题以供执行
    
    Args:
        question: 问题对象
        context: 上下文信息
        
    Returns:
        str: 格式化后的问题文本
    """
    try:
        if isinstance(question, str):
            question_text = question
        elif isinstance(question, dict):
            question_text = question.get('text', str(question))
        else:
            question_text = str(question)
        
        # 处理变量替换
        if context:
            for key, value in context.items():
                placeholder = f"{{{key}}}"
                if placeholder in question_text:
                    question_text = question_text.replace(placeholder, str(value))
        
        # 处理特殊标记
        question_text = question_text.strip()
        
        logging.debug(f"格式化问题: {question_text}")
        return question_text
        
    except Exception as e:
        logging.error(f"格式化问题失败: {str(e)}")
        return str(question) if question else ""


def save_questions_to_file(questions, output_file, format='json'):
    """
    保存问题到文件
    
    Args:
        questions: 问题列表
        output_file: 输出文件路径
        format: 保存格式 ('json', 'csv', 'txt')
    """
    try:
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        if format == 'json':
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(questions, f, ensure_ascii=False, indent=2)
        
        elif format == 'csv':
            with open(output_file, 'w', encoding='utf-8', newline='') as f:
                if questions:
                    fieldnames = set()
                    for q in questions:
                        if isinstance(q, dict):
                            fieldnames.update(q.keys())
                    
                    if not fieldnames:
                        fieldnames = ['text']
                    
                    writer = csv.DictWriter(f, fieldnames=list(fieldnames))
                    writer.writeheader()
                    
                    for q in questions:
                        if isinstance(q, dict):
                            writer.writerow(q)
                        else:
                            writer.writerow({'text': str(q)})
        
        elif format == 'txt':
            with open(output_file, 'w', encoding='utf-8') as f:
                for q in questions:
                    if isinstance(q, dict):
                        f.write(q.get('text', str(q)) + '\n')
                    else:
                        f.write(str(q) + '\n')
        
        else:
            logging.error(f"不支持的保存格式: {format}")
            return
        
        logging.info(f"成功保存 {len(questions)} 个问题到 {output_file}")
        
    except Exception as e:
        logging.error(f"保存问题到文件失败: {str(e)}")


def filter_questions(questions, filters):
    """
    根据条件过滤问题
    
    Args:
        questions: 问题列表
        filters: 过滤条件字典
        
    Returns:
        list: 过滤后的问题列表
    """
    try:
        filtered_questions = []
        
        for question in questions:
            match = True
            
            if isinstance(question, dict):
                for key, value in filters.items():
                    if key not in question:
                        match = False
                        break
                    
                    question_value = question[key]
                    
                    # 支持不同类型的过滤
                    if isinstance(value, str):
                        if value not in str(question_value):
                            match = False
                            break
                    elif isinstance(value, list):
                        if question_value not in value:
                            match = False
                            break
                    elif callable(value):
                        if not value(question_value):
                            match = False
                            break
                    else:
                        if question_value != value:
                            match = False
                            break
            
            if match:
                filtered_questions.append(question)
        
        logging.info(f"过滤后剩余 {len(filtered_questions)} 个问题")
        return filtered_questions
        
    except Exception as e:
        logging.error(f"过滤问题失败: {str(e)}")
        return questions


def shuffle_questions(questions, seed=None):
    """
    打乱问题顺序
    
    Args:
        questions: 问题列表
        seed: 随机种子
        
    Returns:
        list: 打乱后的问题列表
    """
    try:
        if seed is not None:
            random.seed(seed)
        
        shuffled = questions.copy()
        random.shuffle(shuffled)
        
        logging.info(f"已打乱 {len(shuffled)} 个问题的顺序")
        return shuffled
        
    except Exception as e:
        logging.error(f"打乱问题顺序失败: {str(e)}")
        return questions


class QuestionManager:
    """问题管理器"""
    
    def __init__(self, questions_file=None, config=None):
        """
        初始化问题管理器
        
        Args:
            questions_file: 问题文件路径
            config: 配置字典
        """
        self.questions_file = questions_file
        self.config = config or {}
        self.questions = []
        self.used_questions = set()
        self.current_index = 0
        
        if questions_file:
            self.load_questions()
    
    def load_questions(self, questions_file=None):
        """加载问题"""
        if questions_file:
            self.questions_file = questions_file
        
        if self.questions_file:
            self.questions = load_questions(self.questions_file)
            self.reset_usage()
    
    def get_next_question(self, method=None):
        """获取下一个问题"""
        if method is None:
            method = self.config.get('selection_method', 'sequential')
        
        question, new_index = get_next_question_unified(
            self.questions, 
            self.current_index, 
            method
        )
        
        self.current_index = new_index
        return question
    
    def get_multiple_questions(self, count=5, method=None):
        """获取多个问题"""
        if method is None:
            method = self.config.get('selection_method', 'random')
        
        return get_next_questions(
            self.questions, 
            count, 
            method, 
            self.used_questions
        )
    
    def reset_usage(self):
        """重置使用记录"""
        self.used_questions.clear()
        self.current_index = 0
    
    def add_question(self, question):
        """添加问题"""
        self.questions.append(question)
    
    def remove_question(self, question_id):
        """删除问题"""
        self.questions = [q for q in self.questions if get_question_id(q, 0) != question_id]
    
    def filter_questions(self, filters):
        """过滤问题"""
        self.questions = filter_questions(self.questions, filters)
    
    def shuffle_questions(self, seed=None):
        """打乱问题顺序"""
        self.questions = shuffle_questions(self.questions, seed)
    
    def save_questions(self, output_file=None, format='json'):
        """保存问题"""
        if output_file is None:
            output_file = self.questions_file
        
        if output_file:
            save_questions_to_file(self.questions, output_file, format)
    
    def get_stats(self):
        """获取统计信息"""
        return {
            'total_questions': len(self.questions),
            'used_questions': len(self.used_questions),
            'remaining_questions': len(self.questions) - len(self.used_questions),
            'current_index': self.current_index
        } 