"""
日志工具模块
负责日志设置、网络请求记录、调试信息收集等功能
"""

import os
import logging
import logging.handlers
import json
import traceback
import time
from datetime import datetime
import glob


def setup_logging(log_level=logging.INFO, log_file=None, max_bytes=10*1024*1024, backup_count=5):
    """
    设置日志配置
    
    Args:
        log_level: 日志级别
        log_file: 日志文件路径
        max_bytes: 单个日志文件最大大小
        backup_count: 备份文件数量
    """
    try:
        # 创建root logger
        logger = logging.getLogger()
        logger.setLevel(log_level)
        
        # 清除已有的handlers
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # 创建formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 添加控制台handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(log_level)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # 添加文件handler（如果指定了文件路径）
        if log_file:
            # 确保日志目录存在
            os.makedirs(os.path.dirname(log_file), exist_ok=True)
            
            # 使用RotatingFileHandler自动轮转日志
            file_handler = logging.handlers.RotatingFileHandler(
                log_file,
                maxBytes=max_bytes,
                backupCount=backup_count,
                encoding='utf-8'
            )
            file_handler.setLevel(log_level)
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
            
            logging.info(f"日志系统初始化完成，日志文件: {log_file}")
        else:
            logging.info("日志系统初始化完成，仅输出到控制台")
        
    except Exception as e:
        print(f"设置日志系统失败: {str(e)}")


def setup_console_logger(name="console", level=logging.INFO):
    """
    设置专用的控制台日志记录器
    
    Args:
        name: 日志记录器名称
        level: 日志级别
        
    Returns:
        logging.Logger: 日志记录器对象
    """
    try:
        logger = logging.getLogger(name)
        logger.setLevel(level)
        
        # 如果已有handler，先清除
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # 创建控制台handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(level)
        
        # 设置特殊格式用于控制台输出
        formatter = logging.Formatter(
            '%(asctime)s [%(levelname)s] %(message)s',
            datefmt='%H:%M:%S'
        )
        console_handler.setFormatter(formatter)
        
        logger.addHandler(console_handler)
        logger.propagate = False  # 防止重复输出
        
        return logger
        
    except Exception as e:
        print(f"设置控制台日志记录器失败: {str(e)}")
        return logging.getLogger()


def log_console_message(message, level=logging.INFO, logger_name="console"):
    """
    记录控制台消息
    
    Args:
        message: 消息内容
        level: 日志级别
        logger_name: 日志记录器名称
    """
    try:
        logger = logging.getLogger(logger_name)
        logger.log(level, message)
    except Exception as e:
        print(f"记录控制台消息失败: {str(e)}")


def log_network_request(method, url, headers=None, data=None, response_time=None):
    """
    记录网络请求信息
    
    Args:
        method: HTTP方法
        url: 请求URL
        headers: 请求头
        data: 请求数据
        response_time: 响应时间
    """
    try:
        logger = logging.getLogger("network")
        
        # 构建请求信息
        request_info = {
            "timestamp": datetime.now().isoformat(),
            "method": method,
            "url": url,
            "response_time": response_time
        }
        
        # 添加请求头（过滤敏感信息）
        if headers:
            safe_headers = {}
            for key, value in headers.items():
                if key.lower() in ['authorization', 'cookie', 'x-api-key']:
                    safe_headers[key] = "***"
                else:
                    safe_headers[key] = value
            request_info["headers"] = safe_headers
        
        # 添加请求数据（截断过长的数据）
        if data:
            if isinstance(data, (dict, list)):
                data_str = json.dumps(data, ensure_ascii=False)
            else:
                data_str = str(data)
            
            # 截断过长的数据
            if len(data_str) > 1000:
                request_info["data"] = data_str[:1000] + "... (truncated)"
            else:
                request_info["data"] = data_str
        
        logger.info(f"网络请求: {json.dumps(request_info, ensure_ascii=False)}")
        
    except Exception as e:
        logging.error(f"记录网络请求失败: {str(e)}")


def log_network_response(status_code, response_data=None, response_time=None, error=None):
    """
    记录网络响应信息
    
    Args:
        status_code: HTTP状态码
        response_data: 响应数据
        response_time: 响应时间
        error: 错误信息
    """
    try:
        logger = logging.getLogger("network")
        
        # 构建响应信息
        response_info = {
            "timestamp": datetime.now().isoformat(),
            "status_code": status_code,
            "response_time": response_time
        }
        
        if error:
            response_info["error"] = str(error)
            logger.error(f"网络响应错误: {json.dumps(response_info, ensure_ascii=False)}")
        else:
            # 添加响应数据（截断过长的数据）
            if response_data:
                if isinstance(response_data, (dict, list)):
                    data_str = json.dumps(response_data, ensure_ascii=False)
                else:
                    data_str = str(response_data)
                
                # 截断过长的数据
                if len(data_str) > 1000:
                    response_info["data"] = data_str[:1000] + "... (truncated)"
                else:
                    response_info["data"] = data_str
            
            logger.info(f"网络响应: {json.dumps(response_info, ensure_ascii=False)}")
        
    except Exception as e:
        logging.error(f"记录网络响应失败: {str(e)}")


def collect_debug_information(error=None, context=None):
    """
    收集调试信息
    
    Args:
        error: 错误对象
        context: 上下文信息
        
    Returns:
        dict: 调试信息字典
    """
    try:
        debug_info = {
            "timestamp": datetime.now().isoformat(),
            "python_version": None,
            "system_info": {},
            "memory_usage": None,
            "traceback": None,
            "context": context or {}
        }
        
        # 获取Python版本
        import sys
        debug_info["python_version"] = sys.version
        
        # 获取系统信息
        import platform
        debug_info["system_info"] = {
            "system": platform.system(),
            "release": platform.release(),
            "machine": platform.machine(),
            "processor": platform.processor()
        }
        
        # 获取内存使用情况
        try:
            import psutil
            process = psutil.Process()
            debug_info["memory_usage"] = {
                "rss": process.memory_info().rss,
                "vms": process.memory_info().vms,
                "percent": process.memory_percent()
            }
        except ImportError:
            debug_info["memory_usage"] = "psutil not available"
        
        # 获取错误堆栈
        if error:
            debug_info["error"] = {
                "type": type(error).__name__,
                "message": str(error),
                "traceback": traceback.format_exc()
            }
        
        return debug_info
        
    except Exception as e:
        logging.error(f"收集调试信息失败: {str(e)}")
        return {"error": "Failed to collect debug information"}


def find_latest_log_file(log_dir="logs", pattern="*.log"):
    """
    查找最新的日志文件
    
    Args:
        log_dir: 日志目录
        pattern: 文件名模式
        
    Returns:
        str: 最新日志文件路径，如果没有找到则返回None
    """
    try:
        if not os.path.exists(log_dir):
            return None
        
        # 查找匹配的日志文件
        search_pattern = os.path.join(log_dir, pattern)
        log_files = glob.glob(search_pattern)
        
        if not log_files:
            return None
        
        # 按修改时间排序，返回最新的
        latest_file = max(log_files, key=os.path.getmtime)
        return latest_file
        
    except Exception as e:
        logging.error(f"查找最新日志文件失败: {str(e)}")
        return None


def archive_old_logs(log_dir="logs", days_old=30, archive_dir=None):
    """
    归档旧的日志文件
    
    Args:
        log_dir: 日志目录
        days_old: 归档天数阈值
        archive_dir: 归档目录，None时删除文件
        
    Returns:
        int: 归档的文件数量
    """
    try:
        if not os.path.exists(log_dir):
            return 0
        
        from datetime import datetime, timedelta
        
        # 计算阈值时间
        threshold_time = datetime.now() - timedelta(days=days_old)
        
        archived_count = 0
        for filename in os.listdir(log_dir):
            filepath = os.path.join(log_dir, filename)
            
            # 跳过目录
            if not os.path.isfile(filepath):
                continue
            
            # 检查文件修改时间
            file_mtime = datetime.fromtimestamp(os.path.getmtime(filepath))
            
            if file_mtime < threshold_time:
                if archive_dir:
                    # 移动到归档目录
                    os.makedirs(archive_dir, exist_ok=True)
                    archive_path = os.path.join(archive_dir, filename)
                    os.rename(filepath, archive_path)
                    logging.info(f"日志文件已归档: {filepath} -> {archive_path}")
                else:
                    # 直接删除
                    os.remove(filepath)
                    logging.info(f"日志文件已删除: {filepath}")
                
                archived_count += 1
        
        if archived_count > 0:
            logging.info(f"共归档/删除了 {archived_count} 个旧日志文件")
        
        return archived_count
        
    except Exception as e:
        logging.error(f"归档旧日志文件失败: {str(e)}")
        return 0


def get_log_stats(log_file):
    """
    获取日志文件统计信息
    
    Args:
        log_file: 日志文件路径
        
    Returns:
        dict: 统计信息
    """
    try:
        if not os.path.exists(log_file):
            return {"error": "日志文件不存在"}
        
        stats = {
            "file_size": os.path.getsize(log_file),
            "line_count": 0,
            "error_count": 0,
            "warning_count": 0,
            "info_count": 0,
            "debug_count": 0,
            "creation_time": datetime.fromtimestamp(os.path.getctime(log_file)).isoformat(),
            "modification_time": datetime.fromtimestamp(os.path.getmtime(log_file)).isoformat()
        }
        
        # 分析日志内容
        with open(log_file, 'r', encoding='utf-8') as f:
            for line in f:
                stats["line_count"] += 1
                
                line_upper = line.upper()
                if " ERROR " in line_upper:
                    stats["error_count"] += 1
                elif " WARNING " in line_upper or " WARN " in line_upper:
                    stats["warning_count"] += 1
                elif " INFO " in line_upper:
                    stats["info_count"] += 1
                elif " DEBUG " in line_upper:
                    stats["debug_count"] += 1
        
        return stats
        
    except Exception as e:
        logging.error(f"获取日志统计信息失败: {str(e)}")
        return {"error": str(e)}


class PerformanceLogger:
    """性能日志记录器"""
    
    def __init__(self, logger_name="performance"):
        self.logger = logging.getLogger(logger_name)
        self.start_times = {}
    
    def start(self, operation_name):
        """开始计时"""
        self.start_times[operation_name] = time.time()
        self.logger.info(f"开始执行: {operation_name}")
    
    def end(self, operation_name, success=True, details=None):
        """结束计时"""
        if operation_name not in self.start_times:
            self.logger.warning(f"未找到操作的开始时间: {operation_name}")
            return
        
        duration = time.time() - self.start_times[operation_name]
        del self.start_times[operation_name]
        
        status = "成功" if success else "失败"
        message = f"执行完成: {operation_name} - {status} - 耗时: {duration:.2f}秒"
        
        if details:
            message += f" - 详情: {details}"
        
        if success:
            self.logger.info(message)
        else:
            self.logger.error(message)
        
        return duration
    
    def measure(self, operation_name):
        """装饰器：自动测量函数执行时间"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                self.start(operation_name)
                try:
                    result = func(*args, **kwargs)
                    self.end(operation_name, success=True)
                    return result
                except Exception as e:
                    self.end(operation_name, success=False, details=str(e))
                    raise
            return wrapper
        return decorator


class StructuredLogger:
    """结构化日志记录器"""
    
    def __init__(self, logger_name="structured", log_file=None):
        self.logger = logging.getLogger(logger_name)
        
        if log_file and not self.logger.handlers:
            # 添加文件handler
            handler = logging.FileHandler(log_file, encoding='utf-8')
            formatter = logging.Formatter('%(message)s')  # 只输出消息内容
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def log_event(self, event_type, data, level=logging.INFO):
        """记录结构化事件"""
        try:
            event = {
                "timestamp": datetime.now().isoformat(),
                "event_type": event_type,
                "data": data
            }
            
            message = json.dumps(event, ensure_ascii=False)
            self.logger.log(level, message)
            
        except Exception as e:
            logging.error(f"记录结构化事件失败: {str(e)}")
    
    def log_conversation(self, conversation_id, question, answer, metadata=None):
        """记录对话事件"""
        data = {
            "conversation_id": conversation_id,
            "question": question,
            "answer": answer
        }
        
        if metadata:
            data["metadata"] = metadata
        
        self.log_event("conversation", data)
    
    def log_error(self, error_type, error_message, context=None):
        """记录错误事件"""
        data = {
            "error_type": error_type,
            "error_message": error_message,
            "context": context or {}
        }
        
        self.log_event("error", data, level=logging.ERROR) 