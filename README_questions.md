# Questions.json 配置文件说明

## 新的统一数据结构

新版本的 `questions.json` 使用统一的数据结构，通过 `question_type` 字段来区分不同类型的问题。

### 数据结构说明

```json
{
    "questions": [
        {
            "question_type": "single",
            "content": "单个问题文本"
        },
        {
            "question_type": "multi",
            "content": [
                "多轮对话问题1",
                "多轮对话问题2", 
                "多轮对话问题3"
            ]
        },
        {
            "question_type": "image_text",
            "content": {
                "text": "图文问题的文本内容",
                "image": "图片路径或URL",
                "imageType": "url" // 或 "local"
            }
        },
        {
            "question_type": "ai_generated",
            "content": {
                "domain": "问题领域",
                "intent_type": "意图类型",
                "example_question": "示例问题",
                "count": 5,
                "api_config": {
                    "provider": "ollama",
                    "model": "qwen2.5:7b",
                    "base_url": "http://localhost:11434"
                }
            }
        }
    ],
    "mode": "sequential" // 或 "random"
}
```

### 问题类型详解

#### 1. single - 单问题
- **用途**: 单轮对话，一个问题一个回答
- **格式**: `content` 直接是字符串

#### 2. multi - 多轮问题  
- **用途**: 多轮连续对话
- **格式**: `content` 是字符串数组，按顺序发送

#### 3. image_text - 图文问题
- **用途**: 包含图片的问题
- **格式**: `content` 是对象，包含：
  - `text`: 问题文本
  - `image`: 图片路径或URL
  - `imageType`: "url" 表示网络图片，"local" 表示本地文件

#### 4. ai_generated - AI生成问题 🆕
- **用途**: 使用AI模型动态生成问题变种
- **格式**: `content` 是对象，包含：
  - `domain`: 问题领域（如"时尚领域"、"美食推荐"）
  - `intent_type`: 意图类型（如"否定意图"、"特殊需求"）
  - `example_question`: 参考示例问题
  - `count`: 生成问题数量
  - `api_config`: API配置信息

### AI生成问题配置详解

#### API提供商支持
1. **Ollama (本地模型)**
```json
"api_config": {
    "provider": "ollama",
    "model": "qwen2.5:7b",
    "base_url": "http://localhost:11434"
}
```

2. **OpenAI API**
```json
"api_config": {
    "provider": "openai",
    "model": "gpt-3.5-turbo",
    "api_key": "your-api-key",
    "base_url": "https://api.openai.com/v1"
}
```

3. **自定义API**
```json
"api_config": {
    "provider": "custom",
    "url": "http://your-api-endpoint",
    "headers": {
        "Authorization": "Bearer your-token"
    },
    "payload_template": {
        "prompt": "{{prompt}}"
    }
}
```

#### 领域和意图类型示例
- **时尚领域**
  - 否定意图: "推荐xx，但不要xx颜色"
  - 特殊场合: "适合商务/运动/约会的服装"
  - 价格敏感: "平价/高端的xx推荐"

- **美食推荐**
  - 饮食限制: "不辣/素食/无麸质的xx"
  - 特殊需求: "适合老人/小孩的xx"
  - 地域偏好: "正宗/改良版的xx菜"

- **购物咨询**
  - 功能需求: "实用/美观/性价比高的xx"
  - 品牌偏好: "除了xx品牌外的xx推荐"

### 图片路径说明

#### 本地图片
- 相对路径: `"./test_images/sample.jpg"`
- 绝对路径: `"/Users/<USER>/images/sample.jpg"`

#### 网络图片  
- HTTP/HTTPS URL: `"https://example.com/image.jpg"`

### 兼容性

脚本同时支持新的统一格式和旧的分类格式：

#### 旧格式（仍然支持）
```json
{
    "single_questions": ["问题1", "问题2"],
    "multi_questions": [["对话1问题1", "对话1问题2"]],
    "image_text_questions": [{"text": "图文问题", "image": "path", "imageType": "local"}],
    "mode": "sequential"
}
```

### 访问模式

- **sequential**: 按顺序执行所有问题
- **random**: 随机选择问题执行

### AI生成问题的优势

1. **动态多样性**: 每次运行都能生成新的问题变种
2. **意图保持**: 保持原有的意图类型和领域特征
3. **测试覆盖**: 增加测试场景的覆盖面
4. **智能后备**: AI生成失败时自动使用示例问题
5. **元数据记录**: 完整记录生成过程和参数

### 使用建议

1. 优先使用新的统一格式，便于管理和扩展
2. 图文问题的图片建议先上传到稳定的图片服务
3. 本地图片请确保路径正确且文件存在
4. AI生成问题需要确保API服务可用
5. 建议先使用少量问题验证API和生成效果
6. 可以混合使用多种问题类型获得最佳测试效果

### 环境要求

#### 使用Ollama
- 安装Ollama: `curl -fsSL https://ollama.ai/install.sh | sh`
- 拉取模型: `ollama pull qwen2.5:7b`
- 启动服务: `ollama serve`

#### 使用OpenAI
- 获取API密钥
- 配置网络访问（如需代理）

### 示例配置

查看项目根目录的 `questions.json` 文件获取完整示例。

# 对话机器人问题配置指南

## 概述

本系统支持四种问题类型：
1. **单轮问题** (`single`) - 单独的问题
2. **多轮问题** (`multi`) - 连续的多个问题
3. **图文问题** (`image_text`) - 包含图片的问题 
4. **AI生成问题** (`ai_generated`) - 使用AI动态生成的问题

## 5. AI生成问题报告功能

### 5.1 报告中的AI生成问题展示

当使用AI生成问题类型时，系统会在HTML和Excel报告中特别标识这些问题：

#### HTML报告特性：
- **🤖 AI生成问题**：紫色徽章标识AI生成的问题
- **⚠️ 备用问题**：橙色徽章标识AI生成失败时使用的示例问题
- **元数据信息**：显示问题领域和意图类型
- **统计数据**：在报告顶部正确统计AI生成问题数量

#### Excel报告特性：
- **问题类型列**：明确标识"🤖 AI生成问题"或"🤖⚠️ AI生成(备用)"
- **详细信息**：在问题内容中包含AI生成的领域和意图信息
- **颜色编码**：
  - AI生成问题：紫色背景(#f3e6ff)
  - 备用问题：橙色背景(#fff3e0)
  - 图文问题：红色背景(#ffe6e6)
  - 普通文本：蓝色背景(#e6f3ff)

### 5.2 报告统计说明

报告会自动统计和展示：
- 总对话轮数
- 总问题数
- **AI生成问题数**（新增）
- 图文问题数
- 普通文本问题数

示例统计显示：
```
总计 5 轮对话，15 个问题（其中AI生成问题 8 个，图文问题 3 个，普通文本问题 4 个）
```

### 5.3 对话日志格式

AI生成问题在对话日志中的记录格式：
```
问: 帮我推荐一件适合运动的衣服，我不喜欢黑色
AI生成问题 - 领域: 时尚领域, 意图: 否定意图
答: [AI回复内容]
回复等待时长: 3.2秒
截图: [截图URL]
```

如果AI生成失败使用备用问题：
```
问: 帮我推荐一件适合运动的衣服，我不喜欢黑色
AI生成问题 - 领域: 时尚领域, 意图: 否定意图
备注: AI生成失败，使用示例问题
答: [AI回复内容]
```

### 5.4 报告访问

- **HTML报告**：在浏览器中打开，支持图片点击放大预览
- **Excel报告**：在Excel中打开，支持数据筛选和排序
- **OSS存储**：两种格式的报告都会自动上传到OSS并在钉钉通知中提供下载链接 

## 4. AI生成问题类型 (`ai_generated`)

AI生成问题类型允许系统调用AI模型动态生成问题变种，支持多种API提供商。

### 4.1 基本配置格式

```json
{
    "question_type": "ai_generated",
    "content": {
        "domain": "时尚领域",
        "intent_type": "否定意图", 
        "example_question": "帮我推荐一件适合运动的衣服，我不喜欢黑色",
        "count": 5,
        "provider": "ollama"
    }
}
```

### 4.2 配置参数说明

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `domain` | string | 是 | 问题领域，如"时尚领域"、"美食推荐"、"旅行咨询" |
| `intent_type` | string | 是 | 意图类型，如"否定意图"、"特殊需求"、"比较咨询" |
| `example_question` | string | 是 | 示例问题，AI将基于此生成变种 |
| `count` | integer | 否 | 生成问题数量，默认3个，最大10个 |
| `provider` | string | 否 | API提供商名称，不指定时使用默认配置 |

### 4.3 API配置管理

API配置现在统一管理在 `ai_config.json` 文件中，而不是在questions.json中配置。

#### 配置文件结构

创建 `ai_config.json` 文件：

```json
{
    "ai_generation_config": {
        "default_provider": "ollama",
        "providers": {
            "ollama": {
                "provider": "ollama",
                "model": "qwen2.5:7b",
                "base_url": "http://localhost:11434",
                "temperature": 0.7,
                "max_tokens": 1000,
                "timeout": 120
            },
            "openai": {
                "provider": "openai",
                "model": "gpt-3.5-turbo",
                "api_key": "${OPENAI_API_KEY}",
                "base_url": "https://api.openai.com/v1",
                "temperature": 0.7,
                "max_tokens": 1000,
                "timeout": 120
            },
            "custom": {
                "provider": "custom",
                "url": "http://your-custom-api.com/generate",
                "headers": {
                    "Content-Type": "application/json",
                    "Authorization": "Bearer your-token"
                },
                "payload_template": {
                    "prompt": "",
                    "max_tokens": 1000,
                    "temperature": 0.7
                },
                "timeout": 120
            }
        }
    },
    "generation_settings": {
        "default_count": 3,
        "max_count": 10,
        "fallback_to_example": true,
        "retry_attempts": 2
    }
}
```

#### 环境变量支持

API密钥等敏感信息支持环境变量：
- `"api_key": "${OPENAI_API_KEY}"` 会自动读取环境变量 `OPENAI_API_KEY`
- 如果环境变量不存在，该字段将为空

#### 提供商配置

- **默认提供商**：在 `default_provider` 中设置
- **问题级指定**：在questions.json中可选地指定 `"provider": "提供商名称"`
- **自动降级**：如果指定的提供商不存在，自动使用默认提供商

### 4.4 配置示例

#### 使用默认提供商
```json
{
    "question_type": "ai_generated",
    "content": {
        "domain": "时尚领域",
        "intent_type": "否定意图",
        "example_question": "帮我推荐一件适合运动的衣服，我不喜欢黑色",
        "count": 5
    }
}
```

#### 指定特定提供商
```json
{
    "question_type": "ai_generated", 
    "content": {
        "domain": "美食推荐",
        "intent_type": "特殊需求",
        "example_question": "推荐一些不辣的川菜，我不能吃花椒",
        "count": 3,
        "provider": "openai"
    }
}
```

### 4.5 环境配置

#### Ollama配置
1. 安装并启动Ollama服务
2. 下载模型：`ollama pull qwen2.5:7b`
3. 确认服务运行在 http://localhost:11434

#### OpenAI配置
1. 设置环境变量：`export OPENAI_API_KEY="your-api-key"`
2. 或直接在配置文件中替换 `${OPENAI_API_KEY}`

#### 自定义API配置
1. 修改 `ai_config.json` 中的 `custom` 配置
2. 根据实际API接口调整 `url`、`headers` 和 `payload_template`

### 4.6 优势

- **配置分离**：API配置与问题配置分离，便于环境管理
- **灵活性**：支持问题级提供商指定
- **安全性**：敏感信息支持环境变量
- **可扩展**：易于添加新的API提供商
- **兼容性**：向后兼容，如果没有配置文件会使用默认配置 

## AI追问对话类型

AI追问对话是一种智能多轮对话类型，能够根据用户的回复动态生成后续问题，实现真正的交互式对话体验。

### 基本配置

```json
{
    "question_type": "ai_followup",
    "content": {
        "domain": "领域名称",
        "scenario": "对话场景描述",
        "initial_question": "初始问题",
        "rounds": 5,
        "provider": "ollama"
    }
}
```

### 配置参数说明

- **domain**: 对话领域，如"旅行规划"、"购物咨询"、"美食推荐"等
- **scenario**: 对话场景描述，帮助AI理解上下文
- **initial_question**: 对话的初始问题
- **rounds**: 对话轮次配置（详见下方说明）
- **provider**: AI提供商，可选"ollama"、"openai"或"custom"（可选）

### 轮次配置选项

#### 1. 固定轮次
```json
"rounds": 5
```
进行固定5轮对话。

#### 2. 随机轮次
```json
"rounds": {
    "type": "random",
    "min": 3,
    "max": 8
}
```
随机进行3-8轮对话。

#### 3. AI智能判断
```json
"rounds": {
    "type": "ai_judge",
    "max": 15
}
```
由AI判断何时结束对话，最多15轮。

### 完整配置示例

```json
{
    "questions": [
        {
            "question_type": "ai_followup",
            "content": {
                "domain": "旅行规划",
                "scenario": "用户咨询旅行建议",
                "initial_question": "我想去日本旅游，有什么推荐吗？",
                "rounds": 5,
                "provider": "ollama"
            }
        },
        {
            "question_type": "ai_followup",
            "content": {
                "domain": "购物咨询",
                "scenario": "用户寻求购买建议",
                "initial_question": "我想买一台笔记本电脑",
                "rounds": {
                    "type": "random",
                    "min": 3,
                    "max": 8
                }
            }
        },
        {
            "question_type": "ai_followup",
            "content": {
                "domain": "美食推荐",
                "scenario": "用户咨询餐厅和菜品",
                "initial_question": "有没有好吃的川菜餐厅推荐？",
                "rounds": {
                    "type": "ai_judge",
                    "max": 15
                }
            }
        }
    ]
}
```

### 工作原理

1. **初始问题**: 系统发送配置的初始问题
2. **用户回复**: 等待用户回复
3. **AI分析**: AI分析用户回复和对话历史
4. **生成追问**: 基于分析结果生成有针对性的追问
5. **判断结束**: 根据轮次配置或AI判断决定是否结束
6. **循环进行**: 重复步骤2-5直到对话结束

### 报告显示

在HTML和Excel报告中，AI追问对话会被特殊标记：
- 🔄 AI追问对话 标识
- 显示领域、场景和轮次信息
- 完整的对话历史追踪

### 优势特点

- **智能追问**: 根据用户回复动态生成问题
- **上下文理解**: 维护完整的对话历史
- **灵活配置**: 支持多种轮次控制方式
- **专业领域**: 可针对特定领域优化
- **自然对话**: 生成自然、有针对性的问题

### 使用建议

1. **领域专业化**: 为不同业务领域设置专门的配置
2. **场景细化**: 详细描述对话场景以提高AI理解
3. **轮次合理**: 根据业务需求选择合适的轮次配置
4. **问题优化**: 初始问题应该开放且易于回答

## 使用示例

下面是一个完整的questions.json示例：

```json
{
    "questions": [
        {
            "question_type": "single",
            "content": "单个问题文本"
        },
        {
            "question_type": "multi",
            "content": [
                "多轮对话问题1",
                "多轮对话问题2", 
                "多轮对话问题3"
            ]
        },
        {
            "question_type": "image_text",
            "content": {
                "text": "图文问题的文本内容",
                "image": "图片路径或URL",
                "imageType": "url" // 或 "local"
            }
        },
        {
            "question_type": "ai_generated",
            "content": {
                "domain": "问题领域",
                "intent_type": "意图类型",
                "example_question": "示例问题",
                "count": 5,
                "api_config": {
                    "provider": "ollama",
                    "model": "qwen2.5:7b",
                    "base_url": "http://localhost:11434"
                }
            }
        }
    ],
    "mode": "sequential" // 或 "random"
}
``` 