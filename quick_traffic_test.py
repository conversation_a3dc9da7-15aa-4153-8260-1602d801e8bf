#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速流量测试工具
快速诊断Android流量监控问题

使用方法: python3 quick_traffic_test.py
"""

import subprocess
import sys

def run_adb_command(cmd, timeout=10):
    """执行ADB命令"""
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=timeout)
        return result.returncode, result.stdout, result.stderr
    except Exception as e:
        return -1, "", str(e)

def main():
    print("🔍 快速流量监控诊断")
    print("=" * 40)
    
    # 1. 检查ADB
    print("1. 检查ADB连接...")
    returncode, stdout, stderr = run_adb_command(["adb", "devices"])
    if returncode != 0:
        print(f"❌ ADB不可用: {stderr}")
        return
    
    devices = [line.split('\t')[0] for line in stdout.strip().split('\n')[1:] if '\tdevice' in line]
    if not devices:
        print("❌ 没有连接的设备")
        return
    
    device_id = devices[0]
    print(f"✅ 设备已连接: {device_id}")
    
    # 2. 获取微信包名（常见应用）
    print("\n2. 查找微信应用...")
    returncode, stdout, stderr = run_adb_command(["adb", "-s", device_id, "shell", "pm", "list", "packages", "tencent.mm"])
    
    if returncode == 0 and "com.tencent.mm" in stdout:
        package_name = "com.tencent.mm"
        print(f"✅ 找到微信: {package_name}")
    else:
        print("⚠️  未找到微信，尝试查找其他应用...")
        returncode, stdout, stderr = run_adb_command(["adb", "-s", device_id, "shell", "pm", "list", "packages", "-3"])
        if returncode == 0:
            lines = stdout.strip().split('\n')
            packages = [line.replace('package:', '').strip() for line in lines if line.startswith('package:')]
            if packages:
                package_name = packages[0]
                print(f"✅ 使用第一个第三方应用: {package_name}")
            else:
                print("❌ 没有找到可测试的应用")
                return
        else:
            print("❌ 无法获取应用列表")
            return
    
    # 3. 获取UID
    print(f"\n3. 获取 {package_name} 的UID...")
    returncode, stdout, stderr = run_adb_command(["adb", "-s", device_id, "shell", "pm", "list", "packages", "-U", package_name])
    
    uid = None
    if returncode == 0 and stdout.strip():
        lines = stdout.strip().split('\n')
        for line in lines:
            if f'package:{package_name}' in line and 'uid:' in line:
                parts = line.split('uid:')
                if len(parts) > 1:
                    uid = int(parts[1].strip())
                    break
    
    if uid is None:
        print("❌ 无法获取UID")
        return
    
    print(f"✅ UID: {uid}")
    
    # 4. 测试各种流量获取方法
    print(f"\n4. 测试流量获取方法...")
    methods_success = 0
    
    # 方法1: /proc/uid_stat/
    print("   测试 /proc/uid_stat/ 方法...")
    returncode, stdout, stderr = run_adb_command(["adb", "-s", device_id, "shell", "cat", f"/proc/uid_stat/{uid}/tcp_rcv"])
    if returncode == 0 and stdout.strip():
        rx_bytes = int(stdout.strip())
        print(f"   ✅ RX: {rx_bytes} bytes ({rx_bytes/1024/1024:.2f} MB)")
        methods_success += 1
    else:
        print(f"   ❌ 失败: {stderr}")
    
    # 方法2: dumpsys netstats
    print("   测试 dumpsys netstats 方法...")
    returncode, stdout, stderr = run_adb_command(["adb", "-s", device_id, "shell", "dumpsys", "netstats", "detail"], timeout=15)
    if returncode == 0:
        found = False
        for line in stdout.split('\n'):
            if f'uid={uid}' in line:
                print(f"   ✅ 找到: {line.strip()}")
                found = True
                methods_success += 1
                break
        if not found:
            print("   ❌ 未找到该UID的数据")
    else:
        print(f"   ❌ 失败: {stderr}")
    
    # 方法3: /proc/net/xt_qtaguid/stats
    print("   测试 xt_qtaguid 方法...")
    returncode, stdout, stderr = run_adb_command(["adb", "-s", device_id, "shell", "grep", str(uid), "/proc/net/xt_qtaguid/stats"])
    if returncode == 0 and stdout.strip():
        lines = stdout.strip().split('\n')
        print(f"   ✅ 找到 {len(lines)} 条数据")
        methods_success += 1
    else:
        print("   ❌ 未找到数据")
    
    # 5. 总结
    print(f"\n📊 测试结果: {methods_success}/3 种方法成功")
    
    if methods_success == 0:
        print("\n💡 可能的问题和解决方案:")
        print("1. 应用可能没有产生网络流量 - 请在应用中进行网络操作")
        print("2. 设备可能需要Root权限")
        print("3. Android版本可能不支持这些API")
        print("4. 尝试重启应用或设备")
        
        # 额外检查
        print("\n🔧 额外检查:")
        
        # 检查Root权限
        returncode, stdout, stderr = run_adb_command(["adb", "-s", device_id, "shell", "su", "-c", "id"])
        if returncode == 0 and "uid=0" in stdout:
            print("✅ 设备已Root")
        else:
            print("⚠️  设备未Root")
        
        # 检查Android版本
        returncode, stdout, stderr = run_adb_command(["adb", "-s", device_id, "shell", "getprop", "ro.build.version.release"])
        if returncode == 0:
            android_version = stdout.strip()
            print(f"📱 Android版本: {android_version}")
        
    else:
        print("\n✅ 流量监控应该可以正常工作!")
        print("如果主程序仍然显示0流量，请确保:")
        print("1. 应用正在运行")
        print("2. 应用正在使用网络")
        print("3. 监控间隔足够长以捕获流量变化")

if __name__ == "__main__":
    main() 