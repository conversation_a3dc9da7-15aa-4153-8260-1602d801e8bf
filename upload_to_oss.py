#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
上传文件到阿里云OSS
用法: python upload_to_oss.py [文件路径]
如果未提供文件路径，默认上传 tk-automation.zip
"""

import os
import sys
import time
import logging
import oss2
import random
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
# OSS配置
OSS_ACCESS_KEY_ID = 'LTAI5t6yLR9SawePy4TxhihE'
OSS_ACCESS_KEY_SECRET = '******************************'
OSS_ENDPOINT = "oss-cn-shanghai.aliyuncs.com"  # 根据实际地区修改
OSS_BUCKET_NAME = "cloudgame-test"
OSS_FOLDER = "tk-automation"  # OSS中的目录

def ensure_folder_exists(bucket, folder_path):
    """确保OSS中文件夹存在"""
    if not folder_path.endswith('/'):
        folder_path += '/'
    
    try:
        # 检查目录是否存在，修复ListObjectsResult对象非可迭代问题
        exist = False
        for obj in oss2.ObjectIterator(bucket, prefix=folder_path, delimiter='/'):
            if obj.key == folder_path:
                exist = True
                break
        
        if not exist:
            # 创建一个空的目录标识对象
            bucket.put_object(folder_path, '')
            logging.info(f"已在OSS中创建目录: {folder_path}")
        else:
            logging.info(f"OSS目录已存在: {folder_path}")
        return True
    except Exception as e:
        logging.error(f"创建OSS目录时出错: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())
        return False

def upload_to_oss(file_path, max_retries=3):
    """上传文件到OSS"""
    if not os.path.exists(file_path):
        logging.error(f"文件不存在: {file_path}")
        return False
    
    # 支持的文件格式
    supported_formats = ['.zip', '.csv', '.txt', '.json', '.log', '.html', '.xml']
    file_ext = Path(file_path).suffix.lower()
    
    if file_ext not in supported_formats:
        logging.warning(f"文件格式 {file_ext} 可能不被支持: {file_path}")
        logging.info(f"支持的格式: {', '.join(supported_formats)}")
        response = input("是否继续上传? (y/n): ")
        if response.lower() != 'y':
            return False
    else:
        logging.info(f"检测到支持的文件格式: {file_ext}")
    
    file_size = os.path.getsize(file_path) / (1024 * 1024)  # 转换为MB
    logging.info(f"准备上传文件: {file_path} (大小: {file_size:.2f} MB)")
    
    try:
        # 创建OSS客户端
        auth = oss2.Auth(OSS_ACCESS_KEY_ID, OSS_ACCESS_KEY_SECRET)
        bucket = oss2.Bucket(auth, OSS_ENDPOINT, OSS_BUCKET_NAME)
        
        # 确保目录存在
        # if not ensure_folder_exists(bucket, OSS_FOLDER):
        #     return False
        
        # 生成OSS中的文件名
        file_name = os.path.basename(file_path)
        oss_path = f"{OSS_FOLDER}/{file_name}"
        
        # 上传文件，带重试逻辑
        retry_count = 0
        while retry_count <= max_retries:
            try:
                if retry_count > 0:
                    logging.info(f"第 {retry_count} 次重试上传...")
                
                logging.info(f"开始上传到 {oss_path}")
                start_time = time.time()
                
                # 使用进度条上传
                def progress_callback(bytes_uploaded, total_bytes):
                    if total_bytes:
                        rate = 100 * bytes_uploaded / total_bytes
                        sys.stdout.write(f"\r上传进度: {rate:.2f}% ({bytes_uploaded/(1024*1024):.2f}/{total_bytes/(1024*1024):.2f} MB)")
                        sys.stdout.flush()
                
                result = bucket.put_object_from_file(oss_path, file_path, progress_callback=progress_callback)
                
                # 检查上传结果
                if result.status == 200:
                    elapsed = time.time() - start_time
                    sys.stdout.write('\n')  # 换行
                    logging.info(f"上传成功! 耗时: {elapsed:.2f} 秒")
                    
                    # 计算文件URL
                    file_url = f"https://{OSS_BUCKET_NAME}.{OSS_ENDPOINT}/{oss_path}"
                    logging.info(f"文件URL: {file_url}")
                    
                    # 根据文件类型输出说明
                    file_ext = Path(file_path).suffix.lower()
                    if file_ext == '.zip':
                        # 输出Windows部署说明
                        print("\n=== Windows部署说明 ===")
                        print("1. 下载此ZIP文件并解压到本地目录")
                        print("2. 安装Python 3.8或更高版本 (如未安装)")
                        print("3. 双击 scripts/start_automation.bat 运行自动化脚本")
                        print("===============================")
                    elif file_ext == '.csv':
                        print("\n=== CSV文件上传完成 ===")
                        print("此CSV文件可用于数据分析或导入其他系统")
                        print("可以直接在浏览器中访问上述URL下载文件")
                        print("==========================")
                    else:
                        print(f"\n=== {file_ext.upper()}文件上传完成 ===")
                        print("文件已成功上传到云存储")
                        print("可以通过上述URL访问文件")
                        print("======================")
                    
                    return True
                else:
                    logging.error(f"上传失败，状态码: {result.status}")
            except Exception as e:
                logging.error(f"上传过程中出错: {str(e)}")
                import traceback
                logging.error(traceback.format_exc())
            
            # 增加重试次数
            retry_count += 1
            
            # 如果还有重试机会，等待一段时间再重试
            if retry_count <= max_retries:
                wait_time = 2 ** retry_count + random.uniform(0, 1)  # 指数退避策略
                logging.info(f"等待 {wait_time:.2f} 秒后重试...")
                time.sleep(wait_time)
        
        logging.error(f"上传失败，已达到最大重试次数 ({max_retries})")
        return False
        
    except Exception as e:
        logging.error(f"上传过程中出错: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())
        return False

def main():
    # 显示帮助信息
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help']:
        print("OSS文件上传工具")
        print("用法: python upload_to_oss.py [文件路径]")
        print("支持的文件格式: .zip, .csv, .txt, .json, .log, .html, .xml")
        print("示例:")
        print("  python upload_to_oss.py data.csv")
        print("  python upload_to_oss.py report.zip")
        return 0
    
    # 获取文件路径
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
    else:
        # 默认使用当前目录下的文件
        file_path = "tk-automation.zip"  # 默认文件
    
    file_path = os.path.abspath(file_path)
    
    # 确认文件存在
    if not os.path.exists(file_path):
        logging.error(f"文件不存在: {file_path}")
        return 1
    
    # 检查文件格式
    supported_formats = ['.zip', '.csv', '.txt', '.json', '.log', '.html', '.xml']
    file_ext = Path(file_path).suffix.lower()
    
    if file_ext not in supported_formats:
        logging.warning(f"文件格式 {file_ext} 可能不被支持: {file_path}")
        logging.info(f"支持的格式: {', '.join(supported_formats)}")
        response = input("是否继续上传? (y/n): ")
        if response.lower() != 'y':
            return 1
    else:
        logging.info(f"准备上传 {file_ext} 格式文件")
    
    # 上传文件
    if upload_to_oss(file_path):
        return 0
    else:
        return 1

if __name__ == "__main__":
    sys.exit(main()) 