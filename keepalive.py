#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
保活脚本 - 负责监控和管理自动化脚本的运行
功能：
1. 检测自动化脚本是否在运行，如果不在运行则启动
2. 响应重启、启动、停止指令（通过标记文件）
3. 支持暂停自动重启（通过暂停标记文件）
"""

import os
import sys
import time
import psutil
import logging
import datetime
import platform
import subprocess
import traceback
import shutil

# 配置
CHECK_INTERVAL = 30  # 检查间隔（秒）
MAX_RESTARTS = 10    # 最大重启次数
RESTART_INTERVAL = 60  # 重启间隔（秒）
SCRIPT_NAME = "tk_chat_automation.py"  # 要监控的脚本名称

# 标记文件
RESTART_FLAG_FILE = "restart_flag.txt"  # 重启标记文件
START_FLAG_FILE = "start_flag.txt"      # 启动标记文件
STOP_FLAG_FILE = "stop_flag.txt"        # 停止标记文件
PAUSE_FLAG_FILE = "pause_flag.txt"      # 暂停保活脚本标记文件

# 判断操作系统类型，选择合适的Python命令
PYTHON_CMD = "python3" if platform.system() != "Windows" else "python"

# 设置日志
def setup_logging():
    log_dir = os.path.join(os.getcwd(), 'logs')
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, f'keepalive_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )
    return log_file

# 获取脚本路径
def get_script_path():
    current_dir = os.path.dirname(os.path.abspath(__file__))
    script_path = os.path.join(current_dir, SCRIPT_NAME)
    return script_path

# 检查脚本是否运行
def is_script_running():
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['cmdline']:
                cmd_line = ' '.join(proc.info['cmdline'])
                if 'python' in cmd_line and SCRIPT_NAME in cmd_line:
                    return True, proc.info['pid']
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    return False, None

# 启动脚本
def start_script():
    script_path = get_script_path()
    if not os.path.exists(script_path):
        logging.error(f"无法找到脚本: {script_path}")
        return False
    
    try:
        logging.info(f"正在启动脚本: {script_path}")
        
        if platform.system() == "Windows":
            # Windows上使用不显示控制台的方式启动
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            subprocess.Popen(
                [PYTHON_CMD, script_path],
                startupinfo=startupinfo,
                cwd=os.path.dirname(script_path)
            )
        else:
            # Mac/Linux上使用nohup启动
            current_dir = os.path.dirname(script_path)
            cmd = f"cd {current_dir} && {PYTHON_CMD} {script_path} > /dev/null 2>&1 &"
            subprocess.Popen(cmd, shell=True)
        
        # 等待一段时间，检查是否成功启动
        time.sleep(5)
        running, pid = is_script_running()
        if running:
            logging.info(f"脚本启动成功，PID: {pid}")
            return True
        else:
            logging.error("脚本启动失败，未检测到运行进程")
            return False
            
    except Exception as e:
        logging.error(f"启动脚本时出错: {str(e)}")
        logging.error(traceback.format_exc())
        return False

# 停止脚本
def stop_script():
    running, pid = is_script_running()
    if not running:
        logging.info("脚本未运行，无需停止")
        return True
    
    try:
        logging.info(f"正在停止脚本，PID: {pid}")
        process = psutil.Process(pid)
        
        # 尝试优雅终止
        process.terminate()
        
        # 等待进程终止，最多等待10秒
        try:
            process.wait(timeout=10)
            logging.info(f"脚本已成功停止，PID: {pid}")
            return True
        except psutil.TimeoutExpired:
            # 如果超时，则强制终止
            logging.warning(f"脚本未能及时终止，正在强制终止，PID: {pid}")
            process.kill()
            time.sleep(2)
            
            # 再次检查是否已停止
            if not psutil.pid_exists(pid):
                logging.info(f"脚本已强制终止，PID: {pid}")
                return True
            else:
                logging.error(f"无法终止脚本，PID: {pid}")
                return False
    
    except Exception as e:
        logging.error(f"停止脚本时出错: {str(e)}")
        logging.error(traceback.format_exc())
        return False

# 检查并处理标记文件
def check_flag_files():
    # 检查停止标记
    if os.path.exists(STOP_FLAG_FILE):
        try:
            with open(STOP_FLAG_FILE, 'r') as f:
                content = f.read()
            logging.info(f"检测到停止标记文件，内容: \n{content}")
            
            # 执行停止操作
            if stop_script():
                logging.info("成功执行停止操作")
            else:
                logging.error("执行停止操作失败")
            
            # 备份并删除标记文件
            backup_and_remove_flag(STOP_FLAG_FILE)
            return True
        except Exception as e:
            logging.error(f"处理停止标记文件时出错: {str(e)}")
            logging.error(traceback.format_exc())
    
    # 检查重启标记
    if os.path.exists(RESTART_FLAG_FILE):
        try:
            with open(RESTART_FLAG_FILE, 'r') as f:
                content = f.read()
            logging.info(f"检测到重启标记文件，内容: \n{content}")
            
            # 先停止再启动
            stop_result = stop_script()
            if not stop_result:
                logging.warning("停止脚本失败，将尝试强制停止后启动")
            
            # 确保完全停止
            time.sleep(5)
            
            # 启动脚本
            if start_script():
                logging.info("成功执行重启操作")
            else:
                logging.error("执行重启操作失败")
            
            # 备份并删除标记文件
            backup_and_remove_flag(RESTART_FLAG_FILE)
            return True
        except Exception as e:
            logging.error(f"处理重启标记文件时出错: {str(e)}")
            logging.error(traceback.format_exc())
    
    # 检查启动标记
    if os.path.exists(START_FLAG_FILE):
        try:
            with open(START_FLAG_FILE, 'r') as f:
                content = f.read()
            logging.info(f"检测到启动标记文件，内容: \n{content}")
            
            # 检查脚本是否已经在运行
            running, pid = is_script_running()
            if running:
                logging.info(f"脚本已经在运行中，PID: {pid}，无需启动")
            else:
                # 启动脚本
                if start_script():
                    logging.info("成功执行启动操作")
                else:
                    logging.error("执行启动操作失败")
            
            # 备份并删除标记文件
            backup_and_remove_flag(START_FLAG_FILE)
            return True
        except Exception as e:
            logging.error(f"处理启动标记文件时出错: {str(e)}")
            logging.error(traceback.format_exc())
    
    return False

# 备份并删除标记文件
def backup_and_remove_flag(flag_file):
    try:
        # 确保备份目录存在
        backup_dir = os.path.join(os.getcwd(), 'logs', 'flags_backup')
        os.makedirs(backup_dir, exist_ok=True)
        
        # 创建备份文件名
        backup_file = os.path.join(
            backup_dir, 
            f"{os.path.basename(flag_file)}_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
        )
        
        # 复制标记文件到备份目录
        shutil.copy2(flag_file, backup_file)
        logging.info(f"已备份标记文件到: {backup_file}")
        
        # 删除原标记文件
        os.remove(flag_file)
        logging.info(f"已删除标记文件: {flag_file}")
    except Exception as e:
        logging.error(f"备份或删除标记文件时出错: {str(e)}")
        logging.error(traceback.format_exc())

# 检查是否需要暂停自动重启
def should_auto_restart():
    if os.path.exists(PAUSE_FLAG_FILE):
        try:
            with open(PAUSE_FLAG_FILE, 'r') as f:
                content = f.read()
            logging.debug(f"检测到暂停标记文件，内容: \n{content}")
            return False
        except Exception as e:
            logging.error(f"读取暂停标记文件时出错: {str(e)}")
    return True

# 主循环
def main_loop(test_mode=False):
    restart_count = 0
    last_restart_time = 0
    
    # 设置日志
    log_file = setup_logging()
    logging.info(f"保活脚本启动，日志文件: {log_file}")
    logging.info(f"监控脚本: {SCRIPT_NAME}")
    logging.info(f"检查间隔: {CHECK_INTERVAL}秒")
    
    if test_mode:
        logging.info("以测试模式运行，不会自动启动脚本")
    
    try:
        while True:
            try:
                # 检查是否有标记文件需要处理
                if check_flag_files():
                    logging.info("已处理标记文件，跳过本次检查")
                    time.sleep(CHECK_INTERVAL)
                    continue
                
                # 如果不是测试模式，且没有暂停自动重启，则检查脚本是否在运行
                if not test_mode and should_auto_restart():
                    running, pid = is_script_running()
                    
                    if not running:
                        current_time = time.time()
                        if restart_count < MAX_RESTARTS:
                            # 确保不会频繁重启
                            if current_time - last_restart_time >= RESTART_INTERVAL:
                                logging.warning(f"脚本未运行，正在尝试重启 (尝试 {restart_count+1}/{MAX_RESTARTS})")
                                if start_script():
                                    logging.info("脚本已成功重启")
                                    restart_count += 1
                                    last_restart_time = current_time
                                else:
                                    logging.error("脚本重启失败")
                            else:
                                logging.info(f"等待重启间隔，还需等待 {int(RESTART_INTERVAL - (current_time - last_restart_time))} 秒")
                        else:
                            logging.error(f"已达到最大重启次数 ({MAX_RESTARTS})，停止自动重启")
                            # 创建暂停标记文件
                            with open(PAUSE_FLAG_FILE, 'w') as f:
                                f.write(f"自动暂停时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                                f.write(f"原因: 已达到最大重启次数 ({MAX_RESTARTS})\n")
                                f.write("此文件存在时，保活脚本将不会自动重启自动化脚本\n")
                    else:
                        logging.info(f"脚本正在运行中，PID: {pid}")
                else:
                    # 测试模式或暂停状态，只记录状态不执行操作
                    if test_mode:
                        logging.info("测试模式：不执行自动重启")
                    else:
                        logging.info("自动重启已暂停")
                    
                    # 记录脚本状态
                    running, pid = is_script_running()
                    if running:
                        logging.info(f"脚本正在运行中，PID: {pid}")
                    else:
                        logging.info("脚本未运行")
                
            except Exception as e:
                logging.error(f"检查过程中出错: {str(e)}")
                logging.error(traceback.format_exc())
            
            time.sleep(CHECK_INTERVAL)
    
    except KeyboardInterrupt:
        logging.info("收到终止信号，保活脚本退出")
        sys.exit(0)

if __name__ == "__main__":
    # 检查是否有测试模式参数
    test_mode = "--test-mode" in sys.argv
    main_loop(test_mode) 