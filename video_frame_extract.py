import os
import sys
import argparse
import subprocess
import json
from PIL import Image, ImageDraw, ImageFont

def get_video_info(video_path):
    """
    用ffprobe获取视频信息
    """
    cmd = [
        'ffprobe',
        '-v', 'error',
        '-select_streams', 'v:0',
        '-show_entries', 'stream=width,height,avg_frame_rate,duration',
        '-show_format',
        '-of', 'json',
        video_path
    ]
    try:
        result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True, text=True)
        info = json.loads(result.stdout)
        stream = info['streams'][0]
        fmt = info['format']
        # 解析fps
        fps_str = stream.get('avg_frame_rate', '0/1')
        if '/' in fps_str:
            num, den = fps_str.split('/')
            fps = float(num) / float(den) if float(den) != 0 else 0
        else:
            fps = float(fps_str)
        return {
            'width': stream.get('width'),
            'height': stream.get('height'),
            'duration': float(stream.get('duration', fmt.get('duration', 0))),
            'fps': fps,
            'size': int(fmt.get('size', 0)),
            'filename': os.path.basename(video_path)
        }
    except Exception as e:
        print(f"获取视频信息失败: {e}")
        return {}

def annotate_images_with_index(output_dir):
    """
    给分帧图片左上角标注序号
    """
    img_files = sorted([f for f in os.listdir(output_dir) if f.lower().endswith('.png')])
    font = None
    try:
        font = ImageFont.truetype("arial.ttf", 28)
    except:
        font = ImageFont.load_default()
    for idx, fname in enumerate(img_files, 1):
        path = os.path.join(output_dir, fname)
        try:
            with Image.open(path) as im:
                draw = ImageDraw.Draw(im)
                text = str(idx)
                # 白底黑字
                text_size = draw.textsize(text, font=font)
                pad = 6
                box = [pad, pad, pad+text_size[0]+8, pad+text_size[1]+4]
                draw.rectangle(box, fill=(255,255,255,220))
                draw.text((pad+4, pad+2), text, fill=(0,0,0), font=font)
                im.save(path)
        except Exception as e:
            print(f"图片标注失败: {fname}: {e}")

def extract_frames_ffmpeg(video_path, output_dir, interval_ms):
    """
    使用ffmpeg按指定间隔(ms)分帧输出图片
    :param video_path: 输入视频路径
    :param output_dir: 输出图片目录
    :param interval_ms: 分帧间隔（毫秒，1~100ms）
    """
    if not os.path.exists(video_path):
        print(f"视频文件不存在: {video_path}")
        return
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    fps = 1000.0 / interval_ms
    output_pattern = os.path.join(output_dir, "frame_%06d.png")
    cmd = [
        "ffmpeg",
        "-i", video_path,
        "-vf", f"fps={fps}",
        "-vsync", "vfr",
        output_pattern
    ]
    print("执行命令：", " ".join(cmd))
    subprocess.run(cmd, check=True)
    print(f"分帧完成，图片保存在: {output_dir}")

def generate_html_report(video_path, output_dir, interval_ms, video_info):
    """
    生成展示原视频和所有分帧图片的网页
    """
    img_files = sorted([f for f in os.listdir(output_dir) if f.lower().endswith('.png')])
    html_path = os.path.join(output_dir, 'index.html')
    video_rel = os.path.relpath(video_path, output_dir)
    # 视频信息格式化
    def sizeof_fmt(num):
        for unit in ['B','KB','MB','GB','TB']:
            if num < 1024.0:
                return f"{num:.2f} {unit}"
            num /= 1024.0
        return f"{num:.2f} PB"
    info_html = f"""
    <ul style='list-style:none;padding:0;margin:0;'>
      <li><b>文件名：</b> {video_info.get('filename','')}</li>
      <li><b>大小：</b> {sizeof_fmt(video_info.get('size',0))}</li>
      <li><b>时长：</b> {video_info.get('duration',0):.2f} 秒</li>
      <li><b>分辨率：</b> {video_info.get('width','?')} x {video_info.get('height','?')}</li>
      <li><b>帧率：</b> {video_info.get('fps',0):.2f} fps</li>
      <li><b>分帧间隔：</b> {interval_ms} ms</li>
      <li><b>分帧图片数：</b> {len(img_files)}</li>
    </ul>
    """
    html = [
        '<!DOCTYPE html>',
        '<html lang="zh-CN">',
        '<head>',
        '<meta charset="UTF-8">',
        '<title>视频分帧结果</title>',
        '<style>',
        'body{font-family:Arial,sans-serif;background:#f7f7f7;margin:0;padding:0;}',
        '.container{max-width:1200px;margin:30px auto;background:#fff;padding:30px 40px;border-radius:8px;box-shadow:0 2px 8px rgba(0,0,0,0.08);}',
        'h1{text-align:center;color:#222;}',
        '.main-flex{display:flex;gap:40px;align-items:flex-start;margin-bottom:30px;}',
        '.left-col{flex:2;min-width:320px;max-width:600px;}',
        '.video-box{margin-bottom:18px;}',
        '.video-box video{width:480px;height:270px;max-width:100%;border-radius:8px;box-shadow:0 2px 8px #0002;}',
        '.info{margin-bottom:10px;}',
        '.right-col{flex:1;min-width:260px;max-width:340px;display:flex;flex-direction:column;align-items:center;}',
        '.frame-select-bar{width:100%;display:flex;flex-direction:column;gap:24px;align-items:center;}',
        '.frame-block{display:flex;flex-direction:column;align-items:center;}',
        '.frame-thumb{width:120px;height:80px;background:#eee;border:1px solid #ccc;border-radius:6px;object-fit:contain;}',
        '.frame-label{margin-top:6px;font-size:15px;color:#555;}',
        '.frame-idx{font-size:13px;color:#888;}',
        '.frame-placeholder{display:flex;align-items:center;justify-content:center;width:120px;height:80px;background:#f3f3f3;border:1px dashed #bbb;border-radius:6px;color:#bbb;font-size:15px;}',
        '.frame-cost{font-size:16px;color:#1abc9c;font-weight:bold;margin-top:10px;}',
        '.img-table{width:100%;border-collapse:collapse;margin-top:20px;}',
        '.img-table td{text-align:center;padding:8px 4px;}',
        '.img-thumb{max-width:180px;max-height:120px;border:1px solid #eee;border-radius:4px;cursor:pointer;transition:box-shadow .2s;}',
        '.img-thumb:hover{box-shadow:0 0 8px #16a08555;}',
        '.img-idx{color:#333;font-size:15px;margin-top:2px;}',
        '.img-modal{display:none;position:fixed;z-index:9999;left:0;top:0;width:100vw;height:100vh;background:rgba(0,0,0,0.7);align-items:center;justify-content:center;}',
        '.img-modal img{max-width:90vw;max-height:90vh;border-radius:8px;box-shadow:0 4px 32px #0008;}',
        '.img-modal.active{display:flex;}',
        '.img-modal-close{position:absolute;top:30px;right:50px;font-size:40px;color:#fff;cursor:pointer;font-weight:bold;}',
        '.modal-btns{display:flex;gap:24px;justify-content:center;margin-top:18px;}',
        '.modal-btn{padding:7px 18px;font-size:16px;border-radius:5px;border:none;cursor:pointer;background:#1abc9c;color:#fff;transition:background .2s;}',
        '.modal-btn:hover{background:#16a085;}',
        '.toast{position:fixed;top:32px;right:40px;z-index:99999;background:#222;color:#fff;padding:12px 28px;border-radius:6px;box-shadow:0 2px 12px #0003;font-size:16px;opacity:0;pointer-events:none;transition:opacity .3s;}',
        '.toast.show{opacity:1;pointer-events:auto;}',
        '</style>',
        '</head>',
        '<body>',
        '<div class="container">',
        '<h1>视频分帧结果</h1>',
        # 主体左右分栏
        '<div class="main-flex">',
        '<div class="left-col">',
        '<div class="video-box">',
        f'<video src="{video_rel}" controls></video>',
        '</div>',
        f'<div class="info">{info_html}</div>',
        '</div>',
        '<div class="right-col">',
        '<div class="frame-select-bar" id="frameSelectBar">',
        '  <div class="frame-block">',
        '    <div id="startFrameImg" class="frame-placeholder">未选择</div>',
        '    <div class="frame-label">开始帧</div>',
        '    <div id="startFrameIdx" class="frame-idx"></div>',
        '  </div>',
        '  <div class="frame-block">',
        '    <div id="endFrameImg" class="frame-placeholder">未选择</div>',
        '    <div class="frame-label">结束帧</div>',
        '    <div id="endFrameIdx" class="frame-idx"></div>',
        '  </div>',
        '  <div class="frame-block">',
        '    <div class="frame-label">耗时</div>',
        f'    <div class="frame-cost" id="frameCost">-</div>',
        '  </div>',
        '</div>',
        '</div>',
        '</div>',
        # 图片表格
        '<table class="img-table">'
    ]
    imgs_per_row = 6
    for i in range(0, len(img_files), imgs_per_row):
        html.append('<tr>')
        for j in range(imgs_per_row):
            idx = i + j
            if idx < len(img_files):
                img = img_files[idx]
                html.append(f'<td><img src="{img}" class="img-thumb" onclick="showImgModal(\'{img}\',{idx+1})" alt="帧"></td>')
            else:
                html.append('<td></td>')
        html.append('</tr>')
        html.append('<tr>')
        for j in range(imgs_per_row):
            idx = i + j
            if idx < len(img_files):
                html.append(f'<td class="img-idx">{idx+1}</td>')
            else:
                html.append('<td></td>')
        html.append('</tr>')
    html.append('</table>')
    # 弹窗大图容器，带按钮
    html.append('''<div class="img-modal" id="imgModal" onclick="hideImgModal()">
        <span class="img-modal-close" onclick="hideImgModal()">&times;</span>
        <div style="display:flex;flex-direction:column;align-items:center;justify-content:center;width:100%;height:100%;">
            <img id="imgModalImg" src="" alt="大图">
            <div class="modal-btns">
                <button class="modal-btn" id="setStartBtn">设为开始帧</button>
                <button class="modal-btn" id="setEndBtn">设为结束帧</button>
            </div>
        </div>
    </div>''')
    # 提示浮层
    html.append('<div class="toast" id="toast"></div>')
    # 脚本
    html.append(f'''<script>
    var currentImg = null;
    var currentIdx = null;
    var frameCount = {len(img_files)};
    var intervalMs = {interval_ms};
    function showImgModal(url, idx){{
        var modal = document.getElementById('imgModal');
        var img = document.getElementById('imgModalImg');
        img.src = url;
        currentImg = url;
        currentIdx = idx;
        modal.classList.add('active');
    }}
    function hideImgModal(){{
        var modal = document.getElementById('imgModal');
        modal.classList.remove('active');
        document.getElementById('imgModalImg').src = '';
        currentImg = null;
        currentIdx = null;
    }}
    function showToast(msg){{
        var toast = document.getElementById('toast');
        toast.innerText = msg;
        toast.classList.add('show');
        setTimeout(function(){{toast.classList.remove('show');}}, 3000);
    }}
    
    // 帧选择状态
    function updateFrameBar(){{
        var start = JSON.parse(localStorage.getItem('startFrame')||'null');
        var end = JSON.parse(localStorage.getItem('endFrame')||'null');
        var startImg = document.getElementById('startFrameImg');
        var endImg = document.getElementById('endFrameImg');
        var startIdx = document.getElementById('startFrameIdx');
        var endIdx = document.getElementById('endFrameIdx');
        var cost = document.getElementById('frameCost');
        if(start && start.idx && start.img){{
            startImg.innerHTML = '<img src="'+start.img+'" class="frame-thumb">';
            startIdx.innerText = '序号: '+start.idx;
        }}else{{
            startImg.innerHTML = '<div class="frame-placeholder">未选择</div>';
            startIdx.innerText = '';
        }}
        if(end && end.idx && end.img){{
            endImg.innerHTML = '<img src="'+end.img+'" class="frame-thumb">';
            endIdx.innerText = '序号: '+end.idx;
        }}else{{
            endImg.innerHTML = '<div class="frame-placeholder">未选择</div>';
            endIdx.innerText = '';
        }}
        if(start && end && start.idx && end.idx && end.idx > start.idx){{
            var ms = (end.idx - start.idx) * intervalMs;
            cost.innerText = ms + ' ms';
        }}else{{
            cost.innerText = '-';
        }}
    }}
    document.addEventListener('DOMContentLoaded', function(){{
        updateFrameBar();
        document.getElementById('setStartBtn').onclick = function(e){{
            if(currentImg && currentIdx){{
                localStorage.setItem('startFrame', JSON.stringify({{img: currentImg, idx: currentIdx}}));
                updateFrameBar();
                hideImgModal();
                showToast('设置为开始帧成功！');
            }}
            e.stopPropagation();
        }};
        document.getElementById('setEndBtn').onclick = function(e){{
            if(currentImg && currentIdx){{
                localStorage.setItem('endFrame', JSON.stringify({{img: currentImg, idx: currentIdx}}));
                updateFrameBar();
                hideImgModal();
                showToast('设置为结束帧成功！');
            }}
            e.stopPropagation();
        }};
        var img = document.getElementById('imgModalImg');
        img.onclick = function(e){{e.stopPropagation();}};
    }});
    </script>''')
    html.append('</div></body></html>')
    with open(html_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(html))
    print(f"网页已生成: {html_path}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="视频分帧工具（支持高帧率，精确到ms）")
    parser.add_argument("video", help="输入视频文件路径")
    parser.add_argument("-o", "--output", default="frames", help="输出图片目录")
    parser.add_argument("-i", "--interval", type=float, default=10, help="分帧间隔（毫秒，1~100ms）")
    args = parser.parse_args()

    if not (1 <= args.interval <= 100):
        print("分帧间隔必须在1~100毫秒之间")
        sys.exit(1)

    video_info = get_video_info(args.video)
    extract_frames_ffmpeg(args.video, args.output, args.interval)
    generate_html_report(args.video, args.output, args.interval, video_info) 