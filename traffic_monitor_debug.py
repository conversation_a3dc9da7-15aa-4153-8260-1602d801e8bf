#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Android流量监控诊断工具
用于排查流量获取失败的原因

使用方法: python3 traffic_monitor_debug.py
"""

import subprocess
import logging
from typing import Dict, List, Optional

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(message)s')

class TrafficMonitorDebug:
    """流量监控诊断工具"""
    
    def __init__(self):
        self.device_id = None
        
    def check_adb_connection(self) -> bool:
        """检查ADB连接"""
        try:
            result = subprocess.run(["adb", "devices"], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                devices = result.stdout.strip().split('\n')[1:]
                connected_devices = [line.split('\t')[0] for line in devices if '\tdevice' in line]
                if connected_devices:
                    self.device_id = connected_devices[0]
                    print(f"✅ ADB连接成功，设备ID: {self.device_id}")
                    return True
                else:
                    print("❌ 没有找到连接的设备")
                    return False
            else:
                print(f"❌ ADB命令执行失败: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ ADB连接异常: {str(e)}")
            return False
    
    def check_device_info(self):
        """检查设备信息"""
        print("\n📱 设备信息检查:")
        print("-" * 40)
        
        # Android版本
        try:
            result = subprocess.run(["adb", "-s", self.device_id, "shell", "getprop", "ro.build.version.release"], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                android_version = result.stdout.strip()
                print(f"Android版本: {android_version}")
            else:
                print("❌ 无法获取Android版本")
        except Exception as e:
            print(f"❌ 获取Android版本失败: {str(e)}")
        
        # SDK版本
        try:
            result = subprocess.run(["adb", "-s", self.device_id, "shell", "getprop", "ro.build.version.sdk"], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                sdk_version = result.stdout.strip()
                print(f"SDK版本: {sdk_version}")
            else:
                print("❌ 无法获取SDK版本")
        except Exception as e:
            print(f"❌ 获取SDK版本失败: {str(e)}")
        
        # 设备型号
        try:
            result = subprocess.run(["adb", "-s", self.device_id, "shell", "getprop", "ro.product.model"], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                device_model = result.stdout.strip()
                print(f"设备型号: {device_model}")
            else:
                print("❌ 无法获取设备型号")
        except Exception as e:
            print(f"❌ 获取设备型号失败: {str(e)}")
        
        # Root权限检查
        try:
            result = subprocess.run(["adb", "-s", self.device_id, "shell", "su", "-c", "id"], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0 and "uid=0" in result.stdout:
                print("✅ 设备已Root")
            else:
                print("⚠️  设备未Root (可能影响流量监控)")
        except Exception as e:
            print("⚠️  无法检查Root状态")
    
    def check_traffic_data_sources(self):
        """检查流量数据源"""
        print("\n📊 流量数据源检查:")
        print("-" * 40)
        
        # 检查 /proc/uid_stat/
        try:
            result = subprocess.run(["adb", "-s", self.device_id, "shell", "ls", "/proc/uid_stat/"], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                uid_dirs = result.stdout.strip().split('\n')
                print(f"✅ /proc/uid_stat/ 存在，找到 {len(uid_dirs)} 个UID目录")
                if len(uid_dirs) > 5:
                    print(f"   示例UID: {', '.join(uid_dirs[:5])}...")
                else:
                    print(f"   UID列表: {', '.join(uid_dirs)}")
            else:
                print("❌ /proc/uid_stat/ 不存在或无法访问")
        except Exception as e:
            print(f"❌ 检查 /proc/uid_stat/ 失败: {str(e)}")
        
        # 检查 /proc/net/xt_qtaguid/stats
        try:
            result = subprocess.run(["adb", "-s", self.device_id, "shell", "ls", "/proc/net/xt_qtaguid/stats"], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print("✅ /proc/net/xt_qtaguid/stats 存在")
                
                # 尝试读取前几行
                result = subprocess.run(["adb", "-s", self.device_id, "shell", "head", "-5", "/proc/net/xt_qtaguid/stats"], 
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0 and result.stdout.strip():
                    lines = result.stdout.strip().split('\n')
                    print(f"   文件包含 {len(lines)} 行数据")
                else:
                    print("   文件存在但无法读取内容")
            else:
                print("❌ /proc/net/xt_qtaguid/stats 不存在")
        except Exception as e:
            print(f"❌ 检查 /proc/net/xt_qtaguid/stats 失败: {str(e)}")
        
        # 检查 dumpsys netstats
        try:
            result = subprocess.run(["adb", "-s", self.device_id, "shell", "dumpsys", "netstats", "detail"], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0 and result.stdout.strip():
                lines = result.stdout.strip().split('\n')
                print(f"✅ dumpsys netstats 可用，输出 {len(lines)} 行数据")
            else:
                print("❌ dumpsys netstats 不可用")
        except Exception as e:
            print(f"❌ 检查 dumpsys netstats 失败: {str(e)}")
    
    def test_specific_app(self, package_name: str):
        """测试特定应用的流量获取"""
        print(f"\n🔍 测试应用: {package_name}")
        print("-" * 40)
        
        # 获取UID
        uid = self.get_app_uid(package_name)
        if uid is None:
            print(f"❌ 无法获取应用 {package_name} 的UID")
            return
        
        print(f"✅ 应用UID: {uid}")
        
        # 测试各种流量获取方法
        methods_tested = 0
        methods_success = 0
        
        # 方法1: /proc/uid_stat/
        methods_tested += 1
        try:
            result = subprocess.run(["adb", "-s", self.device_id, "shell", "cat", f"/proc/uid_stat/{uid}/tcp_snd"], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0 and result.stdout.strip():
                tx_bytes = int(result.stdout.strip())
                print(f"✅ /proc/uid_stat/ 方法: TX = {tx_bytes} bytes ({tx_bytes/1024/1024:.2f} MB)")
                methods_success += 1
            else:
                print(f"❌ /proc/uid_stat/ 方法失败: {result.stderr}")
        except Exception as e:
            print(f"❌ /proc/uid_stat/ 方法异常: {str(e)}")
        
        # 方法2: dumpsys netstats
        methods_tested += 1
        try:
            result = subprocess.run(["adb", "-s", self.device_id, "shell", "dumpsys", "netstats", "detail"], 
                                  capture_output=True, text=True, timeout=15)
            if result.returncode == 0:
                found_data = False
                lines = result.stdout.split('\n')
                for i, line in enumerate(lines):
                    if f'uid={uid}' in line:
                        print(f"✅ dumpsys netstats 找到UID数据: {line.strip()}")
                        found_data = True
                        break
                
                if found_data:
                    methods_success += 1
                else:
                    print("❌ dumpsys netstats 中未找到该UID的数据")
            else:
                print(f"❌ dumpsys netstats 执行失败: {result.stderr}")
        except Exception as e:
            print(f"❌ dumpsys netstats 异常: {str(e)}")
        
        # 方法3: /proc/net/xt_qtaguid/stats
        methods_tested += 1
        try:
            result = subprocess.run(["adb", "-s", self.device_id, "shell", "grep", str(uid), "/proc/net/xt_qtaguid/stats"], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0 and result.stdout.strip():
                lines = result.stdout.strip().split('\n')
                print(f"✅ xt_qtaguid 找到 {len(lines)} 条UID数据")
                for line in lines[:3]:  # 只显示前3行
                    print(f"   {line}")
                methods_success += 1
            else:
                print("❌ xt_qtaguid 中未找到该UID的数据")
        except Exception as e:
            print(f"❌ xt_qtaguid 方法异常: {str(e)}")
        
        print(f"\n📈 测试结果: {methods_success}/{methods_tested} 种方法成功")
        
        # 给出建议
        if methods_success == 0:
            print("\n💡 建议:")
            print("1. 确保应用正在运行并产生网络流量")
            print("2. 尝试在应用中进行网络操作（如刷新、发送消息等）")
            print("3. 检查设备是否需要Root权限")
            print("4. 考虑使用系统自带的流量监控应用对比")
    
    def get_app_uid(self, package_name: str) -> Optional[int]:
        """获取应用UID（简化版）"""
        try:
            # 使用pm命令
            result = subprocess.run(["adb", "-s", self.device_id, "shell", "pm", "list", "packages", "-U", package_name], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0 and result.stdout.strip():
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if f'package:{package_name}' in line and 'uid:' in line:
                        parts = line.split('uid:')
                        if len(parts) > 1:
                            return int(parts[1].strip())
            
            # 备用方法：dumpsys package
            result = subprocess.run(["adb", "-s", self.device_id, "shell", "dumpsys", "package", package_name], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'userId=' in line:
                        parts = line.split('userId=')
                        if len(parts) > 1:
                            return int(parts[1].split()[0])
            
            return None
            
        except Exception as e:
            print(f"获取UID失败: {str(e)}")
            return None
    
    def get_popular_apps(self) -> List[str]:
        """获取一些常见的应用包名用于测试"""
        try:
            result = subprocess.run(["adb", "-s", self.device_id, "shell", "pm", "list", "packages", "-3"], 
                                  capture_output=True, text=True, timeout=15)
            if result.returncode == 0:
                packages = []
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if line.startswith('package:'):
                        package_name = line.replace('package:', '').strip()
                        # 优先选择常见应用
                        if any(app in package_name for app in ['tencent', 'alibaba', 'baidu', 'sina', 'netease']):
                            packages.append(package_name)
                
                return packages[:5]  # 返回前5个
            return []
        except Exception:
            return []


def main():
    """主函数"""
    print("🔧 Android流量监控诊断工具")
    print("=" * 50)
    
    debug_tool = TrafficMonitorDebug()
    
    # 1. 检查ADB连接
    if not debug_tool.check_adb_connection():
        return
    
    # 2. 检查设备信息
    debug_tool.check_device_info()
    
    # 3. 检查流量数据源
    debug_tool.check_traffic_data_sources()
    
    # 4. 测试具体应用
    print("\n🎯 应用流量测试:")
    print("-" * 40)
    
    # 获取一些应用进行测试
    apps = debug_tool.get_popular_apps()
    if apps:
        print(f"找到 {len(apps)} 个应用可供测试:")
        for i, app in enumerate(apps, 1):
            print(f"{i}. {app}")
        
        try:
            choice = input(f"\n请选择要测试的应用 (1-{len(apps)}) 或输入包名: ").strip()
            
            if choice.isdigit():
                index = int(choice) - 1
                if 0 <= index < len(apps):
                    selected_app = apps[index]
                else:
                    print("❌ 无效选择")
                    return
            else:
                selected_app = choice
            
            debug_tool.test_specific_app(selected_app)
            
        except KeyboardInterrupt:
            print("\n👋 诊断中断")
    else:
        print("❌ 未找到可测试的应用")
    
    print("\n✅ 诊断完成!")
    print("\n💡 如果所有方法都无法获取流量数据，可能的解决方案:")
    print("1. 使用Root权限的设备")
    print("2. 使用系统级流量监控应用")
    print("3. 使用网络抓包工具（如tcpdump + Wireshark）")
    print("4. 使用Android官方的网络监控API")


if __name__ == "__main__":
    main() 