#!/usr/bin/env python3
"""
批量Chrome浏览器启动脚本 (基于Playwright，支持移动设备模拟)
功能：
1. 读取URL文件，每行一个URL
2. 为每个URL启动独立的Chrome浏览器进程，模拟移动设备
3. 等待用户操作完成后，输入指令关闭所有进程
"""

import sys
import os
import time
import logging
from typing import List, Dict, Optional
from playwright.sync_api import sync_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page

class ChromeBatchLauncher:
    def __init__(self, url_file: str = "test_urls_pre.txt", device_name: str = "iPhone 13 Pro"):
        self.url_file = url_file
        self.device_name = device_name
        self.browsers: List[Browser] = []
        self.contexts: List[BrowserContext] = []
        self.pages: List[Page] = []
        self.playwright = None
        self.url_info: Dict[int, str] = {}

        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )

    def find_chrome_executable(self) -> Optional[str]:
        """查找Chrome可执行文件路径"""
        chrome_paths = [
            '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',  # macOS路径
            'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',    # Windows路径
            'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe',
            '/usr/bin/google-chrome',  # Linux路径
            '/usr/bin/google-chrome-stable',
            '/usr/bin/chromium-browser',
            '/usr/bin/chromium',
        ]

        for path in chrome_paths:
            if os.path.exists(path):
                logging.info(f"找到Chrome浏览器: {path}")
                return path

        logging.info("未找到系统Chrome，将使用Playwright内置浏览器")
        return None
    
    def read_urls(self) -> List[str]:
        """读取URL文件"""
        if not os.path.exists(self.url_file):
            raise FileNotFoundError(f"URL文件不存在: {self.url_file}")

        urls = []
        with open(self.url_file, 'r', encoding='utf-8') as f:
            for line in f:
                url = line.strip()
                if url and not url.startswith('#'):  # 忽略空行和注释行
                    urls.append(url)

        return urls

    def create_browser_instance(self, index: int, window_x: Optional[int] = None, window_y: Optional[int] = None) -> tuple[Browser, BrowserContext, Page]:
        """创建单个浏览器实例，模拟移动设备"""
        logging.info(f"正在初始化浏览器实例 {index + 1}...")

        # 查找Chrome可执行文件
        chrome_executable = self.find_chrome_executable()

        # 准备浏览器启动参数
        browser_args = [
            '--no-sandbox',
            '--disable-dev-shm-usage'
        ]

        # 如果指定了窗口位置，添加窗口位置参数
        if window_x is not None and window_y is not None:
            browser_args.append(f'--window-position={window_x},{window_y}')
            logging.info(f"设置浏览器窗口位置: ({window_x}, {window_y})")

        # 启动浏览器
        if chrome_executable:
            browser = self.playwright.chromium.launch(
                headless=False,  # 设为True可以在后台运行
                slow_mo=50,  # 稍微减慢操作速度，以便观察
                executable_path=chrome_executable,
                args=browser_args
            )
        else:
            browser = self.playwright.chromium.launch(
                headless=False,  # 设为True可以在后台运行
                slow_mo=50,  # 稍微减慢操作速度，以便观察
                args=browser_args
            )

        # 获取设备配置
        device = self.playwright.devices[self.device_name]

        # 创建浏览器上下文，使用移动设备模拟
        context = browser.new_context(
            **device,  # 应用设备参数（包括视口、User-Agent等）
            locale="zh-CN",          # 设置语言环境
            timezone_id="Asia/Shanghai"  # 设置时区
        )

        # 创建页面
        page = context.new_page()

        return browser, context, page
    
    def launch_all_browsers(self):
        """启动所有浏览器进程"""
        urls = self.read_urls()
        print(f"找到 {len(urls)} 个URL，开始启动Chrome浏览器（模拟设备: {self.device_name}）...")

        # 初始化Playwright
        self.playwright = sync_playwright().start()

        # 计算窗口位置（可选，用于排列窗口）
        window_offset_x = 50
        window_offset_y = 50

        for i, url in enumerate(urls):
            print(f"启动浏览器 {i+1}/{len(urls)}: {url}")

            try:
                # 计算窗口位置
                window_x = window_offset_x + (i % 4) * 400  # 每行4个窗口
                window_y = window_offset_y + (i // 4) * 300  # 每300像素一行

                # 创建浏览器实例
                browser, context, page = self.create_browser_instance(i, window_x, window_y)

                # 导航到URL
                page.goto(url, wait_until='domcontentloaded', timeout=30000)

                # 保存实例
                self.browsers.append(browser)
                self.contexts.append(context)
                self.pages.append(page)
                self.url_info[i] = f"URL {i+1}: {url}"

                print(f"✓ 浏览器 {i+1} 启动成功")
                time.sleep(2)  # 稍微延迟，避免同时启动太多进程

            except Exception as e:
                print(f"✗ 启动浏览器 {i+1} 失败: {e}")
                logging.error(f"启动浏览器失败: {e}")

        print(f"\n成功启动了 {len(self.browsers)} 个Chrome浏览器进程")
        self.show_process_info()
    
    def show_process_info(self):
        """显示进程信息"""
        print("\n当前运行的Chrome浏览器:")
        print("-" * 60)
        for i, browser in enumerate(self.browsers):
            try:
                # 检查浏览器是否仍在运行
                contexts = browser.contexts
                if contexts:
                    info = self.url_info.get(i, f"浏览器 {i+1}")
                    print(f"✓ 运行中 - {info}")
                else:
                    info = self.url_info.get(i, f"浏览器 {i+1}")
                    print(f"✗ 已关闭 - {info}")
            except Exception:
                info = self.url_info.get(i, f"浏览器 {i+1}")
                print(f"✗ 已关闭 - {info}")
        print("-" * 60)
    
    def wait_for_user_input(self):
        """等待用户输入"""
        print("\n所有浏览器已启动完成！")
        print("请在各个浏览器中完成您的操作...")
        print("\n可用命令:")
        print("  'status' 或 's' - 查看进程状态")
        print("  'quit' 或 'q' - 关闭所有浏览器进程")
        print("  'help' 或 'h' - 显示帮助信息")
        
        while True:
            try:
                user_input = input("\n请输入命令 (输入 'quit' 关闭所有进程): ").strip().lower()
                
                if user_input in ['quit', 'q', 'exit']:
                    break
                elif user_input in ['status', 's']:
                    self.check_process_status()
                elif user_input in ['help', 'h']:
                    self.show_help()
                else:
                    print("未知命令，请输入 'help' 查看可用命令")
                    
            except KeyboardInterrupt:
                print("\n\n检测到 Ctrl+C，准备关闭所有进程...")
                break
            except EOFError:
                print("\n\n检测到输入结束，准备关闭所有进程...")
                break
    
    def show_help(self):
        """显示帮助信息"""
        print("\n=== 帮助信息 ===")
        print("命令列表:")
        print("  status, s  - 查看当前运行的Chrome进程状态")
        print("  quit, q    - 关闭所有Chrome进程并退出程序")
        print("  help, h    - 显示此帮助信息")
        print("  Ctrl+C     - 强制关闭所有进程并退出")
    
    def check_process_status(self):
        """检查进程状态"""
        running_count = 0
        print("\n浏览器状态检查:")
        print("-" * 60)

        for i, browser in enumerate(self.browsers):
            info = self.url_info.get(i, f"浏览器 {i+1}")
            try:
                # 检查浏览器是否仍在运行
                contexts = browser.contexts
                if contexts and len(contexts) > 0:
                    print(f"✓ 运行中 - {info}")
                    running_count += 1
                else:
                    print(f"✗ 已关闭 - {info}")
            except Exception:
                print(f"✗ 已关闭 - {info}")

        print("-" * 60)
        print(f"总计: {len(self.browsers)} 个浏览器，{running_count} 个正在运行")
    
    def cleanup_processes(self):
        """清理所有Chrome浏览器"""
        if not self.browsers:
            print("没有需要关闭的浏览器")
            return

        print(f"\n开始关闭 {len(self.browsers)} 个Chrome浏览器...")

        closed_count = 0

        # 关闭所有页面
        for i, page in enumerate(self.pages):
            try:
                if not page.is_closed():
                    print(f"关闭页面 {i+1}...")
                    page.close()
                    closed_count += 1
            except Exception as e:
                print(f"关闭页面 {i+1} 时出错: {e}")

        # 关闭所有上下文
        for i, context in enumerate(self.contexts):
            try:
                print(f"关闭上下文 {i+1}...")
                context.close()
            except Exception as e:
                print(f"关闭上下文 {i+1} 时出错: {e}")

        # 关闭所有浏览器
        for i, browser in enumerate(self.browsers):
            try:
                print(f"关闭浏览器 {i+1}...")
                browser.close()
            except Exception as e:
                print(f"关闭浏览器 {i+1} 时出错: {e}")

        # 停止Playwright
        if self.playwright:
            try:
                self.playwright.stop()
            except Exception as e:
                print(f"停止Playwright时出错: {e}")

        print(f"成功关闭了 {closed_count} 个浏览器")

        # 清理列表
        self.browsers.clear()
        self.contexts.clear()
        self.pages.clear()
        self.url_info.clear()
    
    def run(self):
        """主运行方法"""
        try:
            print("=== Chrome批量启动器 ===")
            print(f"URL文件: {self.url_file}")
            
            # 启动所有浏览器
            self.launch_all_browsers()
            
            # 等待用户操作
            self.wait_for_user_input()
            
        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
        except Exception as e:
            print(f"\n程序运行出错: {e}")
        finally:
            # 清理所有进程
            self.cleanup_processes()
            print("\n程序结束")

def main():
    """主函数"""
    url_file = "test_urls_pre.txt"
    device_name = "iPhone 13 Pro"

    # 检查命令行参数
    if len(sys.argv) > 1:
        url_file = sys.argv[1]
    if len(sys.argv) > 2:
        device_name = sys.argv[2]

    print(f"使用设备模拟: {device_name}")
    print("可用设备列表: iPhone 13 Pro, iPhone 12, iPad Pro, Samsung Galaxy S21, Pixel 5 等")

    launcher = ChromeBatchLauncher(url_file, device_name)
    launcher.run()

if __name__ == "__main__":
    main()
