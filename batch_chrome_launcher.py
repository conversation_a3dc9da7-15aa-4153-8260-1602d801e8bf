#!/usr/bin/env python3
"""
批量Chrome浏览器启动脚本 (支持移动设备模拟)
功能：
1. 读取URL文件，每行一个URL
2. 为每个URL启动独立的Chrome浏览器进程，模拟移动设备
3. 等待用户操作完成后，输入指令关闭所有进程
"""

import subprocess
import sys
import os
import time
import signal
from typing import List, Dict, Optional

class ChromeBatchLauncher:
    def __init__(self, url_file: str = "test_urls_pre.txt", device_name: str = "iPhone 13 Pro"):
        self.url_file = url_file
        self.device_name = device_name
        self.chrome_processes: List[subprocess.Popen] = []
        self.process_info: Dict[int, str] = {}

        # 移动设备配置
        self.device_configs = {
            "iPhone 13 Pro": {
                "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                "viewport": "390x844",
                "device_scale_factor": 3
            },
            "iPhone 12": {
                "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
                "viewport": "390x844",
                "device_scale_factor": 3
            },
            "iPad Pro": {
                "user_agent": "Mozilla/5.0 (iPad; CPU OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                "viewport": "1024x1366",
                "device_scale_factor": 2
            },
            "Samsung Galaxy S21": {
                "user_agent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36",
                "viewport": "360x800",
                "device_scale_factor": 3
            }
        }

    def find_chrome_executable(self) -> str:
        """查找Chrome可执行文件路径"""
        possible_paths = [
            # macOS
            "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
            "/Applications/Chromium.app/Contents/MacOS/Chromium",
            # Linux
            "/usr/bin/google-chrome",
            "/usr/bin/google-chrome-stable",
            "/usr/bin/chromium-browser",
            "/usr/bin/chromium",
            # Windows (如果在WSL中运行)
            "/mnt/c/Program Files/Google/Chrome/Application/chrome.exe",
            "/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe",
        ]

        for path in possible_paths:
            if os.path.exists(path):
                return path

        # 尝试使用系统PATH中的chrome
        try:
            result = subprocess.run(["which", "google-chrome"],
                                  capture_output=True, text=True)
            if result.returncode == 0:
                return result.stdout.strip()
        except:
            pass

        try:
            result = subprocess.run(["which", "chrome"],
                                  capture_output=True, text=True)
            if result.returncode == 0:
                return result.stdout.strip()
        except:
            pass

        raise FileNotFoundError("未找到Chrome浏览器，请确保已安装Chrome或Chromium")
    
    def read_urls(self) -> List[str]:
        """读取URL文件"""
        if not os.path.exists(self.url_file):
            raise FileNotFoundError(f"URL文件不存在: {self.url_file}")

        urls = []
        with open(self.url_file, 'r', encoding='utf-8') as f:
            for line in f:
                url = line.strip()
                if url and not url.startswith('#'):  # 忽略空行和注释行
                    urls.append(url)

        return urls

    def launch_chrome_process(self, url: str, index: int) -> subprocess.Popen:
        """启动单个Chrome进程，模拟移动设备"""
        chrome_path = self.find_chrome_executable()

        # 获取设备配置
        device_config = self.device_configs.get(self.device_name, self.device_configs["iPhone 13 Pro"])

        # 计算窗口大小（添加浏览器UI的额外空间）
        viewport_parts = device_config['viewport'].split('x')
        viewport_width = int(viewport_parts[0])
        viewport_height = int(viewport_parts[1])

        # 为浏览器UI添加额外空间
        window_width = viewport_width + 20  # 添加边框空间
        window_height = viewport_height + 120  # 添加地址栏、工具栏等空间

        # 创建启动脚本，使用开发者工具自动打开移动设备模拟
        script_content = f'''
tell application "Google Chrome"
    activate
    set newWindow to make new window
    set URL of active tab of newWindow to "{url}"
    delay 2
    tell application "System Events"
        tell process "Google Chrome"
            key code 35 using {{command down, option down}} -- Cmd+Option+I 打开开发者工具
            delay 1
            key code 35 using {{command down, shift down}} -- Cmd+Shift+M 切换设备模拟
        end tell
    end tell
end tell
'''

        # Chrome启动参数，包含移动设备模拟
        chrome_args = [
            chrome_path,
            "--new-window",  # 新窗口
            "--no-first-run",  # 跳过首次运行设置
            "--no-default-browser-check",  # 不检查默认浏览器
            f"--user-data-dir=/tmp/chrome_profile_{index}_{int(time.time())}",  # 独立用户数据目录
            "--disable-web-security",  # 可选：禁用web安全检查
            "--disable-features=VizDisplayCompositor",  # 可选：提高兼容性
            "--disable-extensions",  # 禁用扩展
            "--disable-plugins",  # 禁用插件
            "--disable-default-apps",  # 禁用默认应用
            "--start-maximized",  # 最大化启动

            # 移动设备模拟参数 - 关键设置
            f"--user-agent={device_config['user_agent']}",  # 设置User-Agent
            f"--window-size={window_width},{window_height}",   # 设置窗口大小（包含UI空间）
            f"--force-device-scale-factor={device_config['device_scale_factor']}",  # 设备缩放
            "--enable-touch-events",  # 启用触摸事件
            "--touch-events=enabled",  # 强制启用触摸事件
            "--enable-viewport-meta",  # 启用viewport meta标签
            "--enable-features=OverlayScrollbar",  # 启用移动端滚动条

            # 开发者工具相关
            "--auto-open-devtools-for-tabs",  # 自动打开开发者工具

            # 窗口位置
            f"--window-position={50 + (index % 4) * (window_width + 20)},{50 + (index // 4) * (window_height + 30)}",

            # URL必须放在最后
            url,
        ]

        try:
            process = subprocess.Popen(
                chrome_args,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL,
                preexec_fn=os.setsid if os.name != 'nt' else None
            )
            return process
        except Exception as e:
            print(f"启动Chrome进程失败 (URL {index+1}): {e}")
            return None
    
    def launch_all_browsers(self):
        """启动所有浏览器进程"""
        urls = self.read_urls()
        device_config = self.device_configs.get(self.device_name, self.device_configs["iPhone 13 Pro"])
        print(f"找到 {len(urls)} 个URL，开始启动Chrome浏览器...")
        print(f"模拟设备: {self.device_name}")
        print(f"设备配置: {device_config['viewport']} @ {device_config['device_scale_factor']}x")

        for i, url in enumerate(urls):
            print(f"启动浏览器 {i+1}/{len(urls)}: {url}")
            process = self.launch_chrome_process(url, i)

            if process:
                self.chrome_processes.append(process)
                self.process_info[process.pid] = f"URL {i+1}: {url}"
                time.sleep(1)  # 稍微延迟，避免同时启动太多进程
            else:
                print(f"跳过URL {i+1}（启动失败）")

        print(f"\n成功启动了 {len(self.chrome_processes)} 个Chrome浏览器进程")
        self.show_process_info()
    
    def show_process_info(self):
        """显示进程信息"""
        print("\n当前运行的Chrome进程:")
        print("-" * 60)
        for process in self.chrome_processes:
            if process.poll() is None:  # 进程仍在运行
                info = self.process_info.get(process.pid, f"PID: {process.pid}")
                print(f"PID {process.pid}: {info}")
        print("-" * 60)
    
    def wait_for_user_input(self):
        """等待用户输入"""
        print("\n所有浏览器已启动完成！")
        print("请在各个浏览器中完成您的操作...")
        print("\n可用命令:")
        print("  'status' 或 's' - 查看进程状态")
        print("  'quit' 或 'q' - 关闭所有浏览器进程")
        print("  'help' 或 'h' - 显示帮助信息")
        
        while True:
            try:
                user_input = input("\n请输入命令 (输入 'quit' 关闭所有进程): ").strip().lower()
                
                if user_input in ['quit', 'q', 'exit']:
                    break
                elif user_input in ['status', 's']:
                    self.check_process_status()
                elif user_input in ['help', 'h']:
                    self.show_help()
                else:
                    print("未知命令，请输入 'help' 查看可用命令")
                    
            except KeyboardInterrupt:
                print("\n\n检测到 Ctrl+C，准备关闭所有进程...")
                break
            except EOFError:
                print("\n\n检测到输入结束，准备关闭所有进程...")
                break
    
    def show_help(self):
        """显示帮助信息"""
        print("\n=== 帮助信息 ===")
        print("命令列表:")
        print("  status, s  - 查看当前运行的Chrome进程状态")
        print("  quit, q    - 关闭所有Chrome进程并退出程序")
        print("  help, h    - 显示此帮助信息")
        print("  Ctrl+C     - 强制关闭所有进程并退出")
    
    def check_process_status(self):
        """检查进程状态"""
        running_count = 0
        print("\n进程状态检查:")
        print("-" * 60)

        for process in self.chrome_processes:
            info = self.process_info.get(process.pid, f"PID: {process.pid}")
            if process.poll() is None:
                print(f"✓ 运行中 - PID {process.pid}: {info}")
                running_count += 1
            else:
                print(f"✗ 已结束 - PID {process.pid}: {info}")

        print("-" * 60)
        print(f"总计: {len(self.chrome_processes)} 个进程，{running_count} 个正在运行")
    
    def cleanup_processes(self):
        """清理所有Chrome进程"""
        if not self.chrome_processes:
            print("没有需要关闭的进程")
            return

        print(f"\n开始关闭 {len(self.chrome_processes)} 个Chrome进程...")

        terminated_count = 0
        for process in self.chrome_processes:
            try:
                if process.poll() is None:  # 进程仍在运行
                    print(f"关闭进程 PID {process.pid}...")

                    # 尝试优雅关闭
                    if os.name != 'nt':
                        os.killpg(os.getpgid(process.pid), signal.SIGTERM)
                    else:
                        process.terminate()

                    # 等待进程结束
                    try:
                        process.wait(timeout=5)
                        terminated_count += 1
                    except subprocess.TimeoutExpired:
                        # 强制关闭
                        print(f"强制关闭进程 PID {process.pid}")
                        if os.name != 'nt':
                            os.killpg(os.getpgid(process.pid), signal.SIGKILL)
                        else:
                            process.kill()
                        terminated_count += 1
                else:
                    terminated_count += 1

            except Exception as e:
                print(f"关闭进程 PID {process.pid} 时出错: {e}")

        print(f"成功关闭了 {terminated_count} 个进程")
        self.chrome_processes.clear()
        self.process_info.clear()
    
    def run(self):
        """主运行方法"""
        try:
            print("=== Chrome批量启动器 ===")
            print(f"URL文件: {self.url_file}")
            
            # 启动所有浏览器
            self.launch_all_browsers()
            
            # 等待用户操作
            self.wait_for_user_input()
            
        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
        except Exception as e:
            print(f"\n程序运行出错: {e}")
        finally:
            # 清理所有进程
            self.cleanup_processes()
            print("\n程序结束")

def main():
    """主函数"""
    url_file = "test_urls_pre.txt"
    device_name = "iPhone 13 Pro"

    # 检查命令行参数
    if len(sys.argv) > 1:
        url_file = sys.argv[1]
    if len(sys.argv) > 2:
        device_name = sys.argv[2]

    print(f"使用设备模拟: {device_name}")
    print("可用设备列表: iPhone 13 Pro, iPhone 12, iPad Pro, Samsung Galaxy S21, Pixel 5 等")

    launcher = ChromeBatchLauncher(url_file, device_name)
    launcher.run()

if __name__ == "__main__":
    main()
