# TK聊天机器人循环压力测试使用说明

## 新增功能概述

现在 `stress_test.py` 脚本已经支持**循环压力测试模式**，具备以下新功能：

### ✨ 主要特性

1. **自动保活**：进程完成任务或异常退出后自动重启
2. **智能监控**：实时监控所有进程状态
3. **可配置重试**：支持设置最大重试次数和重启延迟
4. **优雅停止**：支持 `Ctrl+C` 优雅停止所有进程
5. **详细通知**：钉钉通知包含启动、完成、失败、重启等状态

## 使用方法

### 1. 传统模式（一次性压测）

```bash
# 传统模式，10个进程，测试完成后退出
python stress_test.py --concurrent 10 --test-mode
```

### 2. 循环模式（新功能）

```bash
# 启用循环压测，进程完成或失败后自动重启
python stress_test.py --concurrent 10 --test-mode --loop

# 自定义重启延迟（30秒）
python stress_test.py --concurrent 10 --test-mode --loop --restart-delay 30

# 设置最大重试次数（每个进程最多重启5次）
python stress_test.py --concurrent 10 --test-mode --loop --max-retries 5

# 完整配置示例
python stress_test.py \
  --concurrent 10 \
  --test-mode \
  --loop \
  --restart-delay 15 \
  --max-retries 3 \
  --webhook-url "你的钉钉webhook地址"
```

## 参数说明

### 新增循环压测参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--loop` | 标志 | False | 启用循环压力测试模式 |
| `--restart-delay` | 整数 | 10 | 进程重启延迟时间（秒） |
| `--max-retries` | 整数 | -1 | 每个进程最大重试次数（-1=无限） |

### 原有参数保持不变

| 参数 | 说明 |
|------|------|
| `--concurrent` | 并发进程数 |
| `--test-mode` | 测试模式（每个进程只测试1个问题） |
| `--questions-file` | 问题文件路径 |
| `--webhook-url` | 钉钉通知地址 |
| `--tickets-config` | ticket配置文件 |

## 运行场景示例

### 场景1：短期压测验证
```bash
# 10个进程，每个进程最多重试2次，重启延迟5秒
python stress_test.py --concurrent 10 --test-mode --loop --max-retries 2 --restart-delay 5
```

### 场景2：长期稳定性测试
```bash
# 5个进程，无限重试，重启延迟30秒（避免过于频繁重启）
python stress_test.py --concurrent 5 --loop --restart-delay 30
```

### 场景3：夜间无人值守压测
```bash
# 8个进程，无限重试，带钉钉通知
python stress_test.py \
  --concurrent 8 \
  --loop \
  --restart-delay 20 \
  --webhook-url "https://oapi.dingtalk.com/robot/send?access_token=xxx"
```

## 监控和通知

### 钉钉通知内容

循环模式下，你会收到以下类型的通知：

1. **循环压测启动**
   ```
   🎯 循环压力测试启动
   并发进程数: 10
   重启延迟: 10秒
   最大重试: 无限
   启动时间: 2025-01-20 10:00:00
   模式: 循环保活模式
   ```

2. **进程启动**
   ```
   🚀 进程 1 启动
   第: 1 次运行
   重试次数: 0
   启动时间: 10:05:30
   分配ticket: 61653113-3c5f-4bdb-9f73-3be15272d1ca
   ```

3. **进程完成**
   ```
   ✅ 进程 1 完成
   第: 1 次运行
   耗时: 45.2秒
   开始: 10:05:30
   结束: 10:06:15
   ```

4. **进程异常**
   ```
   ❌ 进程 1 异常退出
   返回码: 1
   第: 1 次运行
   重试次数: 1
   耗时: 12.5秒
   错误: 无法进入聊天界面: 未找到已知的输入框元素
   ```

5. **达到重试限制**
   ```
   ⚠️ 进程 1 达到重试限制
   最大重试次数: 5
   总运行次数: 6
   停止重启
   ```

## 优雅停止

### 方法1：键盘中断
```bash
# 运行中按 Ctrl+C
^C
收到停止信号...
正在停止循环压力测试...
正在停止所有进程...
进程 1: 已停止
进程 2: 已停止
...
进程监督器已停止
循环压力测试已停止
```

### 方法2：系统信号
```bash
# 发送SIGTERM信号
kill -TERM <pid>
```

## 日志文件

### 主日志
- 位置：`stress_test.log`
- 内容：总体压测状态、进程启动/停止、监督器状态

### 进程日志
- 位置：`stress_process_N/logs/chat_log_*.log`
- 内容：每个进程的详细运行日志

### 实时监控
```bash
# 监控主日志
tail -f stress_test.log

# 监控特定进程
tail -f stress_process_1/logs/chat_log_*.log
```

## 故障处理

### 常见问题和解决方案

1. **所有进程都异常退出**
   - 检查网站是否可访问
   - 确认ticket配置是否正确
   - 查看进程日志了解具体错误

2. **进程频繁重启**
   - 增加 `--restart-delay` 延迟时间
   - 设置 `--max-retries` 限制重试次数
   - 检查网络稳定性

3. **内存使用过高**
   - 减少 `--concurrent` 并发数
   - 增加重启延迟，避免同时启动过多进程

4. **停止响应慢**
   - 正常情况下停止需要5-30秒
   - 如果超过1分钟未响应，可以强制杀死进程

## 最佳实践

### 推荐配置

1. **开发测试**
   ```bash
   python stress_test.py --concurrent 3 --test-mode --loop --max-retries 2 --restart-delay 5
   ```

2. **生产压测**
   ```bash
   python stress_test.py --concurrent 8 --loop --restart-delay 15 --webhook-url "钉钉地址"
   ```

3. **稳定性测试**
   ```bash
   python stress_test.py --concurrent 5 --loop --restart-delay 30 --max-retries 10
   ```

### 监控建议

1. 设置钉钉通知，及时了解进程状态
2. 定期检查日志文件，分析失败原因
3. 监控系统资源使用情况
4. 合理设置重试次数，避免无效重试

## 解决原有问题

针对你遇到的"无法进入聊天界面"问题，循环模式提供了以下解决方案：

1. **自动重试**：进程异常退出后自动重启，无需人工干预
2. **智能延迟**：重启前等待一段时间，给系统恢复机会
3. **重试限制**：避免无限重试浪费资源
4. **实时监控**：及时发现和处理问题

现在你可以启动10个进程的循环压测，即使某些进程因为"输入框元素"问题退出，也会自动重启继续压测！ 