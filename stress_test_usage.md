# TikaBot聊天自动化压力测试脚本使用说明

## 概述

`stress_test.py` 是基于 `tk_chat_automation.py` 开发的压力测试脚本，支持多个浏览器实例并发运行聊天自动化，用于测试系统在高并发场景下的稳定性和性能。

## 主要特性

### 🔥 核心功能
- **多进程并发**: 支持最多50个并发浏览器实例
- **独立工作空间**: 每个进程使用独立的目录，避免文件冲突
- **实时监控**: 监控系统资源使用情况(CPU、内存、磁盘)
- **压测专用通知**: 针对压力测试优化的钉钉通知功能
- **详细报告**: 生成JSON和HTML格式的测试报告

### 📊 监控统计
- 进程成功率统计
- 实时系统资源监控
- 每个进程的运行时长记录
- 失败进程的错误信息收集

### 🗂️ 文件管理
- 自动创建独立工作目录
- 自动清理过期测试数据
- 支持问题文件的自动分发

## 安装依赖

```bash
pip install psutil requests playwright
```

## 使用方法

### 基础用法

```bash
# 运行5个并发进程的压力测试
python stress_test.py

# 指定并发数量
python stress_test.py --concurrent 10

# 测试模式(快速测试)
python stress_test.py --test-mode --concurrent 3
```

### 高级参数

```bash
# 完整参数示例
python stress_test.py \
    --concurrent 10 \
    --questions-file questions.txt \
    --access-mode random \
    --test-mode \
    --webhook-url "https://oapi.dingtalk.com/robot/send?access_token=YOUR_TOKEN" \
    --duration 30
```

### 参数说明

| 参数 | 简写 | 类型 | 默认值 | 说明 |
|------|------|------|--------|------|
| `--concurrent` | `-c` | int | 5 | 并发进程数量(1-50) |
| `--questions-file` | | str | | 问题文件路径 |
| `--access-mode` | | str | sequential | 问题访问模式(random/sequential) |
| `--test-mode` | | flag | False | 测试模式，只运行少量对话 |
| `--webhook-url` | | str | | 钉钉机器人Webhook URL |
| `--duration` | | int | | 测试持续时间(分钟) |
| `--cleanup` | | flag | False | 清理旧的测试数据 |
| `--cleanup-days` | | int | 7 | 保留最近几天的数据 |

## 目录结构

压力测试会创建以下目录结构：

```
project/
├── stress_test.py              # 压力测试脚本
├── tk_chat_automation.py       # 原始自动化脚本
├── stress_logs/                # 压力测试日志
│   └── stress_test_20240101_120000.log
├── stress_reports/             # 测试报告
│   ├── stress_test_report_20240101_120000.json
│   └── stress_test_report_20240101_120000.html
├── stress_process_1/           # 进程1工作目录
│   ├── screenshots/
│   ├── results/
│   ├── logs/
│   └── questions.txt
├── stress_process_2/           # 进程2工作目录
│   └── ...
└── ...
```

## 钉钉通知功能

### 压测专用通知

脚本会发送以下类型的钉钉通知：

1. **开始通知**: 测试开始时发送基本信息
2. **进程通知**: 每个进程完成时发送结果
3. **完成通知**: 全部测试完成时发送汇总

### 通知示例

```markdown
🔥 **压力测试** 开始

🚀 **压力测试开始**
**并发数**: 10
**开始时间**: 2024-01-01 12:00:00
**测试模式**: 是
**问题文件**: questions.txt
**访问模式**: 顺序
**持续时间**: 无限制分钟
```

## 系统监控

### 资源监控

脚本会每30秒监控一次系统资源：

- CPU使用率
- 内存使用率和已用/总量
- 磁盘使用率
- 运行中的进程数量

### 警告阈值

- CPU使用率 > 90%：记录警告
- 内存使用率 > 90%：记录警告

## 测试报告

### JSON报告结构

```json
{
  "test_info": {
    "start_time": "2024-01-01T12:00:00",
    "end_time": "2024-01-01T12:30:00",
    "total_duration_seconds": 1800,
    "concurrent_count": 10
  },
  "summary": {
    "total_processes": 10,
    "successful_processes": 8,
    "failed_processes": 2,
    "success_rate": 80.0
  },
  "process_results": [
    {
      "process_id": 1,
      "success": true,
      "duration_seconds": 450.2,
      "start_time": "2024-01-01T12:00:00",
      "end_time": "2024-01-01T12:07:30",
      "process_dir": "/path/to/stress_process_1",
      "error": null
    }
  ]
}
```

### HTML报告特性

- 响应式设计，支持各种设备
- 可视化统计卡片
- 详细的进程列表表格
- 成功/失败状态高亮显示

## 性能建议

### 内存要求

- 每个进程大约需要 0.5GB 内存
- 建议系统至少有 `并发数 × 0.5GB + 2GB` 的可用内存

### CPU要求

- 推荐 CPU 核心数 ≥ 并发数 / 2
- 避免在CPU使用率已经很高的系统上运行大并发测试

### 并发数量建议

| 系统配置 | 建议并发数 |
|----------|------------|
| 4核8GB | 5-8 |
| 8核16GB | 10-15 |
| 16核32GB | 20-30 |

## 故障排查

### 常见问题

1. **进程启动失败**
   - 检查 `tk_chat_automation.py` 是否存在
   - 检查依赖是否正确安装
   - 查看进程目录中的错误日志

2. **内存不足**
   - 减少并发数量
   - 关闭其他占用内存的程序
   - 使用 `--test-mode` 进行轻量测试

3. **文件权限问题**
   - 确保脚本有读写当前目录的权限
   - 检查是否有足够的磁盘空间

### 日志查看

```bash
# 查看压力测试主日志
tail -f stress_logs/stress_test_YYYYMMDD_HHMMSS.log

# 查看特定进程日志
tail -f stress_process_1/logs/chat_log_YYYYMMDD_HHMMSS.log
```

## 清理功能

### 自动清理

```bash
# 清理7天前的旧数据
python stress_test.py --cleanup

# 清理3天前的旧数据
python stress_test.py --cleanup --cleanup-days 3
```

### 手动清理

```bash
# 删除所有压力测试相关目录
rm -rf stress_process_* stress_logs/ stress_reports/
```

## 最佳实践

### 1. 渐进式测试

从少量并发开始，逐步增加：

```bash
# 第一轮：小规模测试
python stress_test.py --concurrent 3 --test-mode

# 第二轮：中等规模
python stress_test.py --concurrent 10

# 第三轮：大规模
python stress_test.py --concurrent 20
```

### 2. 监控系统状态

在压力测试过程中，使用系统监控工具：

```bash
# 监控CPU和内存
htop

# 监控磁盘IO
iotop

# 监控网络
iftop
```

### 3. 定期清理

定期清理测试数据以节省磁盘空间：

```bash
# 每周清理一次
0 2 * * 0 cd /path/to/project && python stress_test.py --cleanup
```

## 环境变量

脚本会设置以下环境变量传递给子进程：

- `STRESS_TEST_MODE=true`: 标识当前为压力测试模式
- `STRESS_PROCESS_ID=N`: 当前进程的ID

原始脚本可以通过这些环境变量来调整行为。

## 注意事项

1. **资源消耗**: 大并发测试会消耗大量系统资源
2. **网络限制**: 注意目标服务的访问频率限制
3. **数据存储**: 大量并发会产生大量日志和截图文件
4. **权限要求**: 确保有足够的文件系统权限
5. **防火墙**: 确保网络连接正常

## 技术支持

如果遇到问题，请提供以下信息：

1. 压力测试日志文件
2. 系统配置信息(CPU、内存)
3. 具体的错误信息
4. 使用的命令参数 