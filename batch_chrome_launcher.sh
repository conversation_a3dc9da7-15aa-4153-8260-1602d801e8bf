#!/bin/bash

# Chrome批量启动脚本 (Bash版本)
# 使用方法: ./batch_chrome_launcher.sh [url_file]

URL_FILE="${1:-test_urls_pre.txt}"
CHROME_PIDS=()
TEMP_DIR="/tmp/chrome_batch_$$"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 清理函数
cleanup() {
    echo -e "\n${YELLOW}开始关闭所有Chrome进程...${NC}"
    
    for pid in "${CHROME_PIDS[@]}"; do
        if kill -0 "$pid" 2>/dev/null; then
            echo -e "${BLUE}关闭进程 PID: $pid${NC}"
            kill -TERM "$pid" 2>/dev/null
            
            # 等待进程结束
            sleep 2
            if kill -0 "$pid" 2>/dev/null; then
                echo -e "${RED}强制关闭进程 PID: $pid${NC}"
                kill -KILL "$pid" 2>/dev/null
            fi
        fi
    done
    
    # 清理临时目录
    if [ -d "$TEMP_DIR" ]; then
        rm -rf "$TEMP_DIR"
    fi
    
    echo -e "${GREEN}所有进程已关闭${NC}"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM EXIT

# 查找Chrome可执行文件
find_chrome() {
    local chrome_paths=(
        "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
        "/Applications/Chromium.app/Contents/MacOS/Chromium"
        "/usr/bin/google-chrome"
        "/usr/bin/google-chrome-stable"
        "/usr/bin/chromium-browser"
        "/usr/bin/chromium"
    )
    
    for path in "${chrome_paths[@]}"; do
        if [ -x "$path" ]; then
            echo "$path"
            return 0
        fi
    done
    
    # 尝试使用which命令
    if command -v google-chrome >/dev/null 2>&1; then
        echo "google-chrome"
        return 0
    elif command -v chrome >/dev/null 2>&1; then
        echo "chrome"
        return 0
    elif command -v chromium >/dev/null 2>&1; then
        echo "chromium"
        return 0
    fi
    
    echo ""
    return 1
}

# 显示进程状态
show_status() {
    echo -e "\n${BLUE}=== 进程状态 ===${NC}"
    local running=0
    local total=${#CHROME_PIDS[@]}
    
    for i in "${!CHROME_PIDS[@]}"; do
        local pid=${CHROME_PIDS[$i]}
        if kill -0 "$pid" 2>/dev/null; then
            echo -e "${GREEN}✓ 运行中${NC} - PID: $pid (URL $((i+1)))"
            ((running++))
        else
            echo -e "${RED}✗ 已结束${NC} - PID: $pid (URL $((i+1)))"
        fi
    done
    
    echo -e "${BLUE}总计: $total 个进程，$running 个正在运行${NC}"
}

# 显示帮助
show_help() {
    echo -e "\n${BLUE}=== 帮助信息 ===${NC}"
    echo "可用命令:"
    echo "  status, s  - 查看进程状态"
    echo "  quit, q    - 关闭所有进程并退出"
    echo "  help, h    - 显示帮助信息"
    echo "  Ctrl+C     - 强制退出"
}

# 主函数
main() {
    echo -e "${BLUE}=== Chrome批量启动器 (Bash版) ===${NC}"
    echo "URL文件: $URL_FILE"
    
    # 检查URL文件是否存在
    if [ ! -f "$URL_FILE" ]; then
        echo -e "${RED}错误: URL文件不存在: $URL_FILE${NC}"
        exit 1
    fi
    
    # 查找Chrome
    CHROME_PATH=$(find_chrome)
    if [ -z "$CHROME_PATH" ]; then
        echo -e "${RED}错误: 未找到Chrome浏览器${NC}"
        echo "请确保已安装 Google Chrome 或 Chromium"
        exit 1
    fi
    
    echo -e "${GREEN}找到Chrome: $CHROME_PATH${NC}"
    
    # 创建临时目录
    mkdir -p "$TEMP_DIR"
    
    # 读取URL并启动Chrome
    local count=0
    while IFS= read -r url || [ -n "$url" ]; do
        # 跳过空行和注释行
        if [ -z "$url" ] || [[ "$url" =~ ^[[:space:]]*# ]]; then
            continue
        fi
        
        ((count++))
        echo -e "${YELLOW}启动浏览器 $count: $url${NC}"
        
        # 为每个进程创建独立的用户数据目录
        local profile_dir="$TEMP_DIR/profile_$count"
        mkdir -p "$profile_dir"
        
        # 启动Chrome进程
        "$CHROME_PATH" \
            --new-window \
            --no-first-run \
            --no-default-browser-check \
            --user-data-dir="$profile_dir" \
            --disable-web-security \
            --disable-features=VizDisplayCompositor \
            "$url" \
            >/dev/null 2>&1 &
        
        local chrome_pid=$!
        CHROME_PIDS+=("$chrome_pid")
        
        echo -e "${GREEN}启动成功 - PID: $chrome_pid${NC}"
        sleep 1  # 稍微延迟避免同时启动太多进程
        
    done < "$URL_FILE"
    
    echo -e "\n${GREEN}成功启动了 ${#CHROME_PIDS[@]} 个Chrome浏览器进程${NC}"
    show_status
    
    # 等待用户输入
    echo -e "\n${YELLOW}所有浏览器已启动完成！${NC}"
    echo "请在各个浏览器中完成您的操作..."
    echo -e "\n可用命令: ${BLUE}status${NC}, ${BLUE}quit${NC}, ${BLUE}help${NC}"
    
    while true; do
        echo -n -e "\n${BLUE}请输入命令 (输入 'quit' 关闭所有进程): ${NC}"
        read -r input
        
        case "${input,,}" in  # 转换为小写
            "quit"|"q"|"exit")
                break
                ;;
            "status"|"s")
                show_status
                ;;
            "help"|"h")
                show_help
                ;;
            "")
                # 空输入，继续循环
                ;;
            *)
                echo -e "${RED}未知命令: $input${NC}"
                echo -e "输入 ${BLUE}'help'${NC} 查看可用命令"
                ;;
        esac
    done
}

# 运行主函数
main "$@"
