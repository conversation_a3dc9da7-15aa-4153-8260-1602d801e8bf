# TK Chat 聊天模式适配指南

## 📋 问题分析

### 1. 原始问题
- 脚本在"无法进入聊天界面: 未找到已知的输入框元素"时崩溃
- 现在聊天页面有两种模式：**文本模式**和**数字人模式**
- 需要适配不同模式下的输入框及发送按钮

### 2. 已修复的错误
- ✅ 修复了保活功能的 `'tuple' object has no attribute 'set'` 错误
- ✅ 修复了multiple保活函数调用不一致的问题

## 🔧 解决方案

### 1. 多模式选择器支持

基于HTML文件分析，我们发现：
- **两种模式使用相同的输入框结构**：`input.flex.h-9.w-full.rounded-md.border.border-input.bg-transparent`
- **发送按钮位置**：`.fixed.bottom-6 button` 或 `[class*="max-w-3xl"] button`

### 2. 新增的自适应函数

#### 输入框查找函数 `find_input_element_adaptive()`
按优先级尝试以下选择器：
1. `input.flex.h-9.w-full.rounded-md.border.border-input.bg-transparent` (最高优先级)
2. `input.flex.h-9.w-full`
3. `input[class*="border-input"]`
4. `input[placeholder*="快来"]` / `input[placeholder*="聊"]`
5. 通用备选选择器

#### 发送按钮查找函数 `find_send_button_adaptive()`
按优先级尝试以下选择器：
1. `.fixed.bottom-6 button`
2. `[class*="max-w-3xl"] button`
3. `button.inline-flex`
4. SVG图标按钮等

## 📁 文件说明

### 创建的文件
1. **`chat_mode_selectors.py`** - 选择器配置文件
2. **`tk_chat_automation_adaptive.py`** - 自适应版本的主脚本
3. **`analyze_chat_modes.py`** - 模式分析工具
4. **`simple_debug.py`** - 简单调试工具
5. **`tk_chat_automation_optimized.py`** - 完整的优化测试版本

### 备份文件
- **`tk_chat_automation_backup.py`** - 原脚本的备份

## 🚀 使用方法

### 方法1：使用自适应版本（推荐）
```bash
conda activate tk_chat
python3 tk_chat_automation_adaptive.py --test-mode
```

### 方法2：手动调试（浏览器兼容性问题时）
1. 手动打开聊天页面
2. 使用F12打开开发者工具
3. 在Console中运行测试代码：

```javascript
// 测试输入框选择器
const inputSelectors = [
    'input.flex.h-9.w-full.rounded-md.border.border-input.bg-transparent',
    'input.flex.h-9.w-full',
    'input[class*="border-input"]',
    'input[placeholder*="快来"]'
];

inputSelectors.forEach((selector, index) => {
    const elements = document.querySelectorAll(selector);
    console.log(`${index + 1}. ${selector} - 找到 ${elements.length} 个元素`);
    if (elements.length > 0) {
        elements.forEach((el, i) => {
            console.log(`   元素${i + 1}: 可见=${el.offsetParent !== null}, placeholder="${el.placeholder}"`);
        });
    }
});
```

### 方法3：压力测试
```bash
# 使用更新后的脚本进行压力测试
conda activate tk_chat
python3 stress_test.py --concurrent 2 --questions-file questions.json --loop --restart-delay 60
```

## 💡 调试建议

### 1. 如果浏览器崩溃
- 尝试使用不同的启动参数
- 使用手动调试方法
- 检查系统Chrome版本兼容性

### 2. 如果仍然找不到输入框
- 检查页面是否完全加载
- 手动测试选择器是否正确
- 查看是否有新的页面结构变化

### 3. 日志查看
- 查看 `successful_selectors.log` 了解成功的选择器
- 查看脚本日志文件了解详细执行过程

## 🔍 测试步骤

1. **启动脚本**
2. **手动切换模式**（如果需要）
3. **观察日志输出**，确认选择器是否正确识别
4. **检查截图**，确认聊天功能正常

## ⚠️ 注意事项

1. **环境要求**：确保使用 `conda activate tk_chat`
2. **网络稳定**：确保网络连接稳定
3. **页面等待**：给页面足够的加载时间
4. **模式切换**：在测试前确认当前聊天模式

## 📞 后续支持

如果遇到新问题：
1. 提供具体的错误日志
2. 提供当前页面的HTML结构截图
3. 说明具体的操作步骤和期望结果

---

**更新时间**: 2025-07-23  
**版本**: v2.0 - 多模式适配版本 