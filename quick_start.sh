#!/bin/bash

# Chrome批量启动器 - 快速启动脚本

echo "=== Chrome批量启动器 - 快速启动 ==="
echo ""

# 检查Python
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到Python3"
    exit 1
fi

# 检查Chrome
if [ ! -f "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome" ] && ! command -v google-chrome &> /dev/null; then
    echo "❌ 错误: 未找到Chrome浏览器"
    echo "请先安装Google Chrome"
    exit 1
fi

echo "✅ 环境检查通过"
echo ""

# 显示选项
echo "请选择启动方式:"
echo "1. 小测试 (3个URL) - iPhone 13 Pro"
echo "2. 小测试 (3个URL) - iPad Pro"
echo "3. 完整测试 (40个URL) - iPhone 13 Pro"
echo "4. 完整测试 (40个URL) - iPad Pro"
echo "5. 自定义"
echo ""

read -p "请输入选择 (1-5): " choice

case $choice in
    1)
        echo "启动小测试 - iPhone 13 Pro..."
        python3 batch_chrome_simple_mobile.py test_urls_small.txt "iPhone 13 Pro"
        ;;
    2)
        echo "启动小测试 - iPad Pro..."
        python3 batch_chrome_simple_mobile.py test_urls_small.txt "iPad Pro"
        ;;
    3)
        echo "启动完整测试 - iPhone 13 Pro..."
        python3 batch_chrome_simple_mobile.py test_urls_pre.txt "iPhone 13 Pro"
        ;;
    4)
        echo "启动完整测试 - iPad Pro..."
        python3 batch_chrome_simple_mobile.py test_urls_pre.txt "iPad Pro"
        ;;
    5)
        echo ""
        echo "可用设备类型:"
        echo "- iPhone 13 Pro"
        echo "- iPhone 12"
        echo "- iPad Pro"
        echo "- Samsung Galaxy S21"
        echo ""
        read -p "请输入URL文件名: " url_file
        read -p "请输入设备类型: " device_type
        
        if [ ! -f "$url_file" ]; then
            echo "❌ 错误: 文件 $url_file 不存在"
            exit 1
        fi
        
        echo "启动自定义配置..."
        python3 batch_chrome_simple_mobile.py "$url_file" "$device_type"
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

echo ""
echo "🎉 启动完成！"
echo ""
echo "💡 提示: 如需完整的移动设备模拟效果，请在每个浏览器中:"
echo "   1. 按 F12 打开开发者工具"
echo "   2. 按 Ctrl+Shift+M (macOS: Cmd+Shift+M) 切换到设备模拟模式"
echo "   3. 或运行: python3 auto_enable_mobile_mode.py"
