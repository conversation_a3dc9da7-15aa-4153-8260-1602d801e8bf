# TK Chat 文本模式优化更新说明

## 📋 更新背景

根据用户反馈，文本模式下的发送按钮、获取回复文本的HTML结构发生了变化，需要优化脚本以兼容新的结构。

### 新的HTML结构分析

1. **发送按钮**（文本模式）：
   ```html
   <button class="gap-2 whitespace-nowrap rounded-md text-sm font-medium focus-visible:outline-none focus-visible:ring-1:shadow-none focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:text-accent-foreground p-0 overflow-hidden hover:bg-transparent flex items-center justify-center transition-opacity duration-200 animate-button-in h-6 max-w-6" aria-label="发送消息">
     <img alt="箭头向上-黑" loading="lazy" width="16" height="16" decoding="async" data-nimg="1" class="size-6" src="/_next/static/media/arrow_up_black.44fc03ff.png" style="color: transparent;">
   </button>
   ```

2. **发送中的停止按钮**：
   ```html
   <button class="... animate-button-out ..." aria-label="停止连接">
     <img alt="停止-黑" loading="lazy" width="16" height="16" decoding="async" data-nimg="1" class="size-6" src="/_next/static/media/circle_stop_black.9496d1a7.png" style="color: transparent;">
   </button>
   ```

3. **回复文本**（新格式）：
   ```html
   <p node="[object Object]" class="text-sm text-justify break-all">2025年流行色以"未来黄昏"（Future Dusk）为主打...</p>
   ```

## 🔧 主要优化内容

### 1. 更新发送按钮选择器

在 `tk_chat_automation_adaptive.py` 中更新了 `find_send_button_adaptive()` 函数：

**新增的高优先级选择器**：
- `button[aria-label="发送消息"]` - 基于aria-label属性
- `button img[alt="箭头向上-黑"]` - 基于图片alt属性
- `button img[src*="arrow_up_black"]` - 基于图片源文件
- `button.animate-button-in` - 基于动画class

**改进的选择器处理逻辑**：
- 新增对图片按钮的专门处理
- 改进SVG按钮的查找逻辑
- 增加错误处理的健壮性

### 2. 新增发送状态检测功能

新增了三个关键函数：

#### `check_sending_status(page)`
- 检测是否正在发送消息
- 基于停止按钮的可见性判断发送状态

#### `wait_for_sending_complete(page, max_wait_time=30)`
- 等待消息发送完成
- 取代原有的硬编码等待逻辑
- 支持自定义超时时间

#### `get_latest_reply(page, timeout=10)`
- 智能获取最新回复文本
- 支持新的回复HTML结构
- 多选择器优先级匹配

### 3. 更新主要聊天逻辑

修改了两个关键位置的聊天代码：

1. **图片+文本消息发送逻辑**（约第2436行）
2. **普通文本消息发送逻辑**（约第2907行）

**主要改进**：
- 使用 `find_input_element_adaptive()` 替代硬编码输入框选择器
- 使用 `find_send_button_adaptive()` 替代硬编码发送按钮选择器
- 使用 `wait_for_sending_complete()` 替代旧的等待逻辑

## 📊 选择器优先级配置

### 发送按钮选择器（按优先级）

```python
send_selectors = [
    # 新的文本模式HTML结构（最高优先级）
    'button[aria-label="发送消息"]',
    'button img[alt="箭头向上-黑"]',
    'button img[src*="arrow_up_black"]',
    'button.animate-button-in',
    
    # 原有选择器（保持兼容性）
    '.fixed.bottom-6 button',
    '[class*="max-w-3xl"] button',
    'button.inline-flex',
    # ... 其他选择器
]
```

### 发送状态检测选择器

```python
sending_status_selectors = [
    'button[aria-label="停止连接"]',
    'button img[alt="停止-黑"]',
    'button img[src*="circle_stop_black"]',
    'button.animate-button-out'
]
```

### 回复文本选择器

```python
reply_text_selectors = [
    # 新的HTML结构（最高优先级）
    'p.text-sm.text-justify.break-all',
    'p[node]',
    'p.text-sm',
    
    # 原有选择器（保持兼容性）
    '.message-content',
    '.chat-message',
    '.reply-text',
    # ... 其他选择器
]
```

## 🚀 使用方法

### 测试自适应功能

```bash
# 启动测试模式
conda activate tk_chat
python3 tk_chat_automation_adaptive.py --test-mode
```

### 运行压力测试

```bash
# 使用更新后的脚本进行压力测试
python3 stress_test.py --concurrent 2 --questions-file questions.json --loop --restart-delay 60
```

## 💡 技术亮点

### 1. 智能图片按钮处理
- 能够识别包含图片的按钮元素
- 通过图片属性（alt、src）定位父级按钮
- 支持多种图片按钮格式

### 2. 实时发送状态监控
- 动态检测发送状态变化
- 避免硬编码等待时间
- 提高响应速度和准确性

### 3. 向后兼容设计
- 新选择器优先级最高
- 保留原有选择器作为备选
- 渐进式优化，降低风险

### 4. 错误处理增强
- 多层次异常捕获
- 详细的调试日志输出
- 优雅的失败回退机制

## 📝 日志和调试

优化后的脚本提供更详细的日志输出：

```
🔍 智能查找发送按钮...
尝试发送按钮选择器 1/15: button[aria-label="发送消息"]
✅ 通过图片找到发送按钮: button img[alt="箭头向上-黑"]
使用自适应方法点击发送按钮
🔄 检测到发送中状态: button[aria-label="停止连接"]
⏳ 等待消息发送完成...
✅ 消息发送完成
📝 获取最新回复...
✅ 获取到回复文本: 2025年流行色以"未来黄昏"...
```

## ⚠️ 注意事项

1. **环境要求**：确保使用正确的conda环境
2. **网络稳定性**：新的发送状态检测需要稳定的网络连接
3. **页面加载时间**：给页面足够的时间加载新的HTML结构
4. **兼容性测试**：建议先在测试环境验证后再用于生产

## 🔮 后续计划

1. **性能监控**：收集新选择器的成功率数据
2. **持续优化**：根据实际使用情况调整选择器优先级
3. **功能扩展**：考虑支持更多聊天模式和交互方式

---

**更新时间**: 2025-07-23  
**版本**: v3.0 - 文本模式适配优化版本  
**文件**: `tk_chat_automation_adaptive.py` 