# 更新日志

## [2024-01-XX] - AI问题生成功能 🆕

### 🎯 重大新功能
- **AI生成问题**: 新增 `ai_generated` 问题类型，支持使用AI模型动态生成问题变种
- **多API支持**: 支持Ollama、OpenAI、自定义API等多种AI服务
- **智能问题扩展**: 根据领域、意图类型和示例自动生成多样化问题
- **智能后备机制**: AI生成失败时自动使用示例问题

### 📝 AI生成问题配置

#### 数据结构
```json
{
    "question_type": "ai_generated",
    "content": {
        "domain": "时尚领域",
        "intent_type": "否定意图",
        "example_question": "帮我推荐一件适合运动的衣服，我不喜欢黑色",
        "count": 5,
        "api_config": {
            "provider": "ollama",
            "model": "qwen2.5:7b",
            "base_url": "http://localhost:11434"
        }
    }
}
```

#### 支持的API提供商
- **Ollama**: 本地部署的AI模型
- **OpenAI**: ChatGPT API
- **自定义API**: 兼容任意REST API

### 🔧 技术实现
- 新增 `generate_ai_questions()` 核心生成函数
- 新增 `build_question_generation_prompt()` 智能提示词构建
- 新增 `parse_generated_questions()` 结果解析功能
- 新增 `process_ai_generated_question()` 问题处理流程
- 增强 `format_question_for_execution()` 支持AI生成类型

### 🎨 智能提示词设计
- 结构化任务要求和生成规则
- JSON格式输出保证解析准确性
- 意图类型和领域约束确保相关性
- 多种文本解析策略提高容错性

### 📊 使用场景示例
1. **时尚推荐** - 否定意图: "不喜欢XX颜色的XX"
2. **美食推荐** - 特殊需求: "不辣/素食/无过敏源的XX"
3. **购物咨询** - 功能需求: "性价比高/实用/美观的XX"
4. **服务咨询** - 限制条件: "适合XX人群/XX场合的XX"

### ✅ 测试验证
- ✅ Ollama API连接和调用测试
- ✅ 问题生成和解析测试  
- ✅ 统一格式加载和格式化测试
- ✅ 元数据记录和对话日志测试
- ✅ 后备机制测试

### 📚 文档更新
- 更新 `README_questions.md` 添加AI生成问题详细说明
- 提供多种API配置示例
- 增加环境要求和使用建议

## [2024-01-XX] - 问题数据结构统一改造

### 🎯 新增功能
- **统一问题格式**: 重新设计 `questions.json` 数据结构
- **向后兼容**: 完全支持旧格式文件，无需迁移现有配置
- **类型区分**: 通过 `question_type` 字段统一管理不同类型问题

### 📝 数据结构变更

#### 新格式 (推荐)
```json
{
    "questions": [
        {
            "question_type": "single",
            "content": "单个问题文本"
        },
        {
            "question_type": "multi",
            "content": ["问题1", "问题2", "问题3"]
        },
        {
            "question_type": "image_text",
            "content": {
                "text": "图文问题文本",
                "image": "图片路径或URL",
                "imageType": "url"
            }
        }
    ],
    "mode": "sequential"
}
```

#### 旧格式 (仍支持)
```json
{
    "single_questions": [...],
    "multi_questions": [...],
    "image_text_questions": [...],
    "mode": "sequential"
}
```

### 🔧 技术改进
- 新增 `load_questions()` 函数自动检测格式类型
- 新增 `get_next_question_unified()` 函数处理统一格式
- 新增 `format_question_for_execution()` 格式转换函数
- 保持所有现有功能和API的兼容性

### 📚 文档更新
- 新增 `README_questions.md` 使用说明
- 提供完整的配置示例和最佳实践
- 包含图片路径配置说明

### ✅ 测试验证
- ✅ 新格式加载和执行测试通过
- ✅ 旧格式兼容性测试通过
- ✅ 问题选择和格式化测试通过
- ✅ 三种问题类型（单问题、多问题、图文问题）全部支持

### 🚀 使用建议
1. **新项目**: 直接使用新的统一格式
2. **现有项目**: 无需更改，继续使用现有配置文件
3. **迁移计划**: 可随时将旧格式迁移到新格式，获得更好的管理体验

### 📁 文件说明
- `questions.json` - 新格式示例配置
- `questions_old_format.json` - 旧格式兼容示例  
- `README_questions.md` - 详细使用说明

### 🎉 影响范围
此次更新为**无破坏性更新**，现有用户无需任何操作即可继续使用原有功能。

## v2.5.0 (2024-12-13)

### 新增功能 - AI生成问题报告增强

#### 新增AI生成问题报告展示
- **HTML报告增强**：
  - 🤖 AI生成问题紫色徽章标识
  - ⚠️ 备用问题橙色徽章标识(AI生成失败时)
  - 元数据信息框：显示问题领域和意图类型
  - 统计摘要：正确计算和展示AI生成问题数量

- **Excel报告增强**：
  - 问题类型列：明确标识"🤖 AI生成问题"或"🤖⚠️ AI生成(备用)"
  - 颜色编码：AI生成问题紫色背景，备用问题橙色背景
  - 详细信息：在问题内容中包含AI生成的领域和意图信息
  - 统计信息：增加AI生成问题数量统计

#### 报告解析优化
- **智能识别**：从对话日志中自动解析AI生成问题信息
- **元数据提取**：解析领域、意图类型、备用标记等信息
- **格式兼容**：与现有图文问题和普通问题格式完全兼容

#### 统计分类改进
- **四类问题统计**：
  - AI生成问题数
  - 图文问题数  
  - 普通文本问题数
  - 总问题数
- **准确分类**：避免重复计算，确保统计数据准确

#### 视觉体验提升
- **徽章系统**：不同问题类型使用不同颜色和图标
- **样式优化**：AI生成问题专用CSS样式
- **信息层次**：清晰的元数据展示层次

### 技术改进
- 新增 `extract_ai_generated_info()` 函数用于解析AI生成信息
- 优化报告数据解析逻辑，支持AI生成问题元数据
- 增强HTML和Excel格式生成器的问题类型识别能力

### 文档更新
- 更新README_questions.md添加AI生成问题报告功能说明
- 完善对话日志格式说明
- 增加报告访问和查看指南 

## v2.6.0 (2024-12-13)

### 重大改进 - API配置架构重构

#### 配置文件分离
- **新增独立配置文件**：创建 `ai_config.json` 统一管理AI API配置
- **简化问题配置**：从 `questions.json` 中移除复杂的 `api_config` 配置
- **环境分离**：API配置与问题数据完全分离，便于不同环境部署

#### 配置管理优化
- **默认提供商机制**：支持全局默认API提供商配置
- **问题级覆盖**：可在单个问题中可选指定特定提供商
- **环境变量支持**：API密钥支持环境变量 `${OPENAI_API_KEY}` 格式
- **自动降级机制**：指定提供商不存在时自动使用默认配置

#### 安全性增强
- **敏感信息保护**：API密钥等敏感信息通过环境变量管理
- **配置隔离**：生产环境配置不会意外泄露到问题文件中

#### 可扩展性提升
- **新增提供商配置接口**：`load_ai_config()` 和 `get_ai_api_config()`
- **统一配置格式**：所有API提供商使用统一的配置结构
- **向后兼容**：无配置文件时自动使用内置默认配置

#### 用户体验改进
- **配置简化**：AI生成问题配置更加简洁直观
- **灵活选择**：用户可按需指定API提供商，无需每次配置
- **错误处理**：完善的配置加载失败处理机制

### 技术架构改进
- 新增 `load_ai_config()` 函数用于加载配置文件
- 新增 `get_ai_api_config()` 函数用于获取指定提供商配置
- 重构 `generate_ai_questions()` 函数，移除api_config参数
- 优化 `process_ai_generated_question()` 函数逻辑

### 文件变更
- **新增**：`ai_config.json` - AI API配置文件
- **简化**：`questions.json` - 移除api_config字段
- **更新**：README_questions.md - 新配置方式文档

### 迁移指南
现有用户需要：
1. 创建 `ai_config.json` 文件（可选，无此文件时使用默认配置）
2. 从 `questions.json` 中移除 `api_config` 字段
3. 根据需要设置环境变量（如OPENAI_API_KEY）

### 🎉 影响范围
此次更新为**半破坏性更新**：
- 现有questions.json需要移除api_config字段
- 建议创建ai_config.json进行配置管理
- 代码逻辑完全向后兼容 

## v2.7.0 - 2025-01-10

### 🚀 新增功能
- **AI追问对话类型**: 实现智能多轮对话功能，AI可根据用户回复动态生成后续问题
  - 支持固定轮次、随机轮次和AI智能判断三种模式
  - 维护完整对话历史和上下文理解
  - 灵活的轮次配置：固定数量、随机范围、AI判断结束
  - 专业领域配置，支持不同业务场景
- **智能追问算法**: 基于对话历史生成有针对性的追问问题
- **多轮对话状态管理**: 完整的对话状态保存和恢复机制
- **丰富的配置选项**: 支持领域、场景、轮次等多维度配置

### 📊 报告增强
- HTML和Excel报告新增AI追问对话显示
- 🔄 AI追问对话标识和元数据展示
- 完整的多轮对话历史追踪
- 轮次信息和状态展示

### 🔧 技术改进
- 新增追问问题生成算法和API调用接口
- 完善的对话历史解析和状态管理
- 优化的提示词模板和回复解析机制
- 支持多种AI提供商（Ollama、OpenAI、Custom）

### 📝 文档更新
- README_questions.md新增AI追问对话详细说明
- 完整的配置示例和使用指南
- 工作原理和最佳实践建议

### 🧪 测试
- 新增AI追问对话功能测试脚本
- 多种轮次配置的测试覆盖
- 提示词生成和回复解析测试

---

## v2.6.0 - 2024-12-10 