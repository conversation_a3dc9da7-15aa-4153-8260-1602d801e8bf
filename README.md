# 太古数字人聊天自动化测试

本项目用于自动化测试太古数字人的聊天功能，支持持续运行、自动重启和错误恢复。

## Windows部署指南

### 前置准备

1. **安装Python 3.8+**
   - 从[Python官网](https://www.python.org/downloads/windows/)下载安装Python 3.8或更高版本
   - 安装时勾选"Add Python to PATH"选项
   - 验证安装：打开命令提示符，输入`python --version`应显示Python版本

2. **安装依赖**
   - 本工具已包含自动安装依赖的功能，但如需手动安装，可执行：
   ```
   pip install -r requirements.txt
   ```

3. **准备问题文件**
   - 在项目根目录下创建以下文件：
     - `single_questions.txt`: 每行一个单问题
     - `multi_questions.txt`: JSON格式的多问题对话，格式为`[["问题1", "问题2", ...], [...]]`

### 运行方式

本项目提供三种运行方式：

#### 1. 测试模式

适用于首次部署或测试功能：

1. 双击`start_automation_test.bat`
2. 脚本会检查环境，安装依赖，然后以测试模式运行自动化测试
3. 测试模式仅使用少量问题，测试完成后自动退出

#### 2. 控制台模式

适用于临时运行或需要查看实时输出：

1. 双击`start_automation.bat`
2. 脚本会检查环境，安装依赖，然后启动自动化测试
3. 如果程序崩溃或完成，会自动重启
4. 关闭控制台窗口可以停止运行

#### 3. 服务模式

适用于长期稳定运行，支持开机自启动：

1. 右键点击`install_service.bat`，选择"以管理员身份运行"
2. 脚本会下载并配置NSSM工具，然后将程序安装为Windows服务
3. 服务会自动启动，并在系统启动时自动运行
4. 如需停止或删除服务，请以管理员身份运行`stop_service.bat`

### 输出文件

程序运行后会产生以下输出：

1. **logs目录**: 包含运行日志
2. **screenshots目录**: 包含测试过程中的截图
3. **results目录**: 包含测试结果记录

## 故障排除

1. **依赖安装失败**
   - 确保网络连接正常
   - 尝试手动执行`pip install -r requirements.txt`

2. **脚本启动失败**
   - 检查Python是否正确安装并添加到PATH
   - 查看logs目录下的日志文件了解详细错误

3. **浏览器启动失败**
   - 确保系统中已安装Chrome浏览器
   - 或使用`python -m playwright install chromium`安装Playwright内置浏览器

4. **服务安装失败**
   - 确保以管理员身份运行安装脚本
   - 检查服务安装日志了解详细错误

## 配置文件说明

主要配置在`tk_chat_automation.py`文件中：

- **钉钉通知配置**: 修改`dingtalk_webhook`和`dingtalk_secret`
- **OSS配置**: 修改`oss_access_key_id`等OSS相关参数
- **通知时间间隔**: 修改`notification_interval`参数（默认30分钟） 