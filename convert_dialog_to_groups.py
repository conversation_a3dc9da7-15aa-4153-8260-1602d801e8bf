#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os
import json
import re
import random
from typing import List, Dict, Any, Tuple, Optional

def split_into_dialog_groups(
    input_file: str, 
    output_file: str, 
    questions_per_group: int = 3
) -> int:
    """
    将大的问题列表分割成较小的连续对话组
    
    Args:
        input_file: 输入JSON文件路径
        output_file: 输出JSON文件路径
        questions_per_group: 每组问题数量
    
    Returns:
        生成的对话组数量
    """
    # 读取输入文件
    with open(input_file, 'r') as f:
        data = json.load(f)
    
    # 扁平化嵌套列表
    all_questions = []
    for item in data:
        if isinstance(item, list):
            all_questions.extend(item)
        else:
            all_questions.append(item)
    
    # 识别主问题的模式
    main_question_pattern = re.compile(r'^Q\d+:')
    
    # 将问题分组
    groups = []
    current_group = []
    main_questions_count = 0
    
    for question in all_questions:
        # 如果是主问题且当前组已经有足够的主问题，则开始新组
        if main_question_pattern.match(question) and main_questions_count >= questions_per_group:
            if current_group:
                groups.append(current_group)
                current_group = []
                main_questions_count = 0
        
        current_group.append(question)
        
        # 计算主问题数量
        if main_question_pattern.match(question):
            main_questions_count += 1
    
    # 添加最后一组
    if current_group:
        groups.append(current_group)
    
    # 写入输出文件
    with open(output_file, 'w') as f:
        json.dump(groups, f, indent=2, ensure_ascii=False)
    
    print(f"处理完成: 创建了 {len(groups)} 个对话组")
    return len(groups)

def create_sample_multi_dialog_json(file_path: str):
    """
    创建一个示例的多对话JSON文件
    
    Args:
        file_path: 要创建的文件路径
    """
    # 创建示例数据
    sample_data = [
        # 咖啡店组
        [
            "Q1: 请推荐杭州的一些好咖啡店",
            "A: 杭州有很多不错的咖啡店，比如Seesaw Coffee、Lavida Coffee、猫尾巴咖啡等，这些都是很受欢迎的选择。",
            "Q2: 这些咖啡店的价格大概是多少?",
            "A: Seesaw和Lavida的价格在30-50元一杯，猫尾巴咖啡价格相对亲民，大约在25-40元左右。",
            "Q3: 哪家咖啡店适合安静工作?",
            "A: Seesaw Coffee的环境比较安静，有很多人在那里办公或学习。猫尾巴咖啡的二楼也比较安静，适合工作。"
        ],
        # 自我介绍组
        [
            "Q1: 请简单介绍一下你自己",
            "A: 我是一名AI助手，可以回答问题、提供信息和帮助完成各种任务。",
            "Q2: 你能做些什么?",
            "A: 我可以回答知识性问题、帮助写作、提供建议、解释概念、进行简单计算等。",
            "Q3: 你的知识更新到什么时候?",
            "A: 我的知识库包含到2023年的信息，但我无法访问实时更新的数据。",
            "Q4: 你有什么局限性吗?",
            "A: 我不能浏览互联网、无法执行代码、不能访问或修改文件，也无法记住很长时间前的对话内容。"
        ],
        # 其他问题
        [
            "Q1: 杭州西湖有哪些景点?",
            "A: 杭州西湖的著名景点包括断桥残雪、平湖秋月、苏堤春晓、雷峰塔、三潭印月等十景，还有岳庙、灵隐寺等历史人文景观。",
            "Q2: 最佳的游览时间是什么时候?",
            "A: 春季(3-5月)和秋季(9-11月)是游览西湖的最佳时间，天气宜人，景色优美。春天可以看花，秋天则有不同的美景。",
            "Q3: 有什么特别的交通方式游览西湖吗?",
            "A: 可以乘坐西湖游船游览，也可以租自行车环湖骑行，步行则能更细致地欣赏沿途风景。"
        ]
    ]
    
    # 写入文件
    with open(file_path, 'w') as f:
        json.dump(sample_data, f, indent=2, ensure_ascii=False)
    
    print(f"已创建示例文件: {file_path}")

def main():
    # 设置输入和输出文件路径
    input_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'multi_questions.json')
    output_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'multi_questions_grouped.json')
    
    # 检查输入文件是否存在，如果不存在则创建示例文件
    if not os.path.exists(input_file):
        print(f"未找到输入文件: {input_file}")
        print("创建示例文件...")
        create_sample_multi_dialog_json(input_file)
    
    print(f"开始处理文件: {input_file}")
    result = split_into_dialog_groups(input_file, output_file, questions_per_group=3)
    print(f"结果已保存到: {output_file}")

if __name__ == "__main__":
    main() 