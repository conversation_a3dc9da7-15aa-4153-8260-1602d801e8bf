#!/usr/bin/env python3
"""
测试Playwright是否正常工作
"""

try:
    from playwright.sync_api import sync_playwright
    print("✓ Playwright导入成功")
    
    # 测试启动浏览器
    with sync_playwright() as p:
        print("✓ Playwright启动成功")
        
        # 测试设备列表
        devices = p.devices
        print(f"✓ 可用设备数量: {len(devices)}")
        
        # 检查iPhone 13 Pro是否可用
        if 'iPhone 13 Pro' in devices:
            print("✓ iPhone 13 Pro设备配置可用")
            device = devices['iPhone 13 Pro']
            print(f"  - 视口: {device.get('viewport', {})}")
            print(f"  - User-Agent: {device.get('user_agent', 'N/A')[:50]}...")
        else:
            print("✗ iPhone 13 Pro设备配置不可用")
        
        print("✓ 所有测试通过")
        
except ImportError as e:
    print(f"✗ Playwright未安装: {e}")
    print("请运行: pip install playwright")
except Exception as e:
    print(f"✗ 测试失败: {e}")
