"""
配置管理模块
负责统一管理所有模块的配置信息
"""

import os
import json
import logging
from typing import Dict, Any, Optional


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.json"):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.config = {}
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                logging.info(f"已加载配置文件: {self.config_file}")
            else:
                logging.warning(f"配置文件不存在: {self.config_file}，使用默认配置")
                self.config = self._get_default_config()
                self.save_config()
        except Exception as e:
            logging.error(f"加载配置文件失败: {str(e)}")
            self.config = self._get_default_config()
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            logging.info(f"配置文件已保存: {self.config_file}")
        except Exception as e:
            logging.error(f"保存配置文件失败: {str(e)}")
    
    def get(self, key_path: str, default=None):
        """
        获取配置值
        
        Args:
            key_path: 配置键路径，使用.分隔，如 'ai_config.generation.enabled'
            default: 默认值
            
        Returns:
            配置值
        """
        try:
            keys = key_path.split('.')
            value = self.config
            
            for key in keys:
                if isinstance(value, dict) and key in value:
                    value = value[key]
                else:
                    return default
            
            # 处理环境变量替换
            if isinstance(value, str) and value.startswith('${') and value.endswith('}'):
                env_var = value[2:-1]
                return os.environ.get(env_var, default)
            
            return value
            
        except Exception as e:
            logging.error(f"获取配置值失败: {key_path}, 错误: {str(e)}")
            return default
    
    def set(self, key_path: str, value: Any):
        """
        设置配置值
        
        Args:
            key_path: 配置键路径
            value: 配置值
        """
        try:
            keys = key_path.split('.')
            config = self.config
            
            # 导航到目标位置
            for key in keys[:-1]:
                if key not in config:
                    config[key] = {}
                config = config[key]
            
            # 设置最终值
            config[keys[-1]] = value
            
        except Exception as e:
            logging.error(f"设置配置值失败: {key_path}, 错误: {str(e)}")
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """
        获取配置节
        
        Args:
            section: 节名称
            
        Returns:
            配置节字典
        """
        return self.config.get(section, {})
    
    def get_application_config(self) -> Dict[str, Any]:
        """获取应用程序配置"""
        return self.get_section('application')
    
    def get_browser_config(self) -> Dict[str, Any]:
        """获取浏览器配置"""
        return self.get_section('browser')
    
    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return self.get_section('logging')
    
    def get_question_config(self) -> Dict[str, Any]:
        """获取问题配置"""
        return self.get_section('question_config')
    
    def get_ai_config(self) -> Dict[str, Any]:
        """获取AI配置"""
        return self.get_section('ai_config')
    
    def get_storage_config(self) -> Dict[str, Any]:
        """获取存储配置"""
        return self.get_section('storage')
    
    def get_notification_config(self) -> Dict[str, Any]:
        """获取通知配置"""
        return self.get_section('notification')
    
    def get_image_config(self) -> Dict[str, Any]:
        """获取图片处理配置"""
        return self.get_section('image_processing')
    
    def get_reporting_config(self) -> Dict[str, Any]:
        """获取报告配置"""
        return self.get_section('reporting')
    
    def get_performance_config(self) -> Dict[str, Any]:
        """获取性能配置"""
        return self.get_section('performance')
    
    def get_debug_config(self) -> Dict[str, Any]:
        """获取调试配置"""
        return self.get_section('debug')
    
    def is_test_mode(self) -> bool:
        """是否为测试模式"""
        return self.get('application.test_mode', False)
    
    def get_conversation_count(self) -> int:
        """获取对话轮数"""
        if self.is_test_mode():
            return self.get('application.test_conversation_count', 5)
        else:
            return self.get('application.conversation_count', 10)
    
    def get_target_url(self) -> str:
        """获取目标URL"""
        return self.get('application.target_url', 'https://chatbot.tika.style/')
    
    def setup_environment_variables(self):
        """设置环境变量"""
        storage_config = self.get_storage_config()
        oss_config = storage_config.get('oss', {})
        
        # 设置OSS相关环境变量（如果配置中有值）
        env_mappings = {
            'OSS_ACCESS_KEY_ID': oss_config.get('access_key_id'),
            'OSS_ACCESS_KEY_SECRET': oss_config.get('access_key_secret'),
            'OSS_BUCKET_NAME': oss_config.get('bucket_name'),
            'OSS_ENDPOINT': oss_config.get('endpoint')
        }
        
        for env_key, config_value in env_mappings.items():
            if config_value and not config_value.startswith('${'):
                os.environ[env_key] = config_value
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "application": {
                "name": "TK Chat Automation",
                "version": "2.0.0",
                "target_url": "https://chatbot.tika.style/",
                "test_mode": False,
                "conversation_count": 10,
                "test_conversation_count": 5
            },
            "browser": {
                "headless": False,
                "viewport": {"width": 1280, "height": 720},
                "timeout": 30000,
                "keep_alive_interval": 600
            },
            "logging": {
                "level": "INFO",
                "file_enabled": True,
                "console_enabled": True,
                "max_bytes": 10485760,
                "backup_count": 5,
                "log_directory": "logs"
            },
            "question_config": {
                "default_file": "questions.json",
                "selection_method": "random",
                "question_count_per_conversation": 5,
                "formats_supported": ["json", "csv", "txt"]
            },
            "ai_config": {
                "generation": {
                    "enabled": True,
                    "default_provider": "ollama"
                },
                "evaluation": {
                    "enabled": False,
                    "default_provider": "ollama_eval"
                }
            },
            "storage": {
                "oss": {"enabled": False},
                "local": {
                    "screenshots_dir": "screenshots",
                    "results_dir": "results",
                    "logs_dir": "logs"
                }
            },
            "notification": {
                "enabled_services": [],
                "dingtalk": {"enabled": False},
                "email": {"enabled": False}
            },
            "image_processing": {
                "download_enabled": True,
                "validation_enabled": True
            },
            "reporting": {
                "formats": ["html"],
                "auto_upload": False
            },
            "performance": {
                "monitoring_enabled": True
            },
            "debug": {
                "enabled": False
            }
        }


def load_legacy_ai_config(ai_config_file: str = "ai_config.json") -> Dict[str, Any]:
    """
    加载旧版AI配置文件并转换为新格式
    
    Args:
        ai_config_file: AI配置文件路径
        
    Returns:
        转换后的AI配置
    """
    try:
        if not os.path.exists(ai_config_file):
            return {}
        
        with open(ai_config_file, 'r', encoding='utf-8') as f:
            legacy_config = json.load(f)
        
        # 转换为新格式
        new_config = {
            "generation": {
                "enabled": True,
                "default_provider": legacy_config.get("ai_generation_config", {}).get("default_provider", "ollama"),
                "providers": legacy_config.get("ai_generation_config", {}).get("providers", {}),
                "generation_settings": legacy_config.get("generation_settings", {})
            },
            "evaluation": {
                "enabled": legacy_config.get("ai_evaluation_config", {}).get("enabled", False),
                "default_provider": legacy_config.get("ai_evaluation_config", {}).get("default_provider", "ollama_eval"),
                "providers": legacy_config.get("ai_evaluation_config", {}).get("providers", {}),
                "evaluation_settings": legacy_config.get("evaluation_settings", {})
            }
        }
        
        logging.info(f"已转换旧版AI配置: {ai_config_file}")
        return new_config
        
    except Exception as e:
        logging.error(f"加载旧版AI配置失败: {str(e)}")
        return {}


def migrate_from_legacy_config():
    """从旧版配置迁移到新版配置"""
    try:
        config_manager = ConfigManager()
        
        # 如果新配置文件不存在，尝试从旧配置迁移
        if not os.path.exists("config.json"):
            logging.info("开始从旧版配置迁移...")
            
            # 加载旧版AI配置
            legacy_ai_config = load_legacy_ai_config()
            if legacy_ai_config:
                config_manager.config["ai_config"] = legacy_ai_config
            
            # 保存新配置
            config_manager.save_config()
            logging.info("配置迁移完成")
        
        return config_manager
        
    except Exception as e:
        logging.error(f"配置迁移失败: {str(e)}")
        return ConfigManager()


# 全局配置管理器实例
_config_manager = None


def get_config_manager() -> ConfigManager:
    """获取全局配置管理器实例"""
    global _config_manager
    if _config_manager is None:
        _config_manager = migrate_from_legacy_config()
    return _config_manager


def get_config(key_path: str, default=None):
    """获取配置值的便捷函数"""
    return get_config_manager().get(key_path, default)


def setup_environment():
    """设置环境变量的便捷函数"""
    get_config_manager().setup_environment_variables() 