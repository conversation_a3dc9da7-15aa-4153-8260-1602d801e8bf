"""
存储服务模块
负责OSS文件上传、目录管理等功能
"""

import os
import logging
import hashlib
import time
from datetime import datetime


def upload_image_to_oss(image_path, max_retries=3):
    """
    上传图片到OSS存储，带重试机制
    
    Args:
        image_path: 图片文件路径
        max_retries: 最大重试次数
        
    Returns:
        str: 上传成功的URL，失败时返回None
    """
    try:
        import oss2
        
        # OSS配置（从环境变量获取）
        access_key_id = os.environ.get('OSS_ACCESS_KEY_ID')
        access_key_secret = os.environ.get('OSS_ACCESS_KEY_SECRET')
        bucket_name = os.environ.get('OSS_BUCKET_NAME')
        endpoint = os.environ.get('OSS_ENDPOINT')
        
        if not all([access_key_id, access_key_secret, bucket_name, endpoint]):
            logging.warning("OSS配置不完整，跳过上传")
            return None
        
        # 创建OSS客户端
        auth = oss2.Auth(access_key_id, access_key_secret)
        bucket = oss2.Bucket(auth, endpoint, bucket_name)
        
        # 生成唯一的文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_hash = hashlib.md5(open(image_path, 'rb').read()).hexdigest()[:8]
        _, ext = os.path.splitext(image_path)
        
        # OSS对象键
        object_key = f"screenshots/{timestamp}_{file_hash}{ext}"
        
        for attempt in range(max_retries):
            try:
                logging.info(f"开始上传图片到OSS (尝试 {attempt + 1}/{max_retries}): {image_path}")
                
                # 上传文件
                with open(image_path, 'rb') as f:
                    result = bucket.put_object(object_key, f)
                
                if result.status == 200:
                    # 构建访问URL
                    url = f"https://{bucket_name}.{endpoint.replace('https://', '').replace('http://', '')}/{object_key}"
                    logging.info(f"图片上传成功: {url}")
                    return url
                else:
                    logging.warning(f"上传返回状态码: {result.status}")
                    
            except Exception as e:
                logging.error(f"上传尝试 {attempt + 1} 失败: {str(e)}")
                if attempt == max_retries - 1:
                    raise
                time.sleep(2 ** attempt)  # 指数退避
        
        return None
        
    except ImportError:
        logging.warning("未安装oss2库，无法上传到OSS")
        return None
    except Exception as e:
        logging.error(f"上传图片到OSS时出错: {str(e)}")
        return None


def ensure_oss_dir(bucket, dir_path):
    """
    确保OSS中的目录存在（通过创建空对象模拟目录）
    
    Args:
        bucket: OSS bucket对象
        dir_path: 目录路径
    """
    try:
        if not dir_path.endswith('/'):
            dir_path += '/'
        
        # 检查目录是否存在
        if not bucket.object_exists(dir_path):
            # 创建空对象来模拟目录
            bucket.put_object(dir_path, b'')
            logging.info(f"已创建OSS目录: {dir_path}")
        
    except Exception as e:
        logging.warning(f"创建OSS目录失败: {dir_path}, 错误: {str(e)}")


def upload_text_to_oss(local_file, oss_filename=None):
    """
    上传文本文件到OSS
    
    Args:
        local_file: 本地文件路径
        oss_filename: OSS中的文件名，None时自动生成
        
    Returns:
        str: 上传后的URL，失败时返回None
    """
    try:
        import oss2
        
        # OSS配置
        access_key_id = os.environ.get('OSS_ACCESS_KEY_ID')
        access_key_secret = os.environ.get('OSS_ACCESS_KEY_SECRET')
        bucket_name = os.environ.get('OSS_BUCKET_NAME')
        endpoint = os.environ.get('OSS_ENDPOINT')
        
        if not all([access_key_id, access_key_secret, bucket_name, endpoint]):
            logging.warning("OSS配置不完整，跳过上传")
            return None
        
        # 创建OSS客户端
        auth = oss2.Auth(access_key_id, access_key_secret)
        bucket = oss2.Bucket(auth, endpoint, bucket_name)
        
        # 生成文件名
        if not oss_filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            basename = os.path.basename(local_file)
            oss_filename = f"logs/{timestamp}_{basename}"
        
        # 上传文件
        logging.info(f"开始上传文本文件到OSS: {local_file} -> {oss_filename}")
        result = bucket.put_object_from_file(oss_filename, local_file)
        
        if result.status == 200:
            url = f"https://{bucket_name}.{endpoint.replace('https://', '').replace('http://', '')}/{oss_filename}"
            logging.info(f"文本文件上传成功: {url}")
            return url
        else:
            logging.error(f"文本文件上传失败，状态码: {result.status}")
            return None
            
    except ImportError:
        logging.warning("未安装oss2库，无法上传到OSS")
        return None
    except Exception as e:
        logging.error(f"上传文本文件到OSS时出错: {str(e)}")
        return None


def upload_binary_to_oss(content, oss_path, extension=None):
    """
    上传二进制内容到OSS
    
    Args:
        content: 二进制内容
        oss_path: OSS路径
        extension: 文件扩展名
        
    Returns:
        str: 上传后的URL，失败时返回None
    """
    try:
        import oss2
        
        # OSS配置
        access_key_id = os.environ.get('OSS_ACCESS_KEY_ID')
        access_key_secret = os.environ.get('OSS_ACCESS_KEY_SECRET')
        bucket_name = os.environ.get('OSS_BUCKET_NAME')
        endpoint = os.environ.get('OSS_ENDPOINT')
        
        if not all([access_key_id, access_key_secret, bucket_name, endpoint]):
            logging.warning("OSS配置不完整，跳过上传")
            return None
        
        # 创建OSS客户端
        auth = oss2.Auth(access_key_id, access_key_secret)
        bucket = oss2.Bucket(auth, endpoint, bucket_name)
        
        # 添加扩展名
        if extension and not oss_path.endswith(extension):
            oss_path += extension
        
        # 上传内容
        logging.info(f"开始上传二进制内容到OSS: {oss_path}")
        result = bucket.put_object(oss_path, content)
        
        if result.status == 200:
            url = f"https://{bucket_name}.{endpoint.replace('https://', '').replace('http://', '')}/{oss_path}"
            logging.info(f"二进制内容上传成功: {url}")
            return url
        else:
            logging.error(f"二进制内容上传失败，状态码: {result.status}")
            return None
            
    except ImportError:
        logging.warning("未安装oss2库，无法上传到OSS")
        return None
    except Exception as e:
        logging.error(f"上传二进制内容到OSS时出错: {str(e)}")
        return None


def list_oss_objects(prefix="", max_keys=100):
    """
    列出OSS中的对象
    
    Args:
        prefix: 对象前缀
        max_keys: 最大返回数量
        
    Returns:
        list: 对象列表
    """
    try:
        import oss2
        
        # OSS配置
        access_key_id = os.environ.get('OSS_ACCESS_KEY_ID')
        access_key_secret = os.environ.get('OSS_ACCESS_KEY_SECRET')
        bucket_name = os.environ.get('OSS_BUCKET_NAME')
        endpoint = os.environ.get('OSS_ENDPOINT')
        
        if not all([access_key_id, access_key_secret, bucket_name, endpoint]):
            logging.warning("OSS配置不完整")
            return []
        
        # 创建OSS客户端
        auth = oss2.Auth(access_key_id, access_key_secret)
        bucket = oss2.Bucket(auth, endpoint, bucket_name)
        
        # 列出对象
        objects = []
        for obj in oss2.ObjectIterator(bucket, prefix=prefix, max_keys=max_keys):
            objects.append({
                'key': obj.key,
                'size': obj.size,
                'last_modified': obj.last_modified,
                'etag': obj.etag
            })
        
        logging.info(f"找到 {len(objects)} 个OSS对象，前缀: {prefix}")
        return objects
        
    except ImportError:
        logging.warning("未安装oss2库，无法列出OSS对象")
        return []
    except Exception as e:
        logging.error(f"列出OSS对象时出错: {str(e)}")
        return []


def delete_oss_object(object_key):
    """
    删除OSS对象
    
    Args:
        object_key: 对象键
        
    Returns:
        bool: 删除是否成功
    """
    try:
        import oss2
        
        # OSS配置
        access_key_id = os.environ.get('OSS_ACCESS_KEY_ID')
        access_key_secret = os.environ.get('OSS_ACCESS_KEY_SECRET')
        bucket_name = os.environ.get('OSS_BUCKET_NAME')
        endpoint = os.environ.get('OSS_ENDPOINT')
        
        if not all([access_key_id, access_key_secret, bucket_name, endpoint]):
            logging.warning("OSS配置不完整")
            return False
        
        # 创建OSS客户端
        auth = oss2.Auth(access_key_id, access_key_secret)
        bucket = oss2.Bucket(auth, endpoint, bucket_name)
        
        # 删除对象
        result = bucket.delete_object(object_key)
        
        if result.status == 204:
            logging.info(f"OSS对象删除成功: {object_key}")
            return True
        else:
            logging.error(f"OSS对象删除失败，状态码: {result.status}")
            return False
            
    except ImportError:
        logging.warning("未安装oss2库，无法删除OSS对象")
        return False
    except Exception as e:
        logging.error(f"删除OSS对象时出错: {str(e)}")
        return False


def download_from_oss(object_key, local_path):
    """
    从OSS下载文件
    
    Args:
        object_key: OSS对象键
        local_path: 本地保存路径
        
    Returns:
        bool: 下载是否成功
    """
    try:
        import oss2
        
        # OSS配置
        access_key_id = os.environ.get('OSS_ACCESS_KEY_ID')
        access_key_secret = os.environ.get('OSS_ACCESS_KEY_SECRET')
        bucket_name = os.environ.get('OSS_BUCKET_NAME')
        endpoint = os.environ.get('OSS_ENDPOINT')
        
        if not all([access_key_id, access_key_secret, bucket_name, endpoint]):
            logging.warning("OSS配置不完整")
            return False
        
        # 创建OSS客户端
        auth = oss2.Auth(access_key_id, access_key_secret)
        bucket = oss2.Bucket(auth, endpoint, bucket_name)
        
        # 创建本地目录
        os.makedirs(os.path.dirname(local_path), exist_ok=True)
        
        # 下载文件
        logging.info(f"从OSS下载文件: {object_key} -> {local_path}")
        result = bucket.get_object_to_file(object_key, local_path)
        
        if result.status == 200:
            logging.info(f"文件下载成功: {local_path}")
            return True
        else:
            logging.error(f"文件下载失败，状态码: {result.status}")
            return False
            
    except ImportError:
        logging.warning("未安装oss2库，无法从OSS下载")
        return False
    except Exception as e:
        logging.error(f"从OSS下载文件时出错: {str(e)}")
        return False


def get_oss_object_url(object_key, expires=3600):
    """
    获取OSS对象的签名URL
    
    Args:
        object_key: 对象键
        expires: 过期时间（秒）
        
    Returns:
        str: 签名URL，失败时返回None
    """
    try:
        import oss2
        
        # OSS配置
        access_key_id = os.environ.get('OSS_ACCESS_KEY_ID')
        access_key_secret = os.environ.get('OSS_ACCESS_KEY_SECRET')
        bucket_name = os.environ.get('OSS_BUCKET_NAME')
        endpoint = os.environ.get('OSS_ENDPOINT')
        
        if not all([access_key_id, access_key_secret, bucket_name, endpoint]):
            logging.warning("OSS配置不完整")
            return None
        
        # 创建OSS客户端
        auth = oss2.Auth(access_key_id, access_key_secret)
        bucket = oss2.Bucket(auth, endpoint, bucket_name)
        
        # 生成签名URL
        url = bucket.sign_url('GET', object_key, expires)
        logging.info(f"生成OSS签名URL: {object_key}")
        return url
        
    except ImportError:
        logging.warning("未安装oss2库，无法生成签名URL")
        return None
    except Exception as e:
        logging.error(f"生成OSS签名URL时出错: {str(e)}")
        return None


def cleanup_old_oss_objects(prefix="", days_old=30):
    """
    清理OSS中的旧文件
    
    Args:
        prefix: 对象前缀
        days_old: 保留天数
        
    Returns:
        int: 删除的文件数量
    """
    try:
        import oss2
        from datetime import datetime, timedelta
        
        # OSS配置
        access_key_id = os.environ.get('OSS_ACCESS_KEY_ID')
        access_key_secret = os.environ.get('OSS_ACCESS_KEY_SECRET')
        bucket_name = os.environ.get('OSS_BUCKET_NAME')
        endpoint = os.environ.get('OSS_ENDPOINT')
        
        if not all([access_key_id, access_key_secret, bucket_name, endpoint]):
            logging.warning("OSS配置不完整")
            return 0
        
        # 创建OSS客户端
        auth = oss2.Auth(access_key_id, access_key_secret)
        bucket = oss2.Bucket(auth, endpoint, bucket_name)
        
        # 计算截止时间
        cutoff_time = datetime.now() - timedelta(days=days_old)
        
        # 查找和删除旧文件
        deleted_count = 0
        for obj in oss2.ObjectIterator(bucket, prefix=prefix):
            # 将OSS时间转换为datetime对象进行比较
            if obj.last_modified < cutoff_time:
                try:
                    bucket.delete_object(obj.key)
                    deleted_count += 1
                    logging.debug(f"删除旧OSS对象: {obj.key}")
                except Exception as e:
                    logging.warning(f"删除OSS对象失败: {obj.key}, 错误: {str(e)}")
        
        if deleted_count > 0:
            logging.info(f"清理完成，删除了 {deleted_count} 个旧OSS对象")
        
        return deleted_count
        
    except ImportError:
        logging.warning("未安装oss2库，无法清理OSS对象")
        return 0
    except Exception as e:
        logging.error(f"清理OSS旧文件时出错: {str(e)}")
        return 0 